/**
 * Core type definitions for the Markdown Editor application
 */

export interface MarkdownFile {
  id: string;
  name: string;
  content: string;
  path: string;
  lastModified: Date;
  size: number;
  isDirectory: boolean;
  parentId?: string;
}

export interface EditorSettings {
  theme: 'light' | 'dark';
  fontSize: number;
  lineNumbers: boolean;
  wordWrap: boolean;
  previewMode: 'side' | 'preview' | 'edit';
  autoSave: boolean;
  autoSaveInterval: number; // in milliseconds
}

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'txt' | 'html';
  includeMetadata: boolean;
  pageSize?: 'A4' | 'Letter' | 'Legal';
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface FileManagerState {
  currentDirectory: string;
  selectedFiles: string[];
  files: MarkdownFile[];
  isLoading: boolean;
  error: string | null;
}

export interface EditorState {
  currentFile: MarkdownFile | null;
  content: string;
  isModified: boolean;
  isLoading: boolean;
  error: string | null;
  cursorPosition: {
    line: number;
    column: number;
  };
}

export interface PreviewState {
  htmlContent: string;
  isLoading: boolean;
  error: string | null;
  scrollPosition: number;
}

export interface CloudProvider {
  id: string;
  name: string;
  isConnected: boolean;
  accessToken?: string;
}

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  isOnline: boolean;
  cursorPosition?: {
    line: number;
    column: number;
  };
}

export interface CollaborationState {
  isEnabled: boolean;
  users: CollaborationUser[];
  roomId: string | null;
  isConnected: boolean;
}

export interface AppState {
  editor: EditorState;
  preview: PreviewState;
  fileManager: FileManagerState;
  settings: EditorSettings;
  collaboration: CollaborationState;
  cloudProviders: CloudProvider[];
}

// Action types for state management
export type EditorAction =
  | { type: 'SET_CONTENT'; payload: string }
  | { type: 'SET_CURRENT_FILE'; payload: MarkdownFile | null }
  | { type: 'SET_MODIFIED'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CURSOR_POSITION'; payload: { line: number; column: number } };

export type FileManagerAction =
  | { type: 'SET_FILES'; payload: MarkdownFile[] }
  | { type: 'ADD_FILE'; payload: MarkdownFile }
  | { type: 'UPDATE_FILE'; payload: MarkdownFile }
  | { type: 'DELETE_FILE'; payload: string }
  | { type: 'SET_CURRENT_DIRECTORY'; payload: string }
  | { type: 'SET_SELECTED_FILES'; payload: string[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

export type PreviewAction =
  | { type: 'SET_HTML_CONTENT'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SCROLL_POSITION'; payload: number };

export type SettingsAction =
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'SET_FONT_SIZE'; payload: number }
  | { type: 'SET_LINE_NUMBERS'; payload: boolean }
  | { type: 'SET_WORD_WRAP'; payload: boolean }
  | { type: 'SET_PREVIEW_MODE'; payload: 'side' | 'preview' | 'edit' }
  | { type: 'SET_AUTO_SAVE'; payload: boolean }
  | { type: 'SET_AUTO_SAVE_INTERVAL'; payload: number };

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Event types
export interface FileEvent {
  type: 'create' | 'update' | 'delete' | 'rename';
  file: MarkdownFile;
  timestamp: Date;
}

export interface EditorEvent {
  type: 'content-change' | 'cursor-move' | 'selection-change';
  data: any;
  timestamp: Date;
}

// Plugin system types
export interface EditorPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  isEnabled: boolean;
  initialize: (editor: any) => void;
  destroy: () => void;
}

// Error types
export class EditorError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'EditorError';
  }
}

export class FileSystemError extends EditorError {
  constructor(message: string, public operation: string, details?: any) {
    super(message, 'FILESYSTEM_ERROR', details);
    this.name = 'FileSystemError';
  }
}

export class ExportError extends EditorError {
  constructor(message: string, public format: string, details?: any) {
    super(message, 'EXPORT_ERROR', details);
    this.name = 'ExportError';
  }
}
