/**
 * Header component with toolbar and navigation
 */

'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { exportMarkdown } from '@/utils/export';
import { readFileFromInput } from '@/utils/file';
import { ExportOptions } from '@/types';

interface HeaderProps {
  onSidebarToggle: () => void;
}

export function Header({ onSidebarToggle }: HeaderProps) {
  const { state, saveCurrentFile, createNewFile, updateSettings } = useApp();
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleNewFile = () => {
    const fileName = prompt('Enter file name:');
    if (fileName) {
      createNewFile(fileName);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      try {
        const content = await readFileFromInput(file);
        createNewFile(file.name, content);
      } catch (error) {
        console.error('Failed to upload file:', error);
        alert(`Failed to upload ${file.name}: ${error}`);
      }
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSave = () => {
    if (state.editor.currentFile) {
      saveCurrentFile();
    }
  };

  const handleExport = async (format: 'pdf' | 'docx' | 'txt' | 'html') => {
    if (!state.editor.currentFile || !state.editor.content) {
      alert('No file to export');
      return;
    }

    setIsExporting(true);
    setShowExportMenu(false);

    try {
      const options: ExportOptions = {
        format,
        includeMetadata: true,
        pageSize: 'A4',
        margins: { top: 20, right: 20, bottom: 20, left: 20 }
      };

      await exportMarkdown(
        state.editor.content,
        state.editor.currentFile.name,
        options
      );
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleThemeToggle = () => {
    updateSettings({
      theme: state.settings.theme === 'light' ? 'dark' : 'light'
    });
  };

  const handlePreviewModeChange = (mode: 'side' | 'preview' | 'edit') => {
    updateSettings({ previewMode: mode });
  };

  return (
    <header className="h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 no-print">
      {/* Left section */}
      <div className="flex items-center space-x-4">
        {/* Sidebar toggle */}
        <button
          onClick={onSidebarToggle}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="Toggle sidebar"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <span className="font-semibold text-gray-900 dark:text-white">Markdown Editor</span>
        </div>
      </div>

      {/* Center section - File actions */}
      <div className="flex-1 flex items-center justify-center space-x-2">
        <button
          onClick={handleNewFile}
          className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          title="New file (Ctrl+N)"
        >
          New
        </button>

        <button
          onClick={handleUploadClick}
          className="px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          title="Upload files"
        >
          Upload
        </button>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".md,.markdown,.mdown,.mkd,.mdx,.txt"
          onChange={handleFileUpload}
          className="hidden"
        />

        <button
          onClick={handleSave}
          disabled={!state.editor.isModified}
          className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          title="Save file (Ctrl+S)"
        >
          Save
        </button>

        {/* Export dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowExportMenu(!showExportMenu)}
            disabled={!state.editor.currentFile || isExporting}
            className="px-3 py-1.5 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
            title="Export file"
          >
            {isExporting ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )}
            <span>Export</span>
          </button>

          {showExportMenu && (
            <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
              <div className="py-1">
                <button
                  onClick={() => handleExport('txt')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Plain Text (.txt)
                </button>
                <button
                  onClick={() => handleExport('html')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  HTML (.html)
                </button>
                <button
                  onClick={() => handleExport('pdf')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  PDF (.pdf)
                </button>
                <button
                  onClick={() => handleExport('docx')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Word Document (.docx)
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right section */}
      <div className="flex items-center space-x-2">
        {/* Preview mode toggle */}
        <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-md p-1">
          <button
            onClick={() => handlePreviewModeChange('edit')}
            className={`px-2 py-1 text-xs rounded ${
              state.settings.previewMode === 'edit'
                ? 'bg-white dark:bg-gray-600 shadow-sm'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600'
            } transition-colors`}
            title="Editor only"
          >
            Edit
          </button>
          <button
            onClick={() => handlePreviewModeChange('side')}
            className={`px-2 py-1 text-xs rounded ${
              state.settings.previewMode === 'side'
                ? 'bg-white dark:bg-gray-600 shadow-sm'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600'
            } transition-colors`}
            title="Side by side"
          >
            Split
          </button>
          <button
            onClick={() => handlePreviewModeChange('preview')}
            className={`px-2 py-1 text-xs rounded ${
              state.settings.previewMode === 'preview'
                ? 'bg-white dark:bg-gray-600 shadow-sm'
                : 'hover:bg-gray-200 dark:hover:bg-gray-600'
            } transition-colors`}
            title="Preview only"
          >
            Preview
          </button>
        </div>

        {/* Theme toggle */}
        <button
          onClick={handleThemeToggle}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="Toggle theme"
        >
          {state.settings.theme === 'light' ? (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          )}
        </button>

        {/* Current file indicator */}
        {state.editor.currentFile && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <span>{state.editor.currentFile.name}</span>
            {state.editor.isModified && (
              <div className="w-2 h-2 bg-orange-500 rounded-full" title="Unsaved changes"></div>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close export menu */}
      {showExportMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowExportMenu(false)}
        ></div>
      )}
    </header>
  );
}
