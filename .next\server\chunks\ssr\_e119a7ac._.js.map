{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/contexts/AppContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx <module evaluation>\",\n    \"AppProvider\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx <module evaluation>\",\n    \"useApp\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,6DACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/contexts/AppContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppProvider() from the server but AppProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx\",\n    \"AppProvider\",\n);\nexport const useApp = registerClientReference(\n    function() { throw new Error(\"Attempted to call useApp() from the server but useApp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AppContext.tsx\",\n    \"useApp\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA;AAEG,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,yCACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport \"./globals.css\";\nimport { AppProvider } from \"@/contexts/AppContext\";\n\nexport const metadata: Metadata = {\n  title: \"Markdown Editor - Professional Markdown Editing\",\n  description: \"A powerful, feature-rich markdown editor with real-time preview, file management, and export capabilities.\",\n  keywords: [\"markdown\", \"editor\", \"preview\", \"export\", \"writing\"],\n  authors: [{ name: \"Markdown Editor Team\" }],\n  viewport: \"width=device-width, initial-scale=1\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className=\"antialiased\">\n        <AppProvider>\n          {children}\n        </AppProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAY;QAAU;QAAW;QAAU;KAAU;IAChE,SAAS;QAAC;YAAE,MAAM;QAAuB;KAAE;IAC3C,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,8HAAA,CAAA,cAAW;0BACT;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}