module.exports = {

"[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_8669608d._.js",
  "server/chunks/ssr/node_modules_ccb71a4f._.js",
  "server/chunks/ssr/[externals]_module_74f3e68c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/docx/dist/index.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_docx_dist_index_mjs_d4acdabc._.js",
  "server/chunks/ssr/[externals]_buffer_37e36579._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/docx/dist/index.mjs [app-ssr] (ecmascript)");
    });
});
}}),

};