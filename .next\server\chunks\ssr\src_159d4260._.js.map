{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/utils/export.ts"], "sourcesContent": ["/**\n * Export utilities for different file formats\n */\n\nimport { ExportOptions, ExportError } from '@/types';\nimport { markdownToHtml } from './markdown';\nimport { downloadFile } from './file';\n\n/**\n * Export markdown as plain text\n */\nexport async function exportAsText(\n  markdown: string,\n  filename: string\n): Promise<void> {\n  try {\n    const textFilename = filename.replace(/\\.[^/.]+$/, '.txt');\n    downloadFile(markdown, textFilename, 'text/plain');\n  } catch (error) {\n    throw new ExportError('Failed to export as text', 'txt', error);\n  }\n}\n\n/**\n * Export markdown as HTML\n */\nexport async function exportAsHtml(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    const htmlContent = markdownToHtml(markdown);\n    \n    const fullHtml = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${filename}</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            line-height: 1.6;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 2rem;\n            color: #333;\n        }\n        \n        h1, h2, h3, h4, h5, h6 {\n            margin-top: 2rem;\n            margin-bottom: 1rem;\n            font-weight: 600;\n        }\n        \n        h1 { font-size: 2.5rem; }\n        h2 { font-size: 2rem; }\n        h3 { font-size: 1.5rem; }\n        h4 { font-size: 1.25rem; }\n        h5 { font-size: 1.125rem; }\n        h6 { font-size: 1rem; }\n        \n        p {\n            margin-bottom: 1rem;\n        }\n        \n        code {\n            background-color: #f5f5f5;\n            padding: 0.2rem 0.4rem;\n            border-radius: 3px;\n            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n        }\n        \n        pre {\n            background-color: #f5f5f5;\n            padding: 1rem;\n            border-radius: 5px;\n            overflow-x: auto;\n        }\n        \n        pre code {\n            background: none;\n            padding: 0;\n        }\n        \n        blockquote {\n            border-left: 4px solid #ddd;\n            margin: 1rem 0;\n            padding-left: 1rem;\n            color: #666;\n        }\n        \n        table {\n            border-collapse: collapse;\n            width: 100%;\n            margin: 1rem 0;\n        }\n        \n        th, td {\n            border: 1px solid #ddd;\n            padding: 0.5rem;\n            text-align: left;\n        }\n        \n        th {\n            background-color: #f5f5f5;\n            font-weight: 600;\n        }\n        \n        img {\n            max-width: 100%;\n            height: auto;\n        }\n        \n        a {\n            color: #0066cc;\n            text-decoration: none;\n        }\n        \n        a:hover {\n            text-decoration: underline;\n        }\n        \n        ul, ol {\n            margin: 1rem 0;\n            padding-left: 2rem;\n        }\n        \n        li {\n            margin-bottom: 0.5rem;\n        }\n        \n        hr {\n            border: none;\n            border-top: 1px solid #ddd;\n            margin: 2rem 0;\n        }\n        \n        .anchor-link {\n            opacity: 0;\n            margin-left: 0.5rem;\n            text-decoration: none;\n        }\n        \n        h1:hover .anchor-link,\n        h2:hover .anchor-link,\n        h3:hover .anchor-link,\n        h4:hover .anchor-link,\n        h5:hover .anchor-link,\n        h6:hover .anchor-link {\n            opacity: 1;\n        }\n        \n        @media print {\n            body {\n                margin: 0;\n                padding: 1rem;\n            }\n            \n            .anchor-link {\n                display: none;\n            }\n        }\n    </style>\n</head>\n<body>\n    ${htmlContent}\n</body>\n</html>`;\n    \n    const htmlFilename = filename.replace(/\\.[^/.]+$/, '.html');\n    downloadFile(fullHtml, htmlFilename, 'text/html');\n  } catch (error) {\n    throw new ExportError('Failed to export as HTML', 'html', error);\n  }\n}\n\n/**\n * Export markdown as PDF (lazy loaded)\n */\nexport async function exportAsPdf(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    // Lazy load jsPDF\n    const { jsPDF } = await import('jspdf');\n    \n    const htmlContent = markdownToHtml(markdown);\n    \n    // Create a temporary div to render HTML\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = htmlContent;\n    tempDiv.style.position = 'absolute';\n    tempDiv.style.left = '-9999px';\n    tempDiv.style.width = '800px';\n    tempDiv.style.fontFamily = 'Arial, sans-serif';\n    tempDiv.style.fontSize = '12px';\n    tempDiv.style.lineHeight = '1.6';\n    \n    document.body.appendChild(tempDiv);\n    \n    try {\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: options.pageSize || 'a4'\n      });\n      \n      // Set margins\n      const margins = options.margins || { top: 20, right: 20, bottom: 20, left: 20 };\n      \n      // Add title if available\n      const title = filename.replace(/\\.[^/.]+$/, '');\n      pdf.setFontSize(16);\n      pdf.text(title, margins.left, margins.top);\n      \n      // Convert HTML to text and add to PDF\n      const textContent = tempDiv.textContent || tempDiv.innerText || '';\n      const lines = pdf.splitTextToSize(textContent, 210 - margins.left - margins.right);\n      \n      pdf.setFontSize(12);\n      let yPosition = margins.top + 15;\n      \n      lines.forEach((line: string) => {\n        if (yPosition > 297 - margins.bottom) {\n          pdf.addPage();\n          yPosition = margins.top;\n        }\n        pdf.text(line, margins.left, yPosition);\n        yPosition += 6;\n      });\n      \n      // Add metadata if requested\n      if (options.includeMetadata) {\n        pdf.setProperties({\n          title: title,\n          creator: 'Markdown Editor',\n          creationDate: new Date()\n        });\n      }\n      \n      const pdfFilename = filename.replace(/\\.[^/.]+$/, '.pdf');\n      pdf.save(pdfFilename);\n    } finally {\n      document.body.removeChild(tempDiv);\n    }\n  } catch (error) {\n    throw new ExportError('Failed to export as PDF', 'pdf', error);\n  }\n}\n\n/**\n * Export markdown as DOCX (lazy loaded)\n */\nexport async function exportAsDocx(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    // Lazy load docx\n    const { Document, Packer, Paragraph, TextRun, HeadingLevel } = await import('docx');\n    \n    // Parse markdown and convert to DOCX elements\n    const lines = markdown.split('\\n');\n    const paragraphs: any[] = [];\n    \n    for (const line of lines) {\n      if (line.trim() === '') {\n        paragraphs.push(new Paragraph({ text: '' }));\n        continue;\n      }\n      \n      // Handle headings\n      const headingMatch = line.match(/^(#{1,6})\\s+(.+)$/);\n      if (headingMatch) {\n        const level = headingMatch[1].length;\n        const text = headingMatch[2];\n        \n        const headingLevels = [\n          HeadingLevel.HEADING_1,\n          HeadingLevel.HEADING_2,\n          HeadingLevel.HEADING_3,\n          HeadingLevel.HEADING_4,\n          HeadingLevel.HEADING_5,\n          HeadingLevel.HEADING_6\n        ];\n        \n        paragraphs.push(new Paragraph({\n          text,\n          heading: headingLevels[level - 1] || HeadingLevel.HEADING_6\n        }));\n        continue;\n      }\n      \n      // Handle bold and italic text\n      const textRuns: any[] = [];\n      let currentText = line;\n      \n      // Simple bold/italic parsing (basic implementation)\n      const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n      const italicRegex = /\\*(.*?)\\*/g;\n      \n      // For now, just add as plain text\n      // TODO: Implement proper markdown parsing for rich text\n      textRuns.push(new TextRun({ text: currentText }));\n      \n      paragraphs.push(new Paragraph({ children: textRuns }));\n    }\n    \n    const doc = new Document({\n      sections: [{\n        properties: {},\n        children: paragraphs\n      }]\n    });\n    \n    // Generate and download the document\n    const buffer = await Packer.toBuffer(doc);\n    const blob = new Blob([buffer], { \n      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n    });\n    \n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename.replace(/\\.[^/.]+$/, '.docx');\n    link.style.display = 'none';\n    \n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    setTimeout(() => URL.revokeObjectURL(url), 100);\n  } catch (error) {\n    throw new ExportError('Failed to export as DOCX', 'docx', error);\n  }\n}\n\n/**\n * Main export function that delegates to specific format handlers\n */\nexport async function exportMarkdown(\n  markdown: string,\n  filename: string,\n  options: ExportOptions\n): Promise<void> {\n  switch (options.format) {\n    case 'txt':\n      return exportAsText(markdown, filename);\n    case 'html':\n      return exportAsHtml(markdown, filename, options);\n    case 'pdf':\n      return exportAsPdf(markdown, filename, options);\n    case 'docx':\n      return exportAsDocx(markdown, filename, options);\n    default:\n      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);\n  }\n}\n\n/**\n * Get available export formats\n */\nexport function getAvailableFormats(): Array<{\n  value: string;\n  label: string;\n  description: string;\n}> {\n  return [\n    {\n      value: 'txt',\n      label: 'Plain Text',\n      description: 'Export as plain text file (.txt)'\n    },\n    {\n      value: 'html',\n      label: 'HTML',\n      description: 'Export as HTML file (.html)'\n    },\n    {\n      value: 'pdf',\n      label: 'PDF',\n      description: 'Export as PDF document (.pdf)'\n    },\n    {\n      value: 'docx',\n      label: 'Word Document',\n      description: 'Export as Microsoft Word document (.docx)'\n    }\n  ];\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAED;AACA;AACA;;;;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB;IAEhB,IAAI;QACF,MAAM,eAAe,SAAS,OAAO,CAAC,aAAa;QACnD,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,cAAc;IACvC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,qHAAA,CAAA,cAAW,CAAC,4BAA4B,OAAO;IAC3D;AACF;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;QAEnC,MAAM,WAAW,CAAC;;;;;WAKX,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgIlB,EAAE,YAAY;;OAEX,CAAC;QAEJ,MAAM,eAAe,SAAS,OAAO,CAAC,aAAa;QACnD,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,cAAc;IACvC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,qHAAA,CAAA,cAAW,CAAC,4BAA4B,QAAQ;IAC5D;AACF;AAKO,eAAe,YACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,kBAAkB;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;QAEnC,wCAAwC;QACxC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QACpB,QAAQ,KAAK,CAAC,QAAQ,GAAG;QACzB,QAAQ,KAAK,CAAC,IAAI,GAAG;QACrB,QAAQ,KAAK,CAAC,KAAK,GAAG;QACtB,QAAQ,KAAK,CAAC,UAAU,GAAG;QAC3B,QAAQ,KAAK,CAAC,QAAQ,GAAG;QACzB,QAAQ,KAAK,CAAC,UAAU,GAAG;QAE3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,IAAI,MAAM;gBACpB,aAAa;gBACb,MAAM;gBACN,QAAQ,QAAQ,QAAQ,IAAI;YAC9B;YAEA,cAAc;YACd,MAAM,UAAU,QAAQ,OAAO,IAAI;gBAAE,KAAK;gBAAI,OAAO;gBAAI,QAAQ;gBAAI,MAAM;YAAG;YAE9E,yBAAyB;YACzB,MAAM,QAAQ,SAAS,OAAO,CAAC,aAAa;YAC5C,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAG;YAEzC,sCAAsC;YACtC,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;YAChE,MAAM,QAAQ,IAAI,eAAe,CAAC,aAAa,MAAM,QAAQ,IAAI,GAAG,QAAQ,KAAK;YAEjF,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,QAAQ,GAAG,GAAG;YAE9B,MAAM,OAAO,CAAC,CAAC;gBACb,IAAI,YAAY,MAAM,QAAQ,MAAM,EAAE;oBACpC,IAAI,OAAO;oBACX,YAAY,QAAQ,GAAG;gBACzB;gBACA,IAAI,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAE;gBAC7B,aAAa;YACf;YAEA,4BAA4B;YAC5B,IAAI,QAAQ,eAAe,EAAE;gBAC3B,IAAI,aAAa,CAAC;oBAChB,OAAO;oBACP,SAAS;oBACT,cAAc,IAAI;gBACpB;YACF;YAEA,MAAM,cAAc,SAAS,OAAO,CAAC,aAAa;YAClD,IAAI,IAAI,CAAC;QACX,SAAU;YACR,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,qHAAA,CAAA,cAAW,CAAC,2BAA2B,OAAO;IAC1D;AACF;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,iBAAiB;QACjB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG;QAE/D,8CAA8C;QAC9C,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,MAAM,aAAoB,EAAE;QAE5B,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,OAAO,IAAI;gBACtB,WAAW,IAAI,CAAC,IAAI,UAAU;oBAAE,MAAM;gBAAG;gBACzC;YACF;YAEA,kBAAkB;YAClB,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,cAAc;gBAChB,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,MAAM;gBACpC,MAAM,OAAO,YAAY,CAAC,EAAE;gBAE5B,MAAM,gBAAgB;oBACpB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;iBACvB;gBAED,WAAW,IAAI,CAAC,IAAI,UAAU;oBAC5B;oBACA,SAAS,aAAa,CAAC,QAAQ,EAAE,IAAI,aAAa,SAAS;gBAC7D;gBACA;YACF;YAEA,8BAA8B;YAC9B,MAAM,WAAkB,EAAE;YAC1B,IAAI,cAAc;YAElB,oDAAoD;YACpD,MAAM,YAAY;YAClB,MAAM,cAAc;YAEpB,kCAAkC;YAClC,wDAAwD;YACxD,SAAS,IAAI,CAAC,IAAI,QAAQ;gBAAE,MAAM;YAAY;YAE9C,WAAW,IAAI,CAAC,IAAI,UAAU;gBAAE,UAAU;YAAS;QACrD;QAEA,MAAM,MAAM,IAAI,SAAS;YACvB,UAAU;gBAAC;oBACT,YAAY,CAAC;oBACb,UAAU;gBACZ;aAAE;QACJ;QAEA,qCAAqC;QACrC,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC;QACrC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAO,EAAE;YAC9B,MAAM;QACR;QAEA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,SAAS,OAAO,CAAC,aAAa;QAC9C,KAAK,KAAK,CAAC,OAAO,GAAG;QAErB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,WAAW,IAAM,IAAI,eAAe,CAAC,MAAM;IAC7C,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,qHAAA,CAAA,cAAW,CAAC,4BAA4B,QAAQ;IAC5D;AACF;AAKO,eAAe,eACpB,QAAgB,EAChB,QAAgB,EAChB,OAAsB;IAEtB,OAAQ,QAAQ,MAAM;QACpB,KAAK;YACH,OAAO,aAAa,UAAU;QAChC,KAAK;YACH,OAAO,aAAa,UAAU,UAAU;QAC1C,KAAK;YACH,OAAO,YAAY,UAAU,UAAU;QACzC,KAAK;YACH,OAAO,aAAa,UAAU,UAAU;QAC1C;YACE,MAAM,IAAI,qHAAA,CAAA,cAAW,CAAC,CAAC,2BAA2B,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,MAAM;IACxF;AACF;AAKO,SAAS;IAKd,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;AACH", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/Header.tsx"], "sourcesContent": ["/**\n * Header component with toolbar and navigation\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { exportMarkdown } from '@/utils/export';\nimport { readFileFromInput } from '@/utils/file';\nimport { ExportOptions } from '@/types';\n\ninterface HeaderProps {\n  onSidebarToggle: () => void;\n}\n\nexport function Header({ onSidebarToggle }: HeaderProps) {\n  const { state, saveCurrentFile, createNewFile, updateSettings } = useApp();\n  const [showExportMenu, setShowExportMenu] = useState(false);\n  const [showNewFileDialog, setShowNewFileDialog] = useState(false);\n  const [newFileName, setNewFileName] = useState('');\n  const [isExporting, setIsExporting] = useState(false);\n  const fileInputRef = React.useRef<HTMLInputElement>(null);\n\n  const handleNewFile = () => {\n    setShowNewFileDialog(true);\n  };\n\n  const handleCreateFile = () => {\n    if (newFileName.trim()) {\n      createNewFile(newFileName.trim());\n      setNewFileName('');\n      setShowNewFileDialog(false);\n    }\n  };\n\n  const handleUploadClick = () => {\n    fileInputRef.current?.click();\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files) return;\n\n    for (const file of Array.from(files)) {\n      try {\n        const content = await readFileFromInput(file);\n        createNewFile(file.name, content);\n      } catch (error) {\n        console.error('Failed to upload file:', error);\n        alert(`Failed to upload ${file.name}: ${error}`);\n      }\n    }\n\n    // Reset input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const handleSave = () => {\n    if (state.editor.currentFile) {\n      saveCurrentFile();\n    }\n  };\n\n  const handleExport = async (format: 'pdf' | 'docx' | 'txt' | 'html') => {\n    if (!state.editor.currentFile || !state.editor.content) {\n      alert('No file to export');\n      return;\n    }\n\n    setIsExporting(true);\n    setShowExportMenu(false);\n\n    try {\n      const options: ExportOptions = {\n        format,\n        includeMetadata: true,\n        pageSize: 'A4',\n        margins: { top: 20, right: 20, bottom: 20, left: 20 }\n      };\n\n      await exportMarkdown(\n        state.editor.content,\n        state.editor.currentFile.name,\n        options\n      );\n    } catch (error) {\n      console.error('Export failed:', error);\n      alert('Export failed. Please try again.');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const handleThemeToggle = () => {\n    updateSettings({\n      theme: state.settings.theme === 'light' ? 'dark' : 'light'\n    });\n  };\n\n  const handlePreviewModeChange = (mode: 'side' | 'preview' | 'edit') => {\n    updateSettings({ previewMode: mode });\n  };\n\n  return (\n    <header className=\"h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 no-print\">\n      {/* Left section */}\n      <div className=\"flex items-center space-x-4\">\n        {/* Sidebar toggle */}\n        <button\n          onClick={onSidebarToggle}\n          className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          title=\"Toggle sidebar\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        {/* Logo */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center\">\n            <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n          </div>\n          <span className=\"font-semibold text-gray-900 dark:text-white\">Markdown Editor</span>\n        </div>\n      </div>\n\n      {/* Center section - File actions */}\n      <div className=\"flex-1 flex items-center justify-center space-x-2\">\n        <button\n          onClick={handleNewFile}\n          className=\"px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n          title=\"New file (Ctrl+N)\"\n        >\n          New\n        </button>\n\n        <button\n          onClick={handleUploadClick}\n          className=\"px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors\"\n          title=\"Upload files\"\n        >\n          Upload\n        </button>\n\n        {/* Hidden file input */}\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\".md,.markdown,.mdown,.mkd,.mdx,.txt\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n\n        <button\n          onClick={handleSave}\n          disabled={!state.editor.isModified}\n          className=\"px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n          title=\"Save file (Ctrl+S)\"\n        >\n          Save\n        </button>\n\n        {/* Export dropdown */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setShowExportMenu(!showExportMenu)}\n            disabled={!state.editor.currentFile || isExporting}\n            className=\"px-3 py-1.5 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-1\"\n            title=\"Export file\"\n          >\n            {isExporting ? (\n              <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n            ) : (\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            )}\n            <span>Export</span>\n          </button>\n\n          {showExportMenu && (\n            <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50\">\n              <div className=\"py-1\">\n                <button\n                  onClick={() => handleExport('txt')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  Plain Text (.txt)\n                </button>\n                <button\n                  onClick={() => handleExport('html')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  HTML (.html)\n                </button>\n                <button\n                  onClick={() => handleExport('pdf')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  PDF (.pdf)\n                </button>\n                <button\n                  onClick={() => handleExport('docx')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  Word Document (.docx)\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Right section */}\n      <div className=\"flex items-center space-x-2\">\n        {/* Preview mode toggle */}\n        <div className=\"flex items-center bg-gray-100 dark:bg-gray-700 rounded-md p-1\">\n          <button\n            onClick={() => handlePreviewModeChange('edit')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'edit'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Editor only\"\n          >\n            Edit\n          </button>\n          <button\n            onClick={() => handlePreviewModeChange('side')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'side'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Side by side\"\n          >\n            Split\n          </button>\n          <button\n            onClick={() => handlePreviewModeChange('preview')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'preview'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Preview only\"\n          >\n            Preview\n          </button>\n        </div>\n\n        {/* Theme toggle */}\n        <button\n          onClick={handleThemeToggle}\n          className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          title=\"Toggle theme\"\n        >\n          {state.settings.theme === 'light' ? (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n            </svg>\n          ) : (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n            </svg>\n          )}\n        </button>\n\n        {/* Current file indicator */}\n        {state.editor.currentFile && (\n          <div className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400\">\n            <span>{state.editor.currentFile.name}</span>\n            {state.editor.isModified && (\n              <div className=\"w-2 h-2 bg-orange-500 rounded-full\" title=\"Unsaved changes\"></div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* New file dialog */}\n      {showNewFileDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Create New File\n            </h3>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                File Name\n              </label>\n              <input\n                type=\"text\"\n                value={newFileName}\n                onChange={(e) => setNewFileName(e.target.value)}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter') {\n                    handleCreateFile();\n                  } else if (e.key === 'Escape') {\n                    setShowNewFileDialog(false);\n                    setNewFileName('');\n                  }\n                }}\n                placeholder=\"my-document.md\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                autoFocus\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                .md extension will be added automatically if not provided\n              </p>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => {\n                  setShowNewFileDialog(false);\n                  setNewFileName('');\n                }}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleCreateFile}\n                disabled={!newFileName.trim()}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n              >\n                Create\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Click outside to close menus */}\n      {(showExportMenu || showNewFileDialog) && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => {\n            setShowExportMenu(false);\n            setShowNewFileDialog(false);\n          }}\n        ></div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,eAAe,EAAe;IACrD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAmB;IAEpD,MAAM,gBAAgB;QACpB,qBAAqB;IACvB;IAEA,MAAM,mBAAmB;QACvB,IAAI,YAAY,IAAI,IAAI;YACtB,cAAc,YAAY,IAAI;YAC9B,eAAe;YACf,qBAAqB;QACvB;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,OAAO;QAEZ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;YACpC,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACxC,cAAc,KAAK,IAAI,EAAE;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,OAAO;YACjD;QACF;QAEA,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,MAAM,MAAM,CAAC,WAAW,EAAE;YAC5B;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE;YACtD,MAAM;YACN;QACF;QAEA,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,UAAyB;gBAC7B;gBACA,iBAAiB;gBACjB,UAAU;gBACV,SAAS;oBAAE,KAAK;oBAAI,OAAO;oBAAI,QAAQ;oBAAI,MAAM;gBAAG;YACtD;YAEA,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EACjB,MAAM,MAAM,CAAC,OAAO,EACpB,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,EAC7B;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;YACb,OAAO,MAAM,QAAQ,CAAC,KAAK,KAAK,UAAU,SAAS;QACrD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,eAAe;YAAE,aAAa;QAAK;IACrC;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAKzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAK,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;0BAKlE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCACP;;;;;;kCAID,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCACP;;;;;;kCAKD,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC,MAAM,MAAM,CAAC,UAAU;wBAClC,WAAU;wBACV,OAAM;kCACP;;;;;;kCAKD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,UAAU,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI;gCACvC,WAAU;gCACV,OAAM;;oCAEL,4BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAGzE,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,SAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,SAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,YAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;;;;;;;kCAMH,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEL,MAAM,QAAQ,CAAC,KAAK,KAAK,wBACxB,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;iDAGvE,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;oBAM1E,MAAM,MAAM,CAAC,WAAW,kBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAM,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI;;;;;;4BACnC,MAAM,MAAM,CAAC,UAAU,kBACtB,8OAAC;gCAAI,WAAU;gCAAqC,OAAM;;;;;;;;;;;;;;;;;;YAOjE,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,qBAAqB;4CACrB,eAAe;wCACjB;oCACF;oCACA,aAAY;oCACZ,WAAU;oCACV,SAAS;;;;;;8CAEX,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,qBAAqB;wCACrB,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,YAAY,IAAI;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,CAAC,kBAAkB,iBAAiB,mBACnC,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,kBAAkB;oBAClB,qBAAqB;gBACvB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["/**\n * Sidebar component with file manager\n */\n\n'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { MarkdownFile } from '@/types';\nimport { formatDate, formatFileSize, readFileFromInput } from '@/utils/file';\n\nexport function Sidebar() {\n  const { state, openFile, createNewFile, deleteFile } = useApp();\n  const [showNewFileDialog, setShowNewFileDialog] = useState(false);\n  const [newFileName, setNewFileName] = useState('');\n  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);\n  const [contextMenu, setContextMenu] = useState<{\n    fileId: string;\n    x: number;\n    y: number;\n  } | null>(null);\n  const [showRenameDialog, setShowRenameDialog] = useState(false);\n  const [renameFileId, setRenameFileId] = useState<string | null>(null);\n  const [renameFileName, setRenameFileName] = useState('');\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileClick = (file: MarkdownFile) => {\n    if (!file.isDirectory) {\n      openFile(file);\n      setSelectedFileId(file.id);\n    }\n  };\n\n  const handleNewFile = () => {\n    if (newFileName.trim()) {\n      createNewFile(newFileName.trim());\n      setNewFileName('');\n      setShowNewFileDialog(false);\n    }\n  };\n\n  const handleDeleteFile = (fileId: string, fileName: string) => {\n    if (confirm(`Are you sure you want to delete \"${fileName}\"?`)) {\n      deleteFile(fileId);\n      if (selectedFileId === fileId) {\n        setSelectedFileId(null);\n      }\n    }\n    setContextMenu(null);\n  };\n\n  const handleRightClick = (e: React.MouseEvent, file: MarkdownFile) => {\n    e.preventDefault();\n    if (!file.isDirectory) {\n      setContextMenu({\n        fileId: file.id,\n        x: e.clientX,\n        y: e.clientY\n      });\n    }\n  };\n\n  const handleRename = (fileId: string, currentName: string) => {\n    setRenameFileId(fileId);\n    setRenameFileName(currentName.replace('.md', ''));\n    setShowRenameDialog(true);\n    setContextMenu(null);\n  };\n\n  const handleRenameSubmit = () => {\n    if (renameFileId && renameFileName.trim()) {\n      // TODO: Implement rename functionality in context\n      console.log('Rename file:', renameFileId, 'to:', renameFileName);\n      setShowRenameDialog(false);\n      setRenameFileId(null);\n      setRenameFileName('');\n    }\n  };\n\n  // Close context menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = () => setContextMenu(null);\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, []);\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files) return;\n\n    for (const file of Array.from(files)) {\n      try {\n        const content = await readFileFromInput(file);\n        createNewFile(file.name, content);\n      } catch (error) {\n        console.error('Failed to upload file:', error);\n        alert(`Failed to upload ${file.name}: ${error}`);\n      }\n    }\n\n    // Reset input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const sortedFiles = [...state.fileManager.files].sort((a, b) => {\n    // Directories first, then files\n    if (a.isDirectory && !b.isDirectory) return -1;\n    if (!a.isDirectory && b.isDirectory) return 1;\n    // Then sort by name\n    return a.name.localeCompare(b.name);\n  });\n\n  return (\n    <div className=\"h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">Files</h2>\n\n        {/* Action buttons */}\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowNewFileDialog(true)}\n            className=\"flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-1\"\n            title=\"Create new file\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n            </svg>\n            <span>New</span>\n          </button>\n\n          <button\n            onClick={() => fileInputRef.current?.click()}\n            className=\"flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center space-x-1\"\n            title=\"Upload files\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n            </svg>\n            <span>Upload</span>\n          </button>\n        </div>\n\n        {/* Hidden file input */}\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\".md,.markdown,.mdown,.mkd,.mdx,.txt\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n      </div>\n\n      {/* File list */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {state.fileManager.isLoading ? (\n          <div className=\"p-4 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Loading files...</p>\n          </div>\n        ) : state.fileManager.error ? (\n          <div className=\"p-4 text-center\">\n            <p className=\"text-sm text-red-600 dark:text-red-400\">{state.fileManager.error}</p>\n          </div>\n        ) : sortedFiles.length === 0 ? (\n          <div className=\"p-4 text-center\">\n            <svg className=\"w-12 h-12 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">No files yet</p>\n            <p className=\"text-xs text-gray-500 dark:text-gray-500\">Create a new file or upload existing ones</p>\n          </div>\n        ) : (\n          <div className=\"p-2\">\n            {sortedFiles.map((file) => (\n              <div\n                key={file.id}\n                className={`group flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${\n                  selectedFileId === file.id\n                    ? 'bg-blue-100 dark:bg-blue-900/50'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                onClick={() => handleFileClick(file)}\n                onContextMenu={(e) => handleRightClick(e, file)}\n              >\n                <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n                  {/* File icon */}\n                  <div className=\"flex-shrink-0\">\n                    {file.isDirectory ? (\n                      <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    )}\n                  </div>\n\n                  {/* File info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </p>\n                    <div className=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                      <span>{formatDate(file.lastModified)}</span>\n                      {!file.isDirectory && (\n                        <>\n                          <span>•</span>\n                          <span>{formatFileSize(file.size)}</span>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {!file.isDirectory && (\n                  <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDeleteFile(file.id, file.name);\n                      }}\n                      className=\"p-1 text-red-600 hover:text-red-700 transition-colors\"\n                      title=\"Delete file\"\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                      </svg>\n                    </button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* New file dialog */}\n      {showNewFileDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Create New File\n            </h3>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                File Name\n              </label>\n              <input\n                type=\"text\"\n                value={newFileName}\n                onChange={(e) => setNewFileName(e.target.value)}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter') {\n                    handleNewFile();\n                  } else if (e.key === 'Escape') {\n                    setShowNewFileDialog(false);\n                    setNewFileName('');\n                  }\n                }}\n                placeholder=\"my-document.md\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                autoFocus\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                .md extension will be added automatically if not provided\n              </p>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => {\n                  setShowNewFileDialog(false);\n                  setNewFileName('');\n                }}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleNewFile}\n                disabled={!newFileName.trim()}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n              >\n                Create\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Context Menu */}\n      {contextMenu && (\n        <div\n          className=\"fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 z-50\"\n          style={{\n            left: contextMenu.x,\n            top: contextMenu.y\n          }}\n        >\n          <button\n            onClick={() => {\n              const file = sortedFiles.find(f => f.id === contextMenu.fileId);\n              if (file) handleRename(file.id, file.name);\n            }}\n            className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n            <span>Rename</span>\n          </button>\n          <button\n            onClick={() => {\n              const file = sortedFiles.find(f => f.id === contextMenu.fileId);\n              if (file) handleDeleteFile(file.id, file.name);\n            }}\n            className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 text-red-600 dark:text-red-400 flex items-center space-x-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n            <span>Delete</span>\n          </button>\n        </div>\n      )}\n\n      {/* Rename Dialog */}\n      {showRenameDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Rename File\n            </h3>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                File Name\n              </label>\n              <input\n                type=\"text\"\n                value={renameFileName}\n                onChange={(e) => setRenameFileName(e.target.value)}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter') {\n                    handleRenameSubmit();\n                  } else if (e.key === 'Escape') {\n                    setShowRenameDialog(false);\n                    setRenameFileId(null);\n                    setRenameFileName('');\n                  }\n                }}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                autoFocus\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                .md extension will be added automatically if not provided\n              </p>\n            </div>\n\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => {\n                  setShowRenameDialog(false);\n                  setRenameFileId(null);\n                  setRenameFileName('');\n                }}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleRenameSubmit}\n                disabled={!renameFileName.trim()}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n              >\n                Rename\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAEA;AALA;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAInC;IACV,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,SAAS;YACT,kBAAkB,KAAK,EAAE;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY,IAAI,IAAI;YACtB,cAAc,YAAY,IAAI;YAC9B,eAAe;YACf,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,QAAQ,CAAC,iCAAiC,EAAE,SAAS,EAAE,CAAC,GAAG;YAC7D,WAAW;YACX,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;YACpB;QACF;QACA,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC,GAAqB;QAC7C,EAAE,cAAc;QAChB,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,eAAe;gBACb,QAAQ,KAAK,EAAE;gBACf,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;YACd;QACF;IACF;IAEA,MAAM,eAAe,CAAC,QAAgB;QACpC,gBAAgB;QAChB,kBAAkB,YAAY,OAAO,CAAC,OAAO;QAC7C,oBAAoB;QACpB,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,IAAI,gBAAgB,eAAe,IAAI,IAAI;YACzC,kDAAkD;YAClD,QAAQ,GAAG,CAAC,gBAAgB,cAAc,OAAO;YACjD,oBAAoB;YACpB,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,IAAM,eAAe;QAChD,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;IACrD,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,OAAO;QAEZ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;YACpC,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACxC,cAAc,KAAK,IAAI,EAAE;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,OAAO;YACjD;QACF;QAEA,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,cAAc;WAAI,MAAM,WAAW,CAAC,KAAK;KAAC,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,gCAAgC;QAChC,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;QAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;QAC5C,oBAAoB;QACpB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;gCACV,OAAM;;kDAEN,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,WAAU;gCACV,OAAM;;kDAEN,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,WAAW,CAAC,SAAS,iBAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;2BAExD,MAAM,WAAW,CAAC,KAAK,iBACzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0C,MAAM,WAAW,CAAC,KAAK;;;;;;;;;;2BAE9E,YAAY,MAAM,KAAK,kBACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAC7D,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;yCAG1D,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;4BAEC,WAAW,CAAC,wFAAwF,EAClG,mBAAmB,KAAK,EAAE,GACtB,oCACA,4CACJ;4BACF,SAAS,IAAM,gBAAgB;4BAC/B,eAAe,CAAC,IAAM,iBAAiB,GAAG;;8CAE1C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW,iBACf,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;qEAGV,8OAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAM3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,KAAK,IAAI;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,YAAY;;;;;;wDAClC,CAAC,KAAK,WAAW,kBAChB;;8EACE,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQxC,CAAC,KAAK,WAAW,kBAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;wCACrC;wCACA,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;2BApDxE,KAAK,EAAE;;;;;;;;;;;;;;;YAgErB,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,qBAAqB;4CACrB,eAAe;wCACjB;oCACF;oCACA,aAAY;oCACZ,WAAU;oCACV,SAAS;;;;;;8CAEX,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,qBAAqB;wCACrB,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,YAAY,IAAI;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,6BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,MAAM,YAAY,CAAC;oBACnB,KAAK,YAAY,CAAC;gBACpB;;kCAEA,8OAAC;wBACC,SAAS;4BACP,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,MAAM;4BAC9D,IAAI,MAAM,aAAa,KAAK,EAAE,EAAE,KAAK,IAAI;wBAC3C;wBACA,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBACC,SAAS;4BACP,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,MAAM;4BAC9D,IAAI,MAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;wBAC/C;wBACA,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,oBAAoB;4CACpB,gBAAgB;4CAChB,kBAAkB;wCACpB;oCACF;oCACA,WAAU;oCACV,SAAS;;;;;;8CAEX,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,oBAAoB;wCACpB,gBAAgB;wCAChB,kBAAkB;oCACpB;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,eAAe,IAAI;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Editor/EditorToolbar.tsx"], "sourcesContent": ["/**\n * Editor toolbar with formatting buttons\n */\n\n'use client';\n\nimport React, { useState } from 'react';\n\ninterface EditorToolbarProps {\n  onInsertMarkdown: (before: string, after: string, placeholder: string) => void;\n  onUndo: () => void;\n  onRedo: () => void;\n  onClear: () => void;\n  onPaste: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n}\n\nexport function EditorToolbar({\n  onInsertMarkdown,\n  onUndo,\n  onRedo,\n  onClear,\n  onPaste,\n  canUndo,\n  canRedo\n}: EditorToolbarProps) {\n  const [showHeadingMenu, setShowHeadingMenu] = useState(false);\n  const [showTableDialog, setShowTableDialog] = useState(false);\n  const [tableRows, setTableRows] = useState(3);\n  const [tableCols, setTableCols] = useState(3);\n\n  const toolbarButtons = [\n    {\n      group: 'actions',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\" />\n            </svg>\n          ),\n          title: 'Undo (Ctrl+Z)',\n          action: onUndo,\n          disabled: !canUndo\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 10h-10a8 8 0 00-8 8v2M21 10l-6 6m6-6l-6-6\" />\n            </svg>\n          ),\n          title: 'Redo (Ctrl+Y)',\n          action: onRedo,\n          disabled: !canRedo\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n          ),\n          title: 'Paste (Ctrl+Shift+V)',\n          action: onPaste\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n          ),\n          title: 'Clear All',\n          action: () => {\n            if (confirm('Are you sure you want to clear all content?')) {\n              onClear();\n            }\n          },\n          className: 'text-red-600 hover:text-red-700'\n        }\n      ]\n    },\n    {\n      group: 'text',\n      buttons: [\n        {\n          icon: 'B',\n          title: 'Bold (Ctrl+B)',\n          action: () => onInsertMarkdown('**', '**', 'bold text'),\n          className: 'font-bold'\n        },\n        {\n          icon: 'I',\n          title: 'Italic (Ctrl+I)',\n          action: () => onInsertMarkdown('*', '*', 'italic text'),\n          className: 'italic'\n        },\n        {\n          icon: 'S',\n          title: 'Strikethrough',\n          action: () => onInsertMarkdown('~~', '~~', 'strikethrough text'),\n          className: 'line-through'\n        },\n        {\n          icon: '`',\n          title: 'Inline Code',\n          action: () => onInsertMarkdown('`', '`', 'code'),\n          className: 'font-mono'\n        }\n      ]\n    },\n    {\n      group: 'structure',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          ),\n          title: 'Headings',\n          action: () => setShowHeadingMenu(!showHeadingMenu),\n          hasDropdown: true\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n            </svg>\n          ),\n          title: 'Unordered List',\n          action: () => onInsertMarkdown('- ', '', 'list item')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n          ),\n          title: 'Ordered List',\n          action: () => onInsertMarkdown('1. ', '', 'list item')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n          ),\n          title: 'Blockquote',\n          action: () => onInsertMarkdown('> ', '', 'blockquote text')\n        }\n      ]\n    },\n    {\n      group: 'media',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n            </svg>\n          ),\n          title: 'Link (Ctrl+K)',\n          action: () => onInsertMarkdown('[', '](url)', 'link text')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n          ),\n          title: 'Image',\n          action: () => onInsertMarkdown('![', '](image-url)', 'alt text')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n            </svg>\n          ),\n          title: 'Table',\n          action: () => setShowTableDialog(true)\n        }\n      ]\n    },\n    {\n      group: 'code',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n            </svg>\n          ),\n          title: 'Code Block',\n          action: () => onInsertMarkdown('```\\n', '\\n```', 'code here')\n        },\n        {\n          icon: '---',\n          title: 'Horizontal Rule',\n          action: () => onInsertMarkdown('\\n---\\n', '', ''),\n          className: 'text-xs'\n        }\n      ]\n    }\n  ];\n\n  const insertHeading = (level: number) => {\n    const hashes = '#'.repeat(level);\n    onInsertMarkdown(`${hashes} `, '', `Heading ${level}`);\n    setShowHeadingMenu(false);\n  };\n\n  const insertTable = () => {\n    const headers = Array(tableCols).fill('Header').map((h, i) => `${h} ${i + 1}`).join(' | ');\n    const separator = Array(tableCols).fill('---').join(' | ');\n    const rows = Array(tableRows - 1).fill(null).map((_, rowIndex) =>\n      Array(tableCols).fill('Cell').map((c, colIndex) => `${c} ${rowIndex + 1}-${colIndex + 1}`).join(' | ')\n    ).join('\\n');\n\n    const table = `| ${headers} |\\n| ${separator} |\\n| ${rows} |`;\n    onInsertMarkdown('\\n', '\\n', table);\n    setShowTableDialog(false);\n  };\n\n  return (\n    <div className=\"border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-2 py-1\">\n      <div className=\"flex items-center space-x-0.5 overflow-x-auto\">\n        {toolbarButtons.map((group, groupIndex) => (\n          <React.Fragment key={group.group}>\n            {groupIndex > 0 && (\n              <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\n            )}\n\n            {group.buttons.map((button, buttonIndex) => (\n              <div key={buttonIndex} className=\"relative\">\n                <button\n                  onClick={button.action}\n                  disabled={button.disabled}\n                  className={`p-1.5 rounded transition-colors text-sm ${\n                    button.disabled\n                      ? 'opacity-50 cursor-not-allowed'\n                      : 'hover:bg-gray-200 dark:hover:bg-gray-700'\n                  } ${button.className || ''}`}\n                  title={button.title}\n                >\n                  {typeof button.icon === 'string' ? (\n                    <span className=\"text-sm font-medium\">{button.icon}</span>\n                  ) : (\n                    button.icon\n                  )}\n                </button>\n\n                {/* Heading dropdown */}\n                {button.hasDropdown && showHeadingMenu && (\n                  <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50\">\n                    <div className=\"py-1\">\n                      {[1, 2, 3, 4, 5, 6].map(level => (\n                        <button\n                          key={level}\n                          onClick={() => insertHeading(level)}\n                          className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                        >\n                          <span className=\"font-mono text-gray-500\">{'#'.repeat(level)}</span>\n                          <span style={{ fontSize: `${20 - level}px` }} className=\"font-semibold\">\n                            Heading {level}\n                          </span>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </React.Fragment>\n        ))}\n      </div>\n\n      {/* Table dialog */}\n      {showTableDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Insert Table\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Rows\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"2\"\n                  max=\"20\"\n                  value={tableRows}\n                  onChange={(e) => setTableRows(parseInt(e.target.value) || 2)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Columns\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"2\"\n                  max=\"10\"\n                  value={tableCols}\n                  onChange={(e) => setTableCols(parseInt(e.target.value) || 2)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowTableDialog(false)}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={insertTable}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Insert Table\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Click outside to close menus */}\n      {(showHeadingMenu || showTableDialog) && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => {\n            setShowHeadingMenu(false);\n            setShowTableDialog(false);\n          }}\n        ></div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AAFA;;;AAcO,SAAS,cAAc,EAC5B,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACY;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ;oBACR,UAAU,CAAC;gBACb;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ;oBACR,UAAU,CAAC;gBACb;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ;gBACV;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ;wBACN,IAAI,QAAQ,gDAAgD;4BAC1D;wBACF;oBACF;oBACA,WAAW;gBACb;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,MAAM;oBAC3C,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,KAAK;oBACzC,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,MAAM;oBAC3C,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,KAAK;oBACzC,WAAW;gBACb;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,mBAAmB,CAAC;oBAClC,aAAa;gBACf;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,IAAI;gBAC3C;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,OAAO,IAAI;gBAC5C;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,IAAI;gBAC3C;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,UAAU;gBAChD;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,gBAAgB;gBACvD;gBACA;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,mBAAmB;gBACnC;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,SAAS,SAAS;gBACnD;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,WAAW,IAAI;oBAC9C,WAAW;gBACb;aACD;QACH;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,IAAI,MAAM,CAAC;QAC1B,iBAAiB,GAAG,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO;QACrD,mBAAmB;IACrB;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU,MAAM,WAAW,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAM,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;QACpF,MAAM,YAAY,MAAM,WAAW,IAAI,CAAC,OAAO,IAAI,CAAC;QACpD,MAAM,OAAO,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,WACnD,MAAM,WAAW,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAa,GAAG,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,GAAG,EAAE,IAAI,CAAC,QAChG,IAAI,CAAC;QAEP,MAAM,QAAQ,CAAC,EAAE,EAAE,QAAQ,MAAM,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,CAAC;QAC7D,iBAAiB,MAAM,MAAM;QAC7B,mBAAmB;IACrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,2BAC1B,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;4BACZ,aAAa,mBACZ,8OAAC;gCAAI,WAAU;;;;;;4BAGhB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC1B,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CACC,SAAS,OAAO,MAAM;4CACtB,UAAU,OAAO,QAAQ;4CACzB,WAAW,CAAC,wCAAwC,EAClD,OAAO,QAAQ,GACX,kCACA,2CACL,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;4CAC5B,OAAO,OAAO,KAAK;sDAElB,OAAO,OAAO,IAAI,KAAK,yBACtB,8OAAC;gDAAK,WAAU;0DAAuB,OAAO,IAAI;;;;;uDAElD,OAAO,IAAI;;;;;;wCAKd,OAAO,WAAW,IAAI,iCACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAG;oDAAG;oDAAG;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAA,sBACtB,8OAAC;wDAEC,SAAS,IAAM,cAAc;wDAC7B,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAA2B,IAAI,MAAM,CAAC;;;;;;0EACtD,8OAAC;gEAAK,OAAO;oEAAE,UAAU,GAAG,KAAK,MAAM,EAAE,CAAC;gEAAC;gEAAG,WAAU;;oEAAgB;oEAC7D;;;;;;;;uDANN;;;;;;;;;;;;;;;;mCAxBP;;;;;;uBANO,MAAM,KAAK;;;;;;;;;;YAkDnC,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC1D,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC1D,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,CAAC,mBAAmB,eAAe,mBAClC,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,mBAAmB;oBACnB,mBAAmB;gBACrB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Editor/EditorPane.tsx"], "sourcesContent": ["/**\n * Editor pane component with markdown editing capabilities\n */\n\n'use client';\n\nimport React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { debounce } from '@/utils/file';\nimport { EditorToolbar } from './EditorToolbar';\n\ninterface EditorPaneProps {\n  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;\n}\n\nexport function EditorPane({ onScroll }: EditorPaneProps = {}) {\n  const { state, updateContent, dispatch } = useApp();\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const [localContent, setLocalContent] = useState(state.editor.content);\n  const [undoStack, setUndoStack] = useState<string[]>([]);\n  const [redoStack, setRedoStack] = useState<string[]>([]);\n\n  // Sync local content with state when file changes\n  useEffect(() => {\n    setLocalContent(state.editor.content);\n    setUndoStack([]);\n    setRedoStack([]);\n  }, [state.editor.currentFile?.id]);\n\n  // Debounced content update to improve performance\n  const debouncedUpdateContent = useCallback(\n    debounce((content: string) => {\n      updateContent(content);\n    }, 150), // Reduced debounce time for better responsiveness\n    [updateContent]\n  );\n\n  // Add to undo stack\n  const addToUndoStack = useCallback((content: string) => {\n    setUndoStack(prev => {\n      const newStack = [...prev, content];\n      return newStack.slice(-50); // Keep only last 50 states\n    });\n    setRedoStack([]); // Clear redo stack when new content is added\n  }, []);\n\n  // Handle content changes with immediate local update\n  const handleContentChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const content = event.target.value;\n\n    // Add current content to undo stack before changing\n    if (localContent !== content && localContent.length > 0) {\n      addToUndoStack(localContent);\n    }\n\n    setLocalContent(content); // Immediate local update for responsiveness\n    debouncedUpdateContent(content);\n    updateCursorPosition();\n  };\n\n  // Undo functionality\n  const handleUndo = useCallback(() => {\n    if (undoStack.length === 0) return;\n\n    const previousContent = undoStack[undoStack.length - 1];\n    setRedoStack(prev => [localContent, ...prev]);\n    setUndoStack(prev => prev.slice(0, -1));\n    setLocalContent(previousContent);\n    updateContent(previousContent);\n\n    if (textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [undoStack, localContent, updateContent]);\n\n  // Redo functionality\n  const handleRedo = useCallback(() => {\n    if (redoStack.length === 0) return;\n\n    const nextContent = redoStack[0];\n    setUndoStack(prev => [...prev, localContent]);\n    setRedoStack(prev => prev.slice(1));\n    setLocalContent(nextContent);\n    updateContent(nextContent);\n\n    if (textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [redoStack, localContent, updateContent]);\n\n  // Clear editor\n  const handleClear = useCallback(() => {\n    if (localContent.length > 0) {\n      addToUndoStack(localContent);\n    }\n    setLocalContent('');\n    updateContent('');\n\n    if (textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [localContent, updateContent, addToUndoStack]);\n\n  // Paste functionality\n  const handlePaste = useCallback(async () => {\n    try {\n      const text = await navigator.clipboard.readText();\n      if (textareaRef.current) {\n        const textarea = textareaRef.current;\n        const start = textarea.selectionStart;\n        const end = textarea.selectionEnd;\n        const newContent = localContent.substring(0, start) + text + localContent.substring(end);\n\n        addToUndoStack(localContent);\n        setLocalContent(newContent);\n        updateContent(newContent);\n\n        // Set cursor position after pasted text\n        setTimeout(() => {\n          textarea.setSelectionRange(start + text.length, start + text.length);\n          textarea.focus();\n        }, 0);\n      }\n    } catch (error) {\n      console.error('Failed to paste from clipboard:', error);\n    }\n  }, [localContent, updateContent, addToUndoStack]);\n\n  // Update cursor position\n  const updateCursorPosition = () => {\n    if (!textareaRef.current) return;\n\n    const textarea = textareaRef.current;\n    const content = textarea.value;\n    const cursorPos = textarea.selectionStart;\n\n    const lines = content.substring(0, cursorPos).split('\\n');\n    const line = lines.length;\n    const column = lines[lines.length - 1].length + 1;\n\n    dispatch({\n      type: 'EDITOR_ACTION',\n      payload: {\n        type: 'SET_CURSOR_POSITION',\n        payload: { line, column }\n      }\n    });\n  };\n\n  // Handle keyboard shortcuts\n  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (event.ctrlKey || event.metaKey) {\n      switch (event.key) {\n        case 's':\n          event.preventDefault();\n          // Save functionality is handled by the header\n          break;\n        case 'b':\n          event.preventDefault();\n          insertMarkdown('**', '**', 'bold text');\n          break;\n        case 'i':\n          event.preventDefault();\n          insertMarkdown('*', '*', 'italic text');\n          break;\n        case 'k':\n          event.preventDefault();\n          insertMarkdown('[', '](url)', 'link text');\n          break;\n        case 'z':\n          event.preventDefault();\n          if (event.shiftKey) {\n            handleRedo();\n          } else {\n            handleUndo();\n          }\n          break;\n        case 'y':\n          event.preventDefault();\n          handleRedo();\n          break;\n        case 'v':\n          if (event.shiftKey) {\n            event.preventDefault();\n            handlePaste();\n          }\n          break;\n      }\n    }\n\n    // Tab handling for indentation\n    if (event.key === 'Tab') {\n      event.preventDefault();\n      const textarea = event.currentTarget;\n      const start = textarea.selectionStart;\n      const end = textarea.selectionEnd;\n      const value = textarea.value;\n\n      if (event.shiftKey) {\n        // Unindent\n        const lineStart = value.lastIndexOf('\\n', start - 1) + 1;\n        const lineText = value.substring(lineStart, start);\n        if (lineText.startsWith('  ')) {\n          const newValue = value.substring(0, lineStart) +\n                          lineText.substring(2) +\n                          value.substring(start);\n          textarea.value = newValue;\n          textarea.setSelectionRange(start - 2, end - 2);\n          debouncedUpdateContent(newValue);\n        }\n      } else {\n        // Indent\n        const newValue = value.substring(0, start) + '  ' + value.substring(end);\n        textarea.value = newValue;\n        textarea.setSelectionRange(start + 2, end + 2);\n        debouncedUpdateContent(newValue);\n      }\n    }\n  };\n\n  // Insert markdown formatting\n  const insertMarkdown = (before: string, after: string, placeholder: string) => {\n    if (!textareaRef.current) return;\n\n    const textarea = textareaRef.current;\n    const start = textarea.selectionStart;\n    const end = textarea.selectionEnd;\n    const selectedText = localContent.substring(start, end);\n    const replacement = selectedText || placeholder;\n\n    // Add current content to undo stack\n    addToUndoStack(localContent);\n\n    const newValue =\n      localContent.substring(0, start) +\n      before + replacement + after +\n      localContent.substring(end);\n\n    // Update both local and global state\n    setLocalContent(newValue);\n    updateContent(newValue);\n\n    // Set cursor position after state update\n    setTimeout(() => {\n      if (selectedText) {\n        textarea.setSelectionRange(start + before.length, start + before.length + replacement.length);\n      } else {\n        textarea.setSelectionRange(start + before.length, start + before.length + placeholder.length);\n      }\n      textarea.focus();\n    }, 0);\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(false);\n\n    const files = Array.from(event.dataTransfer.files);\n    const imageFiles = files.filter(file => file.type.startsWith('image/'));\n\n    if (imageFiles.length > 0) {\n      imageFiles.forEach(file => {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const dataUrl = e.target?.result as string;\n          const markdown = `![${file.name}](${dataUrl})`;\n          insertMarkdown('', '', markdown);\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  };\n\n  // Focus textarea when file is opened\n  useEffect(() => {\n    if (state.editor.currentFile && textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [state.editor.currentFile]);\n\n  // Update cursor position on click\n  const handleClick = () => {\n    setTimeout(updateCursorPosition, 0);\n  };\n\n  // Handle scroll synchronization\n  const handleScroll = (event: React.UIEvent<HTMLTextAreaElement>) => {\n    if (onScroll && textareaRef.current) {\n      const { scrollTop, scrollHeight, clientHeight } = textareaRef.current;\n      onScroll(scrollTop, scrollHeight, clientHeight);\n    }\n  };\n\n  // Memoized line numbers for performance\n  const lineNumbers = useMemo(() => {\n    const lines = localContent.split('\\n');\n    return lines.map((_, index) => index + 1);\n  }, [localContent]);\n\n  // Calculate line height and padding for line numbers alignment\n  const lineHeight = state.settings.fontSize * 1.6;\n  const paddingTop = 16; // 4 * 4px (p-4)\n\n  return (\n    <div className=\"h-full flex flex-col bg-white dark:bg-gray-900\">\n      {/* Toolbar */}\n      <EditorToolbar\n        onInsertMarkdown={insertMarkdown}\n        onUndo={handleUndo}\n        onRedo={handleRedo}\n        onClear={handleClear}\n        onPaste={handlePaste}\n        canUndo={undoStack.length > 0}\n        canRedo={redoStack.length > 0}\n      />\n\n      {/* Editor area */}\n      <div className=\"flex-1 relative\">\n        {state.editor.currentFile ? (\n          <div\n            className={`h-full relative ${isDragging ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <textarea\n              ref={textareaRef}\n              value={localContent}\n              onChange={handleContentChange}\n              onKeyDown={handleKeyDown}\n              onClick={handleClick}\n              onKeyUp={updateCursorPosition}\n              onScroll={handleScroll}\n              className={`w-full h-full resize-none border-none outline-none bg-transparent text-gray-900 dark:text-white font-mono ${\n                state.settings.wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'\n              } ${state.settings.lineNumbers ? 'pl-16' : 'p-4'}`}\n              style={{\n                fontSize: `${state.settings.fontSize}px`,\n                lineHeight: `${lineHeight}px`,\n                tabSize: 2,\n                paddingTop: `${paddingTop}px`,\n                paddingRight: '16px',\n                paddingBottom: '16px'\n              }}\n              placeholder=\"Start writing your markdown here...\"\n              spellCheck={false}\n            />\n\n            {/* Line numbers */}\n            {state.settings.lineNumbers && (\n              <div\n                className=\"absolute left-0 top-0 w-12 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 pointer-events-none select-none overflow-hidden\"\n                style={{\n                  paddingTop: `${paddingTop}px`,\n                }}\n              >\n                <div\n                  className=\"text-gray-400 dark:text-gray-600 font-mono text-right pr-2\"\n                  style={{\n                    fontSize: `${state.settings.fontSize}px`,\n                    lineHeight: `${lineHeight}px`\n                  }}\n                >\n                  {lineNumbers.map((lineNum) => (\n                    <div key={lineNum} style={{ height: `${lineHeight}px` }}>\n                      {lineNum}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Drag overlay */}\n            {isDragging && (\n              <div className=\"absolute inset-0 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <svg className=\"w-12 h-12 text-blue-600 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                  </svg>\n                  <p className=\"text-blue-600 font-medium\">Drop images here to insert</p>\n                </div>\n              </div>\n            )}\n\n            {/* Loading overlay */}\n            {state.editor.isLoading && (\n              <div className=\"absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Loading...</p>\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n            <div className=\"text-center\">\n              <svg className=\"w-16 h-16 mx-auto mb-4 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <h3 className=\"text-lg font-medium mb-2\">No file selected</h3>\n              <p className=\"text-sm\">Create a new file or select an existing one to start editing</p>\n            </div>\n          </div>\n        )}\n\n        {/* Error display */}\n        {state.editor.error && (\n          <div className=\"absolute bottom-4 right-4 bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700 rounded-md p-3 max-w-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm text-red-800 dark:text-red-200\">{state.editor.error}</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AACA;AALA;;;;;;AAWO,SAAS,WAAW,EAAE,QAAQ,EAAmB,GAAG,CAAC,CAAC;IAC3D,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAChD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,MAAM,MAAM,CAAC,OAAO;QACpC,aAAa,EAAE;QACf,aAAa,EAAE;IACjB,GAAG;QAAC,MAAM,MAAM,CAAC,WAAW,EAAE;KAAG;IAEjC,kDAAkD;IAClD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,cAAc;IAChB,GAAG,MACH;QAAC;KAAc;IAGjB,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,aAAa,CAAA;YACX,MAAM,WAAW;mBAAI;gBAAM;aAAQ;YACnC,OAAO,SAAS,KAAK,CAAC,CAAC,KAAK,2BAA2B;QACzD;QACA,aAAa,EAAE,GAAG,6CAA6C;IACjE,GAAG,EAAE;IAEL,qDAAqD;IACrD,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAAU,MAAM,MAAM,CAAC,KAAK;QAElC,oDAAoD;QACpD,IAAI,iBAAiB,WAAW,aAAa,MAAM,GAAG,GAAG;YACvD,eAAe;QACjB;QAEA,gBAAgB,UAAU,4CAA4C;QACtE,uBAAuB;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,UAAU,MAAM,KAAK,GAAG;QAE5B,MAAM,kBAAkB,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;QACvD,aAAa,CAAA,OAAQ;gBAAC;mBAAiB;aAAK;QAC5C,aAAa,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,CAAC;QACpC,gBAAgB;QAChB,cAAc;QAEd,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;QAAW;QAAc;KAAc;IAE3C,qBAAqB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,UAAU,MAAM,KAAK,GAAG;QAE5B,MAAM,cAAc,SAAS,CAAC,EAAE;QAChC,aAAa,CAAA,OAAQ;mBAAI;gBAAM;aAAa;QAC5C,aAAa,CAAA,OAAQ,KAAK,KAAK,CAAC;QAChC,gBAAgB;QAChB,cAAc;QAEd,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;QAAW;QAAc;KAAc;IAE3C,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,eAAe;QACjB;QACA,gBAAgB;QAChB,cAAc;QAEd,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC;QAAc;QAAe;KAAe;IAEhD,sBAAsB;IACtB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,MAAM,OAAO,MAAM,UAAU,SAAS,CAAC,QAAQ;YAC/C,IAAI,YAAY,OAAO,EAAE;gBACvB,MAAM,WAAW,YAAY,OAAO;gBACpC,MAAM,QAAQ,SAAS,cAAc;gBACrC,MAAM,MAAM,SAAS,YAAY;gBACjC,MAAM,aAAa,aAAa,SAAS,CAAC,GAAG,SAAS,OAAO,aAAa,SAAS,CAAC;gBAEpF,eAAe;gBACf,gBAAgB;gBAChB,cAAc;gBAEd,wCAAwC;gBACxC,WAAW;oBACT,SAAS,iBAAiB,CAAC,QAAQ,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM;oBACnE,SAAS,KAAK;gBAChB,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF,GAAG;QAAC;QAAc;QAAe;KAAe;IAEhD,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,UAAU,SAAS,KAAK;QAC9B,MAAM,YAAY,SAAS,cAAc;QAEzC,MAAM,QAAQ,QAAQ,SAAS,CAAC,GAAG,WAAW,KAAK,CAAC;QACpD,MAAM,OAAO,MAAM,MAAM;QACzB,MAAM,SAAS,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG;QAEhD,SAAS;YACP,MAAM;YACN,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE;oBAAM;gBAAO;YAC1B;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,EAAE;YAClC,OAAQ,MAAM,GAAG;gBACf,KAAK;oBACH,MAAM,cAAc;oBAEpB;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,MAAM,MAAM;oBAC3B;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,KAAK,KAAK;oBACzB;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,KAAK,UAAU;oBAC9B;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,IAAI,MAAM,QAAQ,EAAE;wBAClB;oBACF,OAAO;wBACL;oBACF;oBACA;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB;oBACA;gBACF,KAAK;oBACH,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;YACJ;QACF;QAEA,+BAA+B;QAC/B,IAAI,MAAM,GAAG,KAAK,OAAO;YACvB,MAAM,cAAc;YACpB,MAAM,WAAW,MAAM,aAAa;YACpC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YACjC,MAAM,QAAQ,SAAS,KAAK;YAE5B,IAAI,MAAM,QAAQ,EAAE;gBAClB,WAAW;gBACX,MAAM,YAAY,MAAM,WAAW,CAAC,MAAM,QAAQ,KAAK;gBACvD,MAAM,WAAW,MAAM,SAAS,CAAC,WAAW;gBAC5C,IAAI,SAAS,UAAU,CAAC,OAAO;oBAC7B,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,aACpB,SAAS,SAAS,CAAC,KACnB,MAAM,SAAS,CAAC;oBAChC,SAAS,KAAK,GAAG;oBACjB,SAAS,iBAAiB,CAAC,QAAQ,GAAG,MAAM;oBAC5C,uBAAuB;gBACzB;YACF,OAAO;gBACL,SAAS;gBACT,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,SAAS,OAAO,MAAM,SAAS,CAAC;gBACpE,SAAS,KAAK,GAAG;gBACjB,SAAS,iBAAiB,CAAC,QAAQ,GAAG,MAAM;gBAC5C,uBAAuB;YACzB;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,CAAC,QAAgB,OAAe;QACrD,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,QAAQ,SAAS,cAAc;QACrC,MAAM,MAAM,SAAS,YAAY;QACjC,MAAM,eAAe,aAAa,SAAS,CAAC,OAAO;QACnD,MAAM,cAAc,gBAAgB;QAEpC,oCAAoC;QACpC,eAAe;QAEf,MAAM,WACJ,aAAa,SAAS,CAAC,GAAG,SAC1B,SAAS,cAAc,QACvB,aAAa,SAAS,CAAC;QAEzB,qCAAqC;QACrC,gBAAgB;QAChB,cAAc;QAEd,yCAAyC;QACzC,WAAW;YACT,IAAI,cAAc;gBAChB,SAAS,iBAAiB,CAAC,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,YAAY,MAAM;YAC9F,OAAO;gBACL,SAAS,iBAAiB,CAAC,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,YAAY,MAAM;YAC9F;YACA,SAAS,KAAK;QAChB,GAAG;IACL;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,YAAY,CAAC,KAAK;QACjD,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;QAE7D,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,MAAM,WAAW,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAC9C,eAAe,IAAI,IAAI;gBACzB;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,CAAC,WAAW,IAAI,YAAY,OAAO,EAAE;YACnD,YAAY,OAAO,CAAC,KAAK;QAC3B;IACF,GAAG;QAAC,MAAM,MAAM,CAAC,WAAW;KAAC;IAE7B,kCAAkC;IAClC,MAAM,cAAc;QAClB,WAAW,sBAAsB;IACnC;IAEA,gCAAgC;IAChC,MAAM,eAAe,CAAC;QACpB,IAAI,YAAY,YAAY,OAAO,EAAE;YACnC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,YAAY,OAAO;YACrE,SAAS,WAAW,cAAc;QACpC;IACF;IAEA,wCAAwC;IACxC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,QAAQ,aAAa,KAAK,CAAC;QACjC,OAAO,MAAM,GAAG,CAAC,CAAC,GAAG,QAAU,QAAQ;IACzC,GAAG;QAAC;KAAa;IAEjB,+DAA+D;IAC/D,MAAM,aAAa,MAAM,QAAQ,CAAC,QAAQ,GAAG;IAC7C,MAAM,aAAa,IAAI,gBAAgB;IAEvC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,gBAAa;gBACZ,kBAAkB;gBAClB,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS,UAAU,MAAM,GAAG;gBAC5B,SAAS,UAAU,MAAM,GAAG;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,CAAC,WAAW,iBACvB,8OAAC;wBACC,WAAW,CAAC,gBAAgB,EAAE,aAAa,mCAAmC,IAAI;wBAClF,YAAY;wBACZ,aAAa;wBACb,QAAQ;;0CAER,8OAAC;gCACC,KAAK;gCACL,OAAO;gCACP,UAAU;gCACV,WAAW;gCACX,SAAS;gCACT,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,0GAA0G,EACpH,MAAM,QAAQ,CAAC,QAAQ,GAAG,wBAAwB,iBACnD,CAAC,EAAE,MAAM,QAAQ,CAAC,WAAW,GAAG,UAAU,OAAO;gCAClD,OAAO;oCACL,UAAU,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oCACxC,YAAY,GAAG,WAAW,EAAE,CAAC;oCAC7B,SAAS;oCACT,YAAY,GAAG,WAAW,EAAE,CAAC;oCAC7B,cAAc;oCACd,eAAe;gCACjB;gCACA,aAAY;gCACZ,YAAY;;;;;;4BAIb,MAAM,QAAQ,CAAC,WAAW,kBACzB,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY,GAAG,WAAW,EAAE,CAAC;gCAC/B;0CAEA,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,UAAU,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;wCACxC,YAAY,GAAG,WAAW,EAAE,CAAC;oCAC/B;8CAEC,YAAY,GAAG,CAAC,CAAC,wBAChB,8OAAC;4CAAkB,OAAO;gDAAE,QAAQ,GAAG,WAAW,EAAE,CAAC;4CAAC;sDACnD;2CADO;;;;;;;;;;;;;;;4BASjB,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAuC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC9F,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;4BAM9C,MAAM,MAAM,CAAC,SAAS,kBACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;6CAMxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAoC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC3F,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAM5B,MAAM,MAAM,CAAC,KAAK,kBACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAK,WAAU;8CAA0C,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1F", "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Preview/PreviewPane.tsx"], "sourcesContent": ["/**\n * Preview pane component for rendered markdown\n */\n\n'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { extractHeadings } from '@/utils/markdown';\n\ninterface PreviewPaneProps {\n  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;\n  scrollToPosition?: { scrollTop: number; scrollHeight: number; clientHeight: number } | null;\n}\n\nexport function PreviewPane({ onScroll, scrollToPosition }: PreviewPaneProps = {}) {\n  const { state, dispatch } = useApp();\n  const previewRef = useRef<HTMLDivElement>(null);\n  const [showToc, setShowToc] = useState(false);\n  const [headings, setHeadings] = useState<Array<{ level: number; text: string; id: string }>>([]);\n  const [isScrollSyncing, setIsScrollSyncing] = useState(false);\n\n  // Extract headings for table of contents\n  useEffect(() => {\n    if (state.editor.content) {\n      const extractedHeadings = extractHeadings(state.editor.content);\n      setHeadings(extractedHeadings);\n    } else {\n      setHeadings([]);\n    }\n  }, [state.editor.content]);\n\n  // Handle scroll synchronization\n  const handleScroll = () => {\n    if (!previewRef.current || isScrollSyncing) return;\n\n    if (onScroll) {\n      const { scrollTop, scrollHeight, clientHeight } = previewRef.current;\n      onScroll(scrollTop, scrollHeight, clientHeight);\n    }\n  };\n\n  // Sync scroll position from editor\n  useEffect(() => {\n    if (scrollToPosition && previewRef.current && !isScrollSyncing) {\n      setIsScrollSyncing(true);\n\n      const { scrollTop, scrollHeight, clientHeight } = scrollToPosition;\n      const previewElement = previewRef.current;\n\n      // Calculate proportional scroll position\n      const scrollRatio = scrollHeight > clientHeight ? scrollTop / (scrollHeight - clientHeight) : 0;\n      const targetScrollTop = scrollRatio * (previewElement.scrollHeight - previewElement.clientHeight);\n\n      previewElement.scrollTop = Math.max(0, targetScrollTop);\n\n      // Reset sync flag after a short delay\n      setTimeout(() => setIsScrollSyncing(false), 100);\n    }\n  }, [scrollToPosition, isScrollSyncing]);\n\n  // Handle anchor link clicks\n  const handleAnchorClick = (event: React.MouseEvent) => {\n    const target = event.target as HTMLElement;\n    if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {\n      event.preventDefault();\n      const id = target.getAttribute('href')?.substring(1);\n      if (id && previewRef.current) {\n        const element = previewRef.current.querySelector(`#${id}`);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    }\n  };\n\n  // Handle copy code button clicks\n  useEffect(() => {\n    const handleCopyCode = (event: Event) => {\n      const target = event.target as HTMLElement;\n      if (target.classList.contains('copy-code-btn')) {\n        const code = decodeURIComponent(target.getAttribute('data-code') || '');\n        navigator.clipboard.writeText(code).then(() => {\n          target.textContent = 'Copied!';\n          setTimeout(() => {\n            target.textContent = 'Copy';\n          }, 2000);\n        }).catch(() => {\n          target.textContent = 'Failed';\n          setTimeout(() => {\n            target.textContent = 'Copy';\n          }, 2000);\n        });\n      }\n    };\n\n    const previewElement = previewRef.current;\n    if (previewElement) {\n      previewElement.addEventListener('click', handleCopyCode);\n      return () => previewElement.removeEventListener('click', handleCopyCode);\n    }\n  }, [state.preview.htmlContent]);\n\n  const scrollToHeading = (id: string) => {\n    if (previewRef.current) {\n      const element = previewRef.current.querySelector(`#${id}`);\n      if (element) {\n        element.scrollIntoView({ behavior: 'smooth' });\n      }\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-white dark:bg-gray-900\">\n      {/* Preview header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Preview</h2>\n\n        <div className=\"flex items-center space-x-2\">\n          {/* Table of contents toggle */}\n          {headings.length > 0 && (\n            <button\n              onClick={() => setShowToc(!showToc)}\n              className={`p-2 rounded-md transition-colors ${\n                showToc\n                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600'\n                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n              title=\"Table of Contents\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Main content area */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Table of contents sidebar */}\n        {showToc && headings.length > 0 && (\n          <div className=\"w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 overflow-y-auto\">\n            <div className=\"p-4\">\n              <h3 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-3\">\n                Table of Contents\n              </h3>\n              <nav className=\"space-y-1\">\n                {headings.map((heading, index) => (\n                  <button\n                    key={index}\n                    onClick={() => scrollToHeading(heading.id)}\n                    className={`block w-full text-left text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors ${\n                      heading.level > 1 ? `ml-${(heading.level - 1) * 3}` : ''\n                    }`}\n                    style={{ paddingLeft: `${(heading.level - 1) * 12}px` }}\n                  >\n                    {heading.text}\n                  </button>\n                ))}\n              </nav>\n            </div>\n          </div>\n        )}\n\n        {/* Preview content */}\n        <div className=\"flex-1 relative\">\n          {state.editor.currentFile ? (\n            <div\n              ref={previewRef}\n              className=\"h-full overflow-y-auto p-6 print-full-width\"\n              onScroll={handleScroll}\n              onClick={handleAnchorClick}\n            >\n              {state.preview.isLoading ? (\n                <div className=\"flex items-center justify-center h-32\">\n                  <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n                    <p className=\"text-gray-600 dark:text-gray-400\">Rendering preview...</p>\n                  </div>\n                </div>\n              ) : state.preview.error ? (\n                <div className=\"flex items-center justify-center h-32\">\n                  <div className=\"text-center\">\n                    <svg className=\"w-12 h-12 text-red-500 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <p className=\"text-red-600 dark:text-red-400\">{state.preview.error}</p>\n                  </div>\n                </div>\n              ) : state.preview.htmlContent ? (\n                <div\n                  className=\"prose prose-lg dark:prose-invert max-w-none\"\n                  dangerouslySetInnerHTML={{ __html: state.preview.htmlContent }}\n                />\n              ) : (\n                <div className=\"flex items-center justify-center h-32 text-gray-500 dark:text-gray-400\">\n                  <div className=\"text-center\">\n                    <svg className=\"w-12 h-12 mx-auto mb-2 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                    </svg>\n                    <p>Start typing to see the preview</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n              <div className=\"text-center\">\n                <svg className=\"w-16 h-16 mx-auto mb-4 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                </svg>\n                <h3 className=\"text-lg font-medium mb-2\">No file selected</h3>\n                <p className=\"text-sm\">Select a file to see the preview</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Custom styles for preview */}\n      <style jsx global>{`\n        .prose {\n          color: inherit;\n        }\n\n        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {\n          position: relative;\n        }\n\n        .prose .anchor-link {\n          position: absolute;\n          left: -1.5rem;\n          opacity: 0;\n          transition: opacity 0.2s ease;\n          text-decoration: none;\n          color: #6b7280;\n        }\n\n        .prose h1:hover .anchor-link,\n        .prose h2:hover .anchor-link,\n        .prose h3:hover .anchor-link,\n        .prose h4:hover .anchor-link,\n        .prose h5:hover .anchor-link,\n        .prose h6:hover .anchor-link {\n          opacity: 1;\n        }\n\n        .prose .code-block-wrapper {\n          position: relative;\n          margin: 1rem 0;\n        }\n\n        .prose .code-block-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: #f3f4f6;\n          border: 1px solid #e5e7eb;\n          border-bottom: none;\n          border-radius: 0.375rem 0.375rem 0 0;\n          padding: 0.5rem 1rem;\n          font-size: 0.875rem;\n        }\n\n        .dark .prose .code-block-header {\n          background: #374151;\n          border-color: #4b5563;\n        }\n\n        .prose .code-language {\n          font-weight: 500;\n          color: #6b7280;\n        }\n\n        .dark .prose .code-language {\n          color: #9ca3af;\n        }\n\n        .prose .copy-code-btn {\n          background: #3b82f6;\n          color: white;\n          border: none;\n          border-radius: 0.25rem;\n          padding: 0.25rem 0.5rem;\n          font-size: 0.75rem;\n          cursor: pointer;\n          transition: background-color 0.2s ease;\n        }\n\n        .prose .copy-code-btn:hover {\n          background: #2563eb;\n        }\n\n        .prose .code-block-wrapper pre {\n          margin: 0;\n          border-radius: 0 0 0.375rem 0.375rem;\n          border: 1px solid #e5e7eb;\n          border-top: none;\n        }\n\n        .dark .prose .code-block-wrapper pre {\n          border-color: #4b5563;\n        }\n\n        .prose .table-wrapper {\n          overflow-x: auto;\n          margin: 1rem 0;\n        }\n\n        .prose .markdown-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .prose .markdown-table th,\n        .prose .markdown-table td {\n          border: 1px solid #e5e7eb;\n          padding: 0.5rem;\n          text-align: left;\n        }\n\n        .dark .prose .markdown-table th,\n        .dark .prose .markdown-table td {\n          border-color: #4b5563;\n        }\n\n        .prose .markdown-table th {\n          background: #f9fafb;\n          font-weight: 600;\n        }\n\n        .dark .prose .markdown-table th {\n          background: #374151;\n        }\n\n        /* Print styles */\n        @media print {\n          .prose {\n            max-width: none !important;\n          }\n\n          .prose .anchor-link {\n            display: none;\n          }\n\n          .prose .copy-code-btn {\n            display: none;\n          }\n\n          .prose .code-block-header {\n            background: #f9fafb !important;\n            color: #000 !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID;AACA;AACA;AAJA;;;;;;AAWO,SAAS,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAoB,GAAG,CAAC,CAAC;IAC/E,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IACjC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD,EAAE;IAC/F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;YACxB,MAAM,oBAAoB,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;YAC9D,YAAY;QACd,OAAO;YACL,YAAY,EAAE;QAChB;IACF,GAAG;QAAC,MAAM,MAAM,CAAC,OAAO;KAAC;IAEzB,gCAAgC;IAChC,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW,OAAO,IAAI,iBAAiB;QAE5C,IAAI,UAAU;YACZ,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,WAAW,OAAO;YACpE,SAAS,WAAW,cAAc;QACpC;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,WAAW,OAAO,IAAI,CAAC,iBAAiB;YAC9D,mBAAmB;YAEnB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG;YAClD,MAAM,iBAAiB,WAAW,OAAO;YAEzC,yCAAyC;YACzC,MAAM,cAAc,eAAe,eAAe,YAAY,CAAC,eAAe,YAAY,IAAI;YAC9F,MAAM,kBAAkB,cAAc,CAAC,eAAe,YAAY,GAAG,eAAe,YAAY;YAEhG,eAAe,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG;YAEvC,sCAAsC;YACtC,WAAW,IAAM,mBAAmB,QAAQ;QAC9C;IACF,GAAG;QAAC;QAAkB;KAAgB;IAEtC,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAI,OAAO,OAAO,KAAK,OAAO,OAAO,YAAY,CAAC,SAAS,WAAW,MAAM;YAC1E,MAAM,cAAc;YACpB,MAAM,KAAK,OAAO,YAAY,CAAC,SAAS,UAAU;YAClD,IAAI,MAAM,WAAW,OAAO,EAAE;gBAC5B,MAAM,UAAU,WAAW,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;gBACzD,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,kBAAkB;gBAC9C,MAAM,OAAO,mBAAmB,OAAO,YAAY,CAAC,gBAAgB;gBACpE,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;oBACvC,OAAO,WAAW,GAAG;oBACrB,WAAW;wBACT,OAAO,WAAW,GAAG;oBACvB,GAAG;gBACL,GAAG,KAAK,CAAC;oBACP,OAAO,WAAW,GAAG;oBACrB,WAAW;wBACT,OAAO,WAAW,GAAG;oBACvB,GAAG;gBACL;YACF;QACF;QAEA,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,eAAe,gBAAgB,CAAC,SAAS;YACzC,OAAO,IAAM,eAAe,mBAAmB,CAAC,SAAS;QAC3D;IACF,GAAG;QAAC,MAAM,OAAO,CAAC,WAAW;KAAC;IAE9B,MAAM,kBAAkB,CAAC;QACvB,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,UAAU,WAAW,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;YACzD,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;IACF;IAEA,qBACE,8OAAC;kDAAc;;0BAEb,8OAAC;0DAAc;;kCACb,8OAAC;kEAAa;kCAAsD;;;;;;kCAEpE,8OAAC;kEAAc;kCAEZ,SAAS,MAAM,GAAG,mBACjB,8OAAC;4BACC,SAAS,IAAM,WAAW,CAAC;4BAM3B,OAAM;sEALK,CAAC,iCAAiC,EAC3C,UACI,kDACA,4CACJ;sCAGF,cAAA,8OAAC;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0EAApD;0CACb,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/E,8OAAC;0DAAc;;oBAEZ,WAAW,SAAS,MAAM,GAAG,mBAC5B,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAA2D;;;;;;8CAGzE,8OAAC;8EAAc;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAEC,SAAS,IAAM,gBAAgB,QAAQ,EAAE;4CAIzC,OAAO;gDAAE,aAAa,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;4CAAC;sFAH3C,CAAC,4HAA4H,EACtI,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IACtD;sDAGD,QAAQ,IAAI;2CAPR;;;;;;;;;;;;;;;;;;;;;kCAgBjB,8OAAC;kEAAc;kCACZ,MAAM,MAAM,CAAC,WAAW,iBACvB,8OAAC;4BACC,KAAK;4BAEL,UAAU;4BACV,SAAS;sEAFC;sCAIT,MAAM,OAAO,CAAC,SAAS,iBACtB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;;;;;sDACf,8OAAC;sFAAY;sDAAmC;;;;;;;;;;;;;;;;uCAGlD,MAAM,OAAO,CAAC,KAAK,iBACrB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;4CAAoD,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sFAAhF;sDACb,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;sDAEvE,8OAAC;sFAAY;sDAAkC,MAAM,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;uCAGpE,MAAM,OAAO,CAAC,WAAW,iBAC3B,8OAAC;gCAEC,yBAAyB;oCAAE,QAAQ,MAAM,OAAO,CAAC,WAAW;gCAAC;0EADnD;;;;;qDAIZ,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;4CAAkD,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sFAA9E;;8DACb,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;8DACrE,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;sDAEvE,8OAAC;;sDAAE;;;;;;;;;;;;;;;;;;;;;iDAMX,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;wCAAkD,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kFAA9E;;0DACb,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;0DACrE,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;kDAEvE,8OAAC;kFAAa;kDAA2B;;;;;;kDACzC,8OAAC;kFAAY;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJvC", "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/StatusBar.tsx"], "sourcesContent": ["/**\n * Status bar component showing file info and editor stats\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { getWordCount, validateMarkdown } from '@/utils/markdown';\n\nexport function StatusBar() {\n  const { state } = useApp();\n\n  // Calculate word count and validation\n  const stats = useMemo(() => {\n    if (!state.editor.content) {\n      return {\n        words: 0,\n        characters: 0,\n        charactersNoSpaces: 0,\n        paragraphs: 0,\n        readingTime: 0,\n        isValid: true,\n        errors: []\n      };\n    }\n\n    const wordCount = getWordCount(state.editor.content);\n    const validation = validateMarkdown(state.editor.content);\n\n    return {\n      ...wordCount,\n      ...validation\n    };\n  }, [state.editor.content]);\n\n  const formatTime = (minutes: number): string => {\n    if (minutes < 1) return '< 1 min read';\n    if (minutes === 1) return '1 min read';\n    return `${minutes} min read`;\n  };\n\n  return (\n    <div className=\"h-6 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 text-xs text-gray-600 dark:text-gray-400 no-print\">\n      {/* Left section - File info */}\n      <div className=\"flex items-center space-x-4\">\n        {state.editor.currentFile ? (\n          <>\n            <span className=\"flex items-center space-x-1\">\n              <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <span>{state.editor.currentFile.name}</span>\n            </span>\n\n            {state.editor.isModified && (\n              <span className=\"flex items-center space-x-1 text-orange-600 dark:text-orange-400\">\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                <span>Modified</span>\n              </span>\n            )}\n\n            {state.settings.autoSave && (\n              <span className=\"flex items-center space-x-1 text-green-600 dark:text-green-400\">\n                <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span>Auto-save</span>\n              </span>\n            )}\n          </>\n        ) : (\n          <span>No file open</span>\n        )}\n      </div>\n\n      {/* Center section - Document stats */}\n      <div className=\"flex items-center space-x-4\">\n        {state.editor.content && (\n          <>\n            <span>{stats.words} words</span>\n            <span>•</span>\n            <span>{stats.characters} characters</span>\n            <span>•</span>\n            <span>{stats.paragraphs} paragraphs</span>\n            <span>•</span>\n            <span>{formatTime(stats.readingTime)}</span>\n          </>\n        )}\n      </div>\n\n      {/* Right section - Editor info */}\n      <div className=\"flex items-center space-x-4\">\n        {/* Validation status */}\n        {state.editor.content && (\n          <div className=\"flex items-center space-x-1\">\n            {stats.isValid ? (\n              <>\n                <svg className=\"w-3 h-3 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-green-600 dark:text-green-400\">Valid</span>\n              </>\n            ) : (\n              <>\n                <svg className=\"w-3 h-3 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span className=\"text-red-600 dark:text-red-400\">\n                  {stats.errors.filter(e => e.type === 'error').length} errors\n                </span>\n              </>\n            )}\n          </div>\n        )}\n\n        {/* Cursor position */}\n        <span>\n          Ln {state.editor.cursorPosition.line}, Col {state.editor.cursorPosition.column}\n        </span>\n\n        {/* Theme indicator */}\n        <span className=\"flex items-center space-x-1\">\n          {state.settings.theme === 'light' ? (\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n            </svg>\n          ) : (\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n            </svg>\n          )}\n          <span className=\"capitalize\">{state.settings.theme}</span>\n        </span>\n\n        {/* Preview mode */}\n        <span className=\"capitalize\">{state.settings.previewMode}</span>\n\n        {/* Loading indicators */}\n        {(state.editor.isLoading || state.preview.isLoading || state.fileManager.isLoading) && (\n          <div className=\"flex items-center space-x-1\">\n            <div className=\"w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin\"></div>\n            <span>Loading...</span>\n          </div>\n        )}\n\n        {/* Error indicators */}\n        {(state.editor.error || state.preview.error || state.fileManager.error) && (\n          <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span>Error</span>\n          </div>\n        )}\n      </div>\n\n      {/* Tooltip for validation errors */}\n      {!stats.isValid && stats.errors.length > 0 && (\n        <div className=\"fixed bottom-8 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-3 max-w-sm z-50\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Validation Issues</h4>\n          <div className=\"space-y-1\">\n            {stats.errors.slice(0, 5).map((error, index) => (\n              <div key={index} className=\"text-xs\">\n                <span className={`font-medium ${error.type === 'error' ? 'text-red-600' : 'text-yellow-600'}`}>\n                  Line {error.line}:\n                </span>\n                <span className=\"ml-1 text-gray-600 dark:text-gray-400\">\n                  {error.message}\n                </span>\n              </div>\n            ))}\n            {stats.errors.length > 5 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                ... and {stats.errors.length - 5} more\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAEvB,sCAAsC;IACtC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE;YACzB,OAAO;gBACL,OAAO;gBACP,YAAY;gBACZ,oBAAoB;gBACpB,YAAY;gBACZ,aAAa;gBACb,SAAS;gBACT,QAAQ,EAAE;YACZ;QACF;QAEA,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;QACnD,MAAM,aAAa,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;QAExD,OAAO;YACL,GAAG,SAAS;YACZ,GAAG,UAAU;QACf;IACF,GAAG;QAAC,MAAM,MAAM,CAAC,OAAO;KAAC;IAEzB,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,YAAY,GAAG,OAAO;QAC1B,OAAO,GAAG,QAAQ,SAAS,CAAC;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,CAAC,WAAW,iBACvB;;sCACE,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAM,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI;;;;;;;;;;;;wBAGrC,MAAM,MAAM,CAAC,UAAU,kBACtB,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;wBAIT,MAAM,QAAQ,CAAC,QAAQ,kBACtB,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAK;;;;;;;;;;;;;iDAKZ,8OAAC;8BAAK;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,CAAC,OAAO,kBACnB;;sCACE,8OAAC;;gCAAM,MAAM,KAAK;gCAAC;;;;;;;sCACnB,8OAAC;sCAAK;;;;;;sCACN,8OAAC;;gCAAM,MAAM,UAAU;gCAAC;;;;;;;sCACxB,8OAAC;sCAAK;;;;;;sCACN,8OAAC;;gCAAM,MAAM,UAAU;gCAAC;;;;;;;sCACxB,8OAAC;sCAAK;;;;;;sCACN,8OAAC;sCAAM,WAAW,MAAM,WAAW;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;;oBAEZ,MAAM,MAAM,CAAC,OAAO,kBACnB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,OAAO,iBACZ;;8CACE,8OAAC;oCAAI,WAAU;oCAAyB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAChF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;yDAGvD;;8CACE,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;oCAAK,WAAU;;wCACb,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;kCAQ/D,8OAAC;;4BAAK;4BACA,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI;4BAAC;4BAAO,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM;;;;;;;kCAIhF,8OAAC;wBAAK,WAAU;;4BACb,MAAM,QAAQ,CAAC,KAAK,KAAK,wBACxB,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;qDAGvE,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAGzE,8OAAC;gCAAK,WAAU;0CAAc,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;kCAIpD,8OAAC;wBAAK,WAAU;kCAAc,MAAM,QAAQ,CAAC,WAAW;;;;;;oBAGvD,CAAC,MAAM,MAAM,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,WAAW,CAAC,SAAS,mBAChF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;oBAKT,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,MAAM,WAAW,CAAC,KAAK,mBACpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,CAAC,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,mBACvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACpC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,MAAM,IAAI,KAAK,UAAU,iBAAiB,mBAAmB;;gDAAE;gDACvF,MAAM,IAAI;gDAAC;;;;;;;sDAEnB,8OAAC;4CAAK,WAAU;sDACb,MAAM,OAAO;;;;;;;mCALR;;;;;4BASX,MAAM,MAAM,CAAC,MAAM,GAAG,mBACrB,8OAAC;gCAAI,WAAU;;oCAA2C;oCAC/C,MAAM,MAAM,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 3901, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["/**\n * Main layout component with resizable panes\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Allotment } from 'allotment';\nimport { useApp } from '@/contexts/AppContext';\nimport { Header } from './Header';\nimport { Sidebar } from './Sidebar';\nimport { EditorPane } from '../Editor/EditorPane';\nimport { PreviewPane } from '../Preview/PreviewPane';\nimport { StatusBar } from './StatusBar';\nimport 'allotment/dist/style.css';\n\ninterface MainLayoutProps {\n  children?: React.ReactNode;\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const { state } = useApp();\n  const [sidebarWidth, setSidebarWidth] = useState(300);\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [editorScrollPosition, setEditorScrollPosition] = useState<{\n    scrollTop: number;\n    scrollHeight: number;\n    clientHeight: number;\n  } | null>(null);\n  const [previewScrollPosition, setPreviewScrollPosition] = useState<{\n    scrollTop: number;\n    scrollHeight: number;\n    clientHeight: number;\n  } | null>(null);\n\n  // Initialize layout\n  useEffect(() => {\n    // Load saved layout preferences\n    const savedSidebarWidth = localStorage.getItem('mdeditor_sidebar_width');\n    const savedSidebarCollapsed = localStorage.getItem('mdeditor_sidebar_collapsed');\n\n    if (savedSidebarWidth) {\n      setSidebarWidth(parseInt(savedSidebarWidth, 10));\n    }\n\n    if (savedSidebarCollapsed) {\n      setIsSidebarCollapsed(savedSidebarCollapsed === 'true');\n    }\n\n    setIsLoading(false);\n  }, []);\n\n  // Save layout preferences\n  useEffect(() => {\n    if (!isLoading) {\n      localStorage.setItem('mdeditor_sidebar_width', sidebarWidth.toString());\n      localStorage.setItem('mdeditor_sidebar_collapsed', isSidebarCollapsed.toString());\n    }\n  }, [sidebarWidth, isSidebarCollapsed, isLoading]);\n\n  const handleSidebarToggle = () => {\n    setIsSidebarCollapsed(!isSidebarCollapsed);\n  };\n\n  // Handle scroll synchronization\n  const handleEditorScroll = (scrollTop: number, scrollHeight: number, clientHeight: number) => {\n    if (state.settings.viewMode === 'split') {\n      setPreviewScrollPosition({ scrollTop, scrollHeight, clientHeight });\n    }\n  };\n\n  const handlePreviewScroll = (scrollTop: number, scrollHeight: number, clientHeight: number) => {\n    if (state.settings.viewMode === 'split') {\n      setEditorScrollPosition({ scrollTop, scrollHeight, clientHeight });\n    }\n  };\n\n  const renderMainContent = () => {\n    const { previewMode } = state.settings;\n\n    switch (previewMode) {\n      case 'edit':\n        return <EditorPane onScroll={handleEditorScroll} />;\n      case 'preview':\n        return <PreviewPane onScroll={handlePreviewScroll} />;\n      case 'side':\n      default:\n        return (\n          <Allotment\n            defaultSizes={[50, 50]}\n            minSize={200}\n            resizerStyle={{\n              width: '4px',\n              backgroundColor: 'var(--border-color)',\n              cursor: 'col-resize'\n            }}\n          >\n            <Allotment.Pane>\n              <EditorPane onScroll={handleEditorScroll} />\n            </Allotment.Pane>\n            <Allotment.Pane>\n              <PreviewPane\n                onScroll={handlePreviewScroll}\n                scrollToPosition={previewScrollPosition}\n              />\n            </Allotment.Pane>\n          </Allotment>\n        );\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <div className=\"relative\">\n            <div className=\"w-16 h-16 mx-auto mb-4\">\n              <div className=\"absolute inset-0 rounded-full border-4 border-blue-200 dark:border-blue-800\"></div>\n              <div className=\"absolute inset-0 rounded-full border-4 border-transparent border-t-blue-600 animate-spin\"></div>\n              <div className=\"absolute inset-2 rounded-full border-2 border-transparent border-t-blue-400 animate-spin animation-delay-150\"></div>\n            </div>\n          </div>\n          <p className=\"text-gray-600 dark:text-gray-400 animate-pulse\">Loading Markdown Editor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`h-screen flex flex-col ${state.settings.theme === 'dark' ? 'dark' : ''}`}>\n      {/* Header */}\n      <Header onSidebarToggle={handleSidebarToggle} />\n\n      {/* Main content area */}\n      <div className=\"flex-1 flex overflow-hidden relative\">\n        {/* Sidebar with smooth animations */}\n        <div\n          className={`h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out ${\n            isSidebarCollapsed ? 'w-0 opacity-0' : `opacity-100`\n          }`}\n          style={{\n            width: isSidebarCollapsed ? '0px' : `${sidebarWidth}px`,\n            minWidth: isSidebarCollapsed ? '0px' : '200px'\n          }}\n        >\n          <div className={`h-full transition-opacity duration-200 ${isSidebarCollapsed ? 'opacity-0' : 'opacity-100'}`}>\n            <Sidebar />\n          </div>\n        </div>\n\n        {/* Resizer */}\n        {!isSidebarCollapsed && (\n          <div\n            className=\"w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 cursor-col-resize transition-colors duration-200 flex-shrink-0\"\n            onMouseDown={(e) => {\n              const startX = e.clientX;\n              const startWidth = sidebarWidth;\n\n              const handleMouseMove = (e: MouseEvent) => {\n                const newWidth = Math.max(200, Math.min(600, startWidth + (e.clientX - startX)));\n                setSidebarWidth(newWidth);\n              };\n\n              const handleMouseUp = () => {\n                document.removeEventListener('mousemove', handleMouseMove);\n                document.removeEventListener('mouseup', handleMouseUp);\n              };\n\n              document.addEventListener('mousemove', handleMouseMove);\n              document.addEventListener('mouseup', handleMouseUp);\n            }}\n          />\n        )}\n\n        {/* Main editor/preview area */}\n        <div className={`flex-1 h-full bg-white dark:bg-gray-900 transition-all duration-300 ease-in-out ${\n          isSidebarCollapsed ? 'ml-0' : ''\n        }`}>\n          {renderMainContent()}\n        </div>\n      </div>\n\n      {/* Status bar */}\n      <StatusBar />\n\n      {/* Custom styles */}\n      <style jsx global>{`\n        :root {\n          --border-color: #e5e7eb;\n          --sidebar-bg: #f9fafb;\n          --sidebar-border: #e5e7eb;\n        }\n\n        .dark {\n          --border-color: #374151;\n          --sidebar-bg: #1f2937;\n          --sidebar-border: #374151;\n        }\n\n        .allotment > .allotment-pane {\n          overflow: hidden;\n        }\n\n        .allotment-separator {\n          background-color: var(--border-color) !important;\n          transition: background-color 0.2s ease;\n        }\n\n        .allotment-separator:hover {\n          background-color: #3b82f6 !important;\n        }\n\n        /* Responsive design */\n        @media (max-width: 768px) {\n          .allotment-separator {\n            width: 2px !important;\n          }\n        }\n\n        /* Scrollbar styling */\n        ::-webkit-scrollbar {\n          width: 8px;\n          height: 8px;\n        }\n\n        ::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        ::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 4px;\n        }\n\n        ::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n\n        .dark ::-webkit-scrollbar-thumb {\n          background: #4b5563;\n        }\n\n        .dark ::-webkit-scrollbar-thumb:hover {\n          background: #6b7280;\n        }\n\n        /* Focus styles */\n        .allotment:focus-within .allotment-separator {\n          background-color: #3b82f6 !important;\n        }\n\n        /* Animation for smooth transitions */\n        .transition-layout {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        /* Print styles */\n        @media print {\n          .no-print {\n            display: none !important;\n          }\n\n          .print-full-width {\n            width: 100% !important;\n            max-width: none !important;\n          }\n        }\n\n        /* High contrast mode support */\n        @media (prefers-contrast: high) {\n          .allotment-separator {\n            background-color: #000 !important;\n          }\n\n          .dark .allotment-separator {\n            background-color: #fff !important;\n          }\n        }\n\n        /* Reduced motion support */\n        @media (prefers-reduced-motion: reduce) {\n          .transition-layout,\n          .allotment-separator {\n            transition: none !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;;;AAgBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrD;IACV,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIvD;IAEV,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAC/C,MAAM,wBAAwB,aAAa,OAAO,CAAC;QAEnD,IAAI,mBAAmB;YACrB,gBAAgB,SAAS,mBAAmB;QAC9C;QAEA,IAAI,uBAAuB;YACzB,sBAAsB,0BAA0B;QAClD;QAEA,aAAa;IACf,GAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,aAAa,OAAO,CAAC,0BAA0B,aAAa,QAAQ;YACpE,aAAa,OAAO,CAAC,8BAA8B,mBAAmB,QAAQ;QAChF;IACF,GAAG;QAAC;QAAc;QAAoB;KAAU;IAEhD,MAAM,sBAAsB;QAC1B,sBAAsB,CAAC;IACzB;IAEA,gCAAgC;IAChC,MAAM,qBAAqB,CAAC,WAAmB,cAAsB;QACnE,IAAI,MAAM,QAAQ,CAAC,QAAQ,KAAK,SAAS;YACvC,yBAAyB;gBAAE;gBAAW;gBAAc;YAAa;QACnE;IACF;IAEA,MAAM,sBAAsB,CAAC,WAAmB,cAAsB;QACpE,IAAI,MAAM,QAAQ,CAAC,QAAQ,KAAK,SAAS;YACvC,wBAAwB;gBAAE;gBAAW;gBAAc;YAAa;QAClE;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;QAEtC,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0IAAA,CAAA,aAAU;oBAAC,UAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,4IAAA,CAAA,cAAW;oBAAC,UAAU;;;;;;YAChC,KAAK;YACL;gBACE,qBACE,8OAAC,4IAAA,CAAA,YAAS;oBACR,cAAc;wBAAC;wBAAI;qBAAG;oBACtB,SAAS;oBACT,cAAc;wBACZ,OAAO;wBACP,iBAAiB;wBACjB,QAAQ;oBACV;;sCAEA,8OAAC,4IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;gCAAC,UAAU;;;;;;;;;;;sCAExB,8OAAC,4IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,8OAAC,4IAAA,CAAA,cAAW;gCACV,UAAU;gCACV,kBAAkB;;;;;;;;;;;;;;;;;QAK9B;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAE,WAAU;kCAAiD;;;;;;;;;;;;;;;;;IAItE;IAEA,qBACE,8OAAC;kDAAe,CAAC,uBAAuB,EAAE,MAAM,QAAQ,CAAC,KAAK,KAAK,SAAS,SAAS,IAAI;;0BAEvF,8OAAC,sIAAA,CAAA,SAAM;gBAAC,iBAAiB;;;;;;0BAGzB,8OAAC;0DAAc;;kCAEb,8OAAC;wBAIC,OAAO;4BACL,OAAO,qBAAqB,QAAQ,GAAG,aAAa,EAAE,CAAC;4BACvD,UAAU,qBAAqB,QAAQ;wBACzC;kEANW,CAAC,yHAAyH,EACnI,qBAAqB,kBAAkB,CAAC,WAAW,CAAC,EACpD;kCAMF,cAAA,8OAAC;sEAAe,CAAC,uCAAuC,EAAE,qBAAqB,cAAc,eAAe;sCAC1G,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;;;;;;oBAKX,CAAC,oCACA,8OAAC;wBAEC,aAAa,CAAC;4BACZ,MAAM,SAAS,EAAE,OAAO;4BACxB,MAAM,aAAa;4BAEnB,MAAM,kBAAkB,CAAC;gCACvB,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,CAAC,EAAE,OAAO,GAAG,MAAM;gCAC7E,gBAAgB;4BAClB;4BAEA,MAAM,gBAAgB;gCACpB,SAAS,mBAAmB,CAAC,aAAa;gCAC1C,SAAS,mBAAmB,CAAC,WAAW;4BAC1C;4BAEA,SAAS,gBAAgB,CAAC,aAAa;4BACvC,SAAS,gBAAgB,CAAC,WAAW;wBACvC;kEAjBU;;;;;;kCAsBd,8OAAC;kEAAe,CAAC,gFAAgF,EAC/F,qBAAqB,SAAS,IAC9B;kCACC;;;;;;;;;;;;0BAKL,8OAAC,yIAAA,CAAA,YAAS;;;;;;;;;;;;;;;AA0GhB", "debugId": null}}]}