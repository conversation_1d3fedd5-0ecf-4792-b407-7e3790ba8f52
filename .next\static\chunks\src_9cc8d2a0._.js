(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/types/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Core type definitions for the Markdown Editor application
 */ __turbopack_context__.s({
    "EditorError": (()=>EditorError),
    "ExportError": (()=>ExportError),
    "FileSystemError": (()=>FileSystemError)
});
class EditorError extends Error {
    code;
    details;
    constructor(message, code, details){
        super(message), this.code = code, this.details = details;
        this.name = 'EditorError';
    }
}
class FileSystemError extends EditorError {
    operation;
    constructor(message, operation, details){
        super(message, 'FILESYSTEM_ERROR', details), this.operation = operation;
        this.name = 'FileSystemError';
    }
}
class ExportError extends EditorError {
    format;
    constructor(message, format, details){
        super(message, 'EXPORT_ERROR', details), this.format = format;
        this.name = 'ExportError';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/file.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * File system utilities
 */ __turbopack_context__.s({
    "createDirectory": (()=>createDirectory),
    "createMarkdownFile": (()=>createMarkdownFile),
    "debounce": (()=>debounce),
    "downloadFile": (()=>downloadFile),
    "formatDate": (()=>formatDate),
    "formatFileSize": (()=>formatFileSize),
    "generateFileId": (()=>generateFileId),
    "getFileExtension": (()=>getFileExtension),
    "isMarkdownFile": (()=>isMarkdownFile),
    "localStorage": (()=>localStorage),
    "readFileFromInput": (()=>readFileFromInput),
    "throttle": (()=>throttle),
    "validateFilename": (()=>validateFilename)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript)");
;
function generateFileId() {
    return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}
function getFileExtension(filename) {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.slice(lastDot + 1).toLowerCase();
}
function isMarkdownFile(filename) {
    const extension = getFileExtension(filename);
    return [
        'md',
        'markdown',
        'mdown',
        'mkd',
        'mdx'
    ].includes(extension);
}
function validateFilename(filename) {
    if (!filename.trim()) {
        return {
            isValid: false,
            error: 'Filename cannot be empty'
        };
    }
    if (filename.length > 255) {
        return {
            isValid: false,
            error: 'Filename is too long (max 255 characters)'
        };
    }
    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(filename)) {
        return {
            isValid: false,
            error: 'Filename contains invalid characters'
        };
    }
    // Check for reserved names (Windows)
    const reservedNames = [
        'CON',
        'PRN',
        'AUX',
        'NUL',
        'COM1',
        'COM2',
        'COM3',
        'COM4',
        'COM5',
        'COM6',
        'COM7',
        'COM8',
        'COM9',
        'LPT1',
        'LPT2',
        'LPT3',
        'LPT4',
        'LPT5',
        'LPT6',
        'LPT7',
        'LPT8',
        'LPT9'
    ];
    const nameWithoutExt = filename.split('.')[0].toUpperCase();
    if (reservedNames.includes(nameWithoutExt)) {
        return {
            isValid: false,
            error: 'Filename is reserved by the system'
        };
    }
    return {
        isValid: true
    };
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = [
        'B',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}
function formatDate(date) {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays === 0) {
        return `Today at ${date.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        })}`;
    } else if (diffDays === 1) {
        return `Yesterday at ${date.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        })}`;
    } else if (diffDays < 7) {
        return `${diffDays} days ago`;
    } else {
        return date.toLocaleDateString();
    }
}
function createMarkdownFile(name, content = '', parentId) {
    const validation = validateFilename(name);
    if (!validation.isValid) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"](validation.error, 'create');
    }
    // Ensure .md extension
    const filename = name.endsWith('.md') ? name : `${name}.md`;
    return {
        id: generateFileId(),
        name: filename,
        content,
        path: parentId ? `${parentId}/${filename}` : filename,
        lastModified: new Date(),
        size: new Blob([
            content
        ]).size,
        isDirectory: false,
        parentId
    };
}
function createDirectory(name, parentId) {
    const validation = validateFilename(name);
    if (!validation.isValid) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"](validation.error, 'create');
    }
    return {
        id: generateFileId(),
        name,
        content: '',
        path: parentId ? `${parentId}/${name}` : name,
        lastModified: new Date(),
        size: 0,
        isDirectory: true,
        parentId
    };
}
const localStorage = {
    /**
   * Save files to local storage
   */ saveFiles (files) {
        try {
            const serialized = JSON.stringify(files, (key, value)=>{
                if (key === 'lastModified' && value instanceof Date) {
                    return value.toISOString();
                }
                return value;
            });
            window.localStorage.setItem('mdeditor_files', serialized);
        } catch (error) {
            console.error('Failed to save files to localStorage:', error);
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"]('Failed to save files', 'save');
        }
    },
    /**
   * Load files from local storage
   */ loadFiles () {
        try {
            const stored = window.localStorage.getItem('mdeditor_files');
            if (!stored) return [];
            const parsed = JSON.parse(stored);
            return parsed.map((file)=>({
                    ...file,
                    lastModified: new Date(file.lastModified)
                }));
        } catch (error) {
            console.error('Failed to load files from localStorage:', error);
            return [];
        }
    },
    /**
   * Save editor settings
   */ saveSettings (settings) {
        try {
            window.localStorage.setItem('mdeditor_settings', JSON.stringify(settings));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    },
    /**
   * Load editor settings
   */ loadSettings () {
        try {
            const stored = window.localStorage.getItem('mdeditor_settings');
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.error('Failed to load settings:', error);
            return null;
        }
    },
    /**
   * Clear all stored data
   */ clear () {
        try {
            window.localStorage.removeItem('mdeditor_files');
            window.localStorage.removeItem('mdeditor_settings');
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
        }
    }
};
function downloadFile(content, filename, mimeType = 'text/plain') {
    try {
        const blob = new Blob([
            content
        ], {
            type: mimeType
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Clean up the URL object
        setTimeout(()=>URL.revokeObjectURL(url), 100);
    } catch (error) {
        console.error('Failed to download file:', error);
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"]('Failed to download file', 'download');
    }
}
function readFileFromInput(file) {
    return new Promise((resolve, reject)=>{
        if (!isMarkdownFile(file.name)) {
            reject(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"]('File is not a markdown file', 'read'));
            return;
        }
        const reader = new FileReader();
        reader.onload = (event)=>{
            const content = event.target?.result;
            resolve(content);
        };
        reader.onerror = ()=>{
            reject(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FileSystemError"]('Failed to read file', 'read'));
        };
        reader.readAsText(file);
    });
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/markdown.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Markdown processing utilities
 */ __turbopack_context__.s({
    "extractHeadings": (()=>extractHeadings),
    "formatMarkdown": (()=>formatMarkdown),
    "getWordCount": (()=>getWordCount),
    "markdownToHtml": (()=>markdownToHtml),
    "validateMarkdown": (()=>validateMarkdown)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$marked$2f$lib$2f$marked$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/marked/lib/marked.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript)");
;
;
// Configure marked with custom renderer
const renderer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$marked$2f$lib$2f$marked$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marked"].Renderer();
// Custom heading renderer with anchor links
renderer.heading = function(text, level) {
    const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-');
    return `
    <h${level} id="${escapedText}">
      <a href="#${escapedText}" class="anchor-link">#</a>
      ${text}
    </h${level}>
  `;
};
// Custom code block renderer with syntax highlighting support
renderer.code = function(code, language) {
    const validLanguage = language && language.match(/^[a-zA-Z0-9_+-]*$/);
    const langClass = validLanguage ? `language-${language}` : '';
    return `
    <div class="code-block-wrapper">
      <div class="code-block-header">
        <span class="code-language">${language || 'text'}</span>
        <button class="copy-code-btn" data-code="${encodeURIComponent(code)}">
          Copy
        </button>
      </div>
      <pre><code class="${langClass}">${code}</code></pre>
    </div>
  `;
};
// Custom table renderer with responsive wrapper
renderer.table = function(header, body) {
    return `
    <div class="table-wrapper">
      <table class="markdown-table">
        <thead>${header}</thead>
        <tbody>${body}</tbody>
      </table>
    </div>
  `;
};
// Custom link renderer with external link handling
renderer.link = function(href, title, text) {
    const isExternal = href.startsWith('http') || href.startsWith('//');
    const target = isExternal ? 'target="_blank" rel="noopener noreferrer"' : '';
    const titleAttr = title ? `title="${title}"` : '';
    return `<a href="${href}" ${target} ${titleAttr}>${text}</a>`;
};
// Configure marked options
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$marked$2f$lib$2f$marked$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marked"].setOptions({
    renderer,
    gfm: true,
    breaks: true,
    pedantic: false,
    sanitize: false,
    smartLists: true,
    smartypants: true
});
function markdownToHtml(markdown) {
    try {
        // Convert markdown to HTML
        const rawHtml = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$marked$2f$lib$2f$marked$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marked"])(markdown);
        // Sanitize HTML to prevent XSS attacks
        const cleanHtml = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].sanitize(rawHtml, {
            ALLOWED_TAGS: [
                'h1',
                'h2',
                'h3',
                'h4',
                'h5',
                'h6',
                'p',
                'br',
                'strong',
                'em',
                'u',
                's',
                'del',
                'ins',
                'a',
                'img',
                'video',
                'audio',
                'ul',
                'ol',
                'li',
                'blockquote',
                'pre',
                'code',
                'table',
                'thead',
                'tbody',
                'tr',
                'th',
                'td',
                'div',
                'span',
                'hr',
                'details',
                'summary'
            ],
            ALLOWED_ATTR: [
                'href',
                'title',
                'alt',
                'src',
                'width',
                'height',
                'class',
                'id',
                'data-*',
                'target',
                'rel',
                'controls',
                'autoplay',
                'loop',
                'muted'
            ],
            ALLOW_DATA_ATTR: true
        });
        return cleanHtml;
    } catch (error) {
        console.error('Error converting markdown to HTML:', error);
        return '<p>Error rendering markdown content</p>';
    }
}
function extractHeadings(markdown) {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings = [];
    let match;
    while((match = headingRegex.exec(markdown)) !== null){
        const level = match[1].length;
        const text = match[2].trim();
        const id = text.toLowerCase().replace(/[^\w]+/g, '-');
        headings.push({
            level,
            text,
            id
        });
    }
    return headings;
}
function getWordCount(markdown) {
    // Remove markdown syntax for accurate word count
    const plainText = markdown.replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/^\s*\|.*\|$/gm, '') // Remove tables
    .replace(/^\s*[-=]{3,}$/gm, '') // Remove horizontal rules
    .trim();
    const words = plainText.split(/\s+/).filter((word)=>word.length > 0).length;
    const characters = plainText.length;
    const charactersNoSpaces = plainText.replace(/\s/g, '').length;
    const paragraphs = plainText.split(/\n\s*\n/).filter((p)=>p.trim().length > 0).length;
    // Average reading speed: 200 words per minute
    const readingTime = Math.ceil(words / 200);
    return {
        words,
        characters,
        charactersNoSpaces,
        paragraphs,
        readingTime
    };
}
function validateMarkdown(markdown) {
    const errors = [];
    const lines = markdown.split('\n');
    lines.forEach((line, index)=>{
        const lineNumber = index + 1;
        // Check for unmatched brackets
        const openBrackets = (line.match(/\[/g) || []).length;
        const closeBrackets = (line.match(/\]/g) || []).length;
        if (openBrackets !== closeBrackets) {
            errors.push({
                line: lineNumber,
                message: 'Unmatched square brackets',
                type: 'warning'
            });
        }
        // Check for unmatched parentheses in links
        const openParens = (line.match(/\(/g) || []).length;
        const closeParens = (line.match(/\)/g) || []).length;
        if (openParens !== closeParens) {
            errors.push({
                line: lineNumber,
                message: 'Unmatched parentheses',
                type: 'warning'
            });
        }
        // Check for malformed links
        const linkRegex = /\[([^\]]*)\]\(([^)]*)\)/g;
        let linkMatch;
        while((linkMatch = linkRegex.exec(line)) !== null){
            const linkText = linkMatch[1];
            const linkUrl = linkMatch[2];
            if (!linkText.trim()) {
                errors.push({
                    line: lineNumber,
                    message: 'Link has empty text',
                    type: 'warning'
                });
            }
            if (!linkUrl.trim()) {
                errors.push({
                    line: lineNumber,
                    message: 'Link has empty URL',
                    type: 'error'
                });
            }
        }
        // Check for malformed images
        const imageRegex = /!\[([^\]]*)\]\(([^)]*)\)/g;
        let imageMatch;
        while((imageMatch = imageRegex.exec(line)) !== null){
            const altText = imageMatch[1];
            const imageUrl = imageMatch[2];
            if (!imageUrl.trim()) {
                errors.push({
                    line: lineNumber,
                    message: 'Image has empty URL',
                    type: 'error'
                });
            }
        }
    });
    return {
        isValid: errors.filter((e)=>e.type === 'error').length === 0,
        errors
    };
}
function formatMarkdown(markdown) {
    return markdown.replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .replace(/[ \t]+$/gm, '') // Remove trailing whitespace
    .replace(/^[ \t]+/gm, (match)=>match.replace(/\t/g, '  ')) // Convert tabs to spaces
    .trim();
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AppContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Main application context for state management
 */ __turbopack_context__.s({
    "AppProvider": (()=>AppProvider),
    "useApp": (()=>useApp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/file.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$markdown$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/markdown.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
// Initial state
const initialState = {
    editor: {
        currentFile: null,
        content: '',
        isModified: false,
        isLoading: false,
        error: null,
        cursorPosition: {
            line: 1,
            column: 1
        }
    },
    preview: {
        htmlContent: '',
        isLoading: false,
        error: null,
        scrollPosition: 0
    },
    fileManager: {
        currentDirectory: '/',
        selectedFiles: [],
        files: [],
        isLoading: false,
        error: null
    },
    settings: {
        theme: 'light',
        fontSize: 14,
        lineNumbers: true,
        wordWrap: true,
        previewMode: 'side',
        autoSave: true,
        autoSaveInterval: 30000 // 30 seconds
    },
    collaboration: {
        isEnabled: false,
        users: [],
        roomId: null,
        isConnected: false
    },
    cloudProviders: []
};
// Reducers
function editorReducer(state, action) {
    switch(action.type){
        case 'SET_CONTENT':
            return {
                ...state,
                content: action.payload,
                isModified: true
            };
        case 'SET_CURRENT_FILE':
            return {
                ...state,
                currentFile: action.payload,
                content: action.payload?.content || '',
                isModified: false
            };
        case 'SET_MODIFIED':
            return {
                ...state,
                isModified: action.payload
            };
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        case 'SET_CURSOR_POSITION':
            return {
                ...state,
                cursorPosition: action.payload
            };
        default:
            return state;
    }
}
function fileManagerReducer(state, action) {
    switch(action.type){
        case 'SET_FILES':
            return {
                ...state,
                files: action.payload
            };
        case 'ADD_FILE':
            return {
                ...state,
                files: [
                    ...state.files,
                    action.payload
                ]
            };
        case 'UPDATE_FILE':
            return {
                ...state,
                files: state.files.map((file)=>file.id === action.payload.id ? action.payload : file)
            };
        case 'DELETE_FILE':
            return {
                ...state,
                files: state.files.filter((file)=>file.id !== action.payload)
            };
        case 'SET_CURRENT_DIRECTORY':
            return {
                ...state,
                currentDirectory: action.payload
            };
        case 'SET_SELECTED_FILES':
            return {
                ...state,
                selectedFiles: action.payload
            };
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        default:
            return state;
    }
}
function previewReducer(state, action) {
    switch(action.type){
        case 'SET_HTML_CONTENT':
            return {
                ...state,
                htmlContent: action.payload
            };
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        case 'SET_SCROLL_POSITION':
            return {
                ...state,
                scrollPosition: action.payload
            };
        default:
            return state;
    }
}
function settingsReducer(state, action) {
    switch(action.type){
        case 'SET_THEME':
            return {
                ...state,
                theme: action.payload
            };
        case 'SET_FONT_SIZE':
            return {
                ...state,
                fontSize: action.payload
            };
        case 'SET_LINE_NUMBERS':
            return {
                ...state,
                lineNumbers: action.payload
            };
        case 'SET_WORD_WRAP':
            return {
                ...state,
                wordWrap: action.payload
            };
        case 'SET_PREVIEW_MODE':
            return {
                ...state,
                previewMode: action.payload
            };
        case 'SET_AUTO_SAVE':
            return {
                ...state,
                autoSave: action.payload
            };
        case 'SET_AUTO_SAVE_INTERVAL':
            return {
                ...state,
                autoSaveInterval: action.payload
            };
        default:
            return state;
    }
}
// Main reducer
function appReducer(state, action) {
    switch(action.type){
        case 'EDITOR_ACTION':
            return {
                ...state,
                editor: editorReducer(state.editor, action.payload)
            };
        case 'FILE_MANAGER_ACTION':
            return {
                ...state,
                fileManager: fileManagerReducer(state.fileManager, action.payload)
            };
        case 'PREVIEW_ACTION':
            return {
                ...state,
                preview: previewReducer(state.preview, action.payload)
            };
        case 'SETTINGS_ACTION':
            const newSettings = settingsReducer(state.settings, action.payload);
            // Save settings to localStorage
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].saveSettings(newSettings);
            return {
                ...state,
                settings: newSettings
            };
        case 'LOAD_INITIAL_DATA':
            const savedFiles = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].loadFiles();
            const savedSettings = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].loadSettings();
            return {
                ...state,
                fileManager: {
                    ...state.fileManager,
                    files: savedFiles
                },
                settings: savedSettings ? {
                    ...state.settings,
                    ...savedSettings
                } : state.settings
            };
        case 'RESET_STATE':
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].clear();
            return initialState;
        default:
            return state;
    }
}
const AppContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AppProvider({ children }) {
    _s();
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(appReducer, initialState);
    // Load initial data on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            dispatch({
                type: 'LOAD_INITIAL_DATA'
            });
        }
    }["AppProvider.useEffect"], []);
    // Handle theme changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            const root = document.documentElement;
            if (state.settings.theme === 'dark') {
                root.classList.add('dark');
            } else {
                root.classList.remove('dark');
            }
        }
    }["AppProvider.useEffect"], [
        state.settings.theme
    ]);
    // Auto-save functionality
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            if (!state.settings.autoSave || !state.editor.isModified || !state.editor.currentFile) {
                return;
            }
            const autoSaveTimer = setTimeout({
                "AppProvider.useEffect.autoSaveTimer": ()=>{
                    saveCurrentFile();
                }
            }["AppProvider.useEffect.autoSaveTimer"], state.settings.autoSaveInterval);
            return ({
                "AppProvider.useEffect": ()=>clearTimeout(autoSaveTimer)
            })["AppProvider.useEffect"];
        }
    }["AppProvider.useEffect"], [
        state.editor.content,
        state.editor.isModified,
        state.settings.autoSave,
        state.settings.autoSaveInterval
    ]);
    // Update preview when content changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppProvider.useEffect": ()=>{
            if (state.editor.content) {
                updatePreview(state.editor.content);
            }
        }
    }["AppProvider.useEffect"], [
        state.editor.content
    ]);
    // Helper functions
    const updateContent = (content)=>{
        dispatch({
            type: 'EDITOR_ACTION',
            payload: {
                type: 'SET_CONTENT',
                payload: content
            }
        });
    };
    const openFile = (file)=>{
        dispatch({
            type: 'EDITOR_ACTION',
            payload: {
                type: 'SET_CURRENT_FILE',
                payload: file
            }
        });
    };
    const saveCurrentFile = ()=>{
        if (!state.editor.currentFile) return;
        const updatedFile = {
            ...state.editor.currentFile,
            content: state.editor.content,
            lastModified: new Date(),
            size: new Blob([
                state.editor.content
            ]).size
        };
        dispatch({
            type: 'FILE_MANAGER_ACTION',
            payload: {
                type: 'UPDATE_FILE',
                payload: updatedFile
            }
        });
        dispatch({
            type: 'EDITOR_ACTION',
            payload: {
                type: 'SET_MODIFIED',
                payload: false
            }
        });
        // Save to localStorage
        const updatedFiles = state.fileManager.files.map((file)=>file.id === updatedFile.id ? updatedFile : file);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].saveFiles(updatedFiles);
    };
    const createNewFile = (name, content = '')=>{
        const newFile = {
            id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            name: name.endsWith('.md') ? name : `${name}.md`,
            content,
            path: name,
            lastModified: new Date(),
            size: new Blob([
                content
            ]).size,
            isDirectory: false
        };
        dispatch({
            type: 'FILE_MANAGER_ACTION',
            payload: {
                type: 'ADD_FILE',
                payload: newFile
            }
        });
        // Save to localStorage
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].saveFiles([
            ...state.fileManager.files,
            newFile
        ]);
        // Open the new file
        openFile(newFile);
    };
    const deleteFile = (fileId)=>{
        dispatch({
            type: 'FILE_MANAGER_ACTION',
            payload: {
                type: 'DELETE_FILE',
                payload: fileId
            }
        });
        // If the deleted file is currently open, close it
        if (state.editor.currentFile?.id === fileId) {
            dispatch({
                type: 'EDITOR_ACTION',
                payload: {
                    type: 'SET_CURRENT_FILE',
                    payload: null
                }
            });
        }
        // Save to localStorage
        const updatedFiles = state.fileManager.files.filter((file)=>file.id !== fileId);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$file$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorage"].saveFiles(updatedFiles);
    };
    const updatePreview = (content)=>{
        try {
            dispatch({
                type: 'PREVIEW_ACTION',
                payload: {
                    type: 'SET_LOADING',
                    payload: true
                }
            });
            const htmlContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$markdown$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["markdownToHtml"])(content);
            dispatch({
                type: 'PREVIEW_ACTION',
                payload: {
                    type: 'SET_HTML_CONTENT',
                    payload: htmlContent
                }
            });
            dispatch({
                type: 'PREVIEW_ACTION',
                payload: {
                    type: 'SET_ERROR',
                    payload: null
                }
            });
        } catch (error) {
            dispatch({
                type: 'PREVIEW_ACTION',
                payload: {
                    type: 'SET_ERROR',
                    payload: 'Failed to render preview'
                }
            });
        } finally{
            dispatch({
                type: 'PREVIEW_ACTION',
                payload: {
                    type: 'SET_LOADING',
                    payload: false
                }
            });
        }
    };
    const updateSettings = (settings)=>{
        Object.entries(settings).forEach(([key, value])=>{
            switch(key){
                case 'theme':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_THEME',
                            payload: value
                        }
                    });
                    break;
                case 'fontSize':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_FONT_SIZE',
                            payload: value
                        }
                    });
                    break;
                case 'lineNumbers':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_LINE_NUMBERS',
                            payload: value
                        }
                    });
                    break;
                case 'wordWrap':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_WORD_WRAP',
                            payload: value
                        }
                    });
                    break;
                case 'previewMode':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_PREVIEW_MODE',
                            payload: value
                        }
                    });
                    break;
                case 'autoSave':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_AUTO_SAVE',
                            payload: value
                        }
                    });
                    break;
                case 'autoSaveInterval':
                    dispatch({
                        type: 'SETTINGS_ACTION',
                        payload: {
                            type: 'SET_AUTO_SAVE_INTERVAL',
                            payload: value
                        }
                    });
                    break;
            }
        });
    };
    const contextValue = {
        state,
        dispatch,
        updateContent,
        openFile,
        saveCurrentFile,
        createNewFile,
        deleteFile,
        updatePreview,
        updateSettings
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AppContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AppContext.tsx",
        lineNumber: 433,
        columnNumber: 5
    }, this);
}
_s(AppProvider, "4iRlf1j8yd8EOnLzdn/Ri/p3tw0=");
_c = AppProvider;
function useApp() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AppContext);
    if (context === undefined) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
}
_s1(useApp, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AppProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_9cc8d2a0._.js.map