/**
 * Markdown processing utilities
 */

import { marked } from 'marked';
import DOMPurify from 'dompurify';

// Configure marked with custom renderer
const renderer = new marked.Renderer();

// Custom heading renderer with anchor links
renderer.heading = function(text: string, level: number) {
  // Ensure text is a string and handle edge cases
  const textStr = typeof text === 'string' ? text : String(text || '');
  const escapedText = textStr.toLowerCase().replace(/[^\w]+/g, '-');
  return `
    <h${level} id="${escapedText}">
      <a href="#${escapedText}" class="anchor-link">#</a>
      ${textStr}
    </h${level}>
  `;
};

// Custom code block renderer with syntax highlighting support
renderer.code = function(code: string, language?: string) {
  const validLanguage = language && language.match(/^[a-zA-Z0-9_+-]*$/);
  const langClass = validLanguage ? `language-${language}` : '';

  return `
    <div class="code-block-wrapper">
      <div class="code-block-header">
        <span class="code-language">${language || 'text'}</span>
        <button class="copy-code-btn" data-code="${encodeURIComponent(code)}">
          Copy
        </button>
      </div>
      <pre><code class="${langClass}">${code}</code></pre>
    </div>
  `;
};

// Custom table renderer with responsive wrapper
renderer.table = function(header: string, body: string) {
  return `
    <div class="table-wrapper">
      <table class="markdown-table">
        <thead>${header}</thead>
        <tbody>${body}</tbody>
      </table>
    </div>
  `;
};

// Custom link renderer with external link handling
renderer.link = function(href: string, title: string | null, text: string) {
  const isExternal = href.startsWith('http') || href.startsWith('//');
  const target = isExternal ? 'target="_blank" rel="noopener noreferrer"' : '';
  const titleAttr = title ? `title="${title}"` : '';

  return `<a href="${href}" ${target} ${titleAttr}>${text}</a>`;
};

// Configure marked options
marked.setOptions({
  renderer,
  gfm: true, // GitHub Flavored Markdown
  breaks: true, // Convert \n to <br>
  pedantic: false,
  sanitize: false, // We'll use DOMPurify instead
  smartLists: true,
  smartypants: true, // Use smart quotes
});

/**
 * Convert markdown to sanitized HTML
 */
export function markdownToHtml(markdown: string): string {
  try {
    // Convert markdown to HTML
    const rawHtml = marked(markdown);

    // Sanitize HTML to prevent XSS attacks
    const cleanHtml = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',
        'a', 'img', 'video', 'audio',
        'ul', 'ol', 'li',
        'blockquote', 'pre', 'code',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'div', 'span',
        'hr',
        'details', 'summary'
      ],
      ALLOWED_ATTR: [
        'href', 'title', 'alt', 'src', 'width', 'height',
        'class', 'id', 'data-*',
        'target', 'rel',
        'controls', 'autoplay', 'loop', 'muted'
      ],
      ALLOW_DATA_ATTR: true,
    });

    return cleanHtml;
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return '<p>Error rendering markdown content</p>';
  }
}

/**
 * Extract headings from markdown for table of contents
 */
export function extractHeadings(markdown: string): Array<{
  level: number;
  text: string;
  id: string;
}> {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: Array<{ level: number; text: string; id: string }> = [];

  let match;
  while ((match = headingRegex.exec(markdown)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    const id = text.toLowerCase().replace(/[^\w]+/g, '-');

    headings.push({ level, text, id });
  }

  return headings;
}

/**
 * Get word count from markdown
 */
export function getWordCount(markdown: string): {
  words: number;
  characters: number;
  charactersNoSpaces: number;
  paragraphs: number;
  readingTime: number; // in minutes
} {
  // Remove markdown syntax for accurate word count
  const plainText = markdown
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/^\s*\|.*\|$/gm, '') // Remove tables
    .replace(/^\s*[-=]{3,}$/gm, '') // Remove horizontal rules
    .trim();

  const words = plainText.split(/\s+/).filter(word => word.length > 0).length;
  const characters = plainText.length;
  const charactersNoSpaces = plainText.replace(/\s/g, '').length;
  const paragraphs = plainText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

  // Average reading speed: 200 words per minute
  const readingTime = Math.ceil(words / 200);

  return {
    words,
    characters,
    charactersNoSpaces,
    paragraphs,
    readingTime
  };
}

/**
 * Validate markdown syntax
 */
export function validateMarkdown(markdown: string): {
  isValid: boolean;
  errors: Array<{
    line: number;
    message: string;
    type: 'warning' | 'error';
  }>;
} {
  const errors: Array<{ line: number; message: string; type: 'warning' | 'error' }> = [];
  const lines = markdown.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // Check for unmatched brackets
    const openBrackets = (line.match(/\[/g) || []).length;
    const closeBrackets = (line.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched square brackets',
        type: 'warning'
      });
    }

    // Check for unmatched parentheses in links
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched parentheses',
        type: 'warning'
      });
    }

    // Check for malformed links
    const linkRegex = /\[([^\]]*)\]\(([^)]*)\)/g;
    let linkMatch;
    while ((linkMatch = linkRegex.exec(line)) !== null) {
      const linkText = linkMatch[1];
      const linkUrl = linkMatch[2];

      if (!linkText.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty text',
          type: 'warning'
        });
      }

      if (!linkUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty URL',
          type: 'error'
        });
      }
    }

    // Check for malformed images
    const imageRegex = /!\[([^\]]*)\]\(([^)]*)\)/g;
    let imageMatch;
    while ((imageMatch = imageRegex.exec(line)) !== null) {
      const altText = imageMatch[1];
      const imageUrl = imageMatch[2];

      if (!imageUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Image has empty URL',
          type: 'error'
        });
      }
    }
  });

  return {
    isValid: errors.filter(e => e.type === 'error').length === 0,
    errors
  };
}

/**
 * Format markdown content
 */
export function formatMarkdown(markdown: string): string {
  return markdown
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .replace(/[ \t]+$/gm, '') // Remove trailing whitespace
    .replace(/^[ \t]+/gm, (match) => match.replace(/\t/g, '  ')) // Convert tabs to spaces
    .trim();
}
