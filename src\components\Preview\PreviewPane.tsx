/**
 * Preview pane component for rendered markdown
 */

'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { extractHeadings } from '@/utils/markdown';

interface PreviewPaneProps {
  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;
  scrollToPosition?: { scrollTop: number; scrollHeight: number; clientHeight: number } | null;
}

export function PreviewPane({ onScroll, scrollToPosition }: PreviewPaneProps = {}) {
  const { state, dispatch } = useApp();
  const previewRef = useRef<HTMLDivElement>(null);
  const [showToc, setShowToc] = useState(false);
  const [headings, setHeadings] = useState<Array<{ level: number; text: string; id: string }>>([]);
  const [isScrollSyncing, setIsScrollSyncing] = useState(false);

  // Extract headings for table of contents
  useEffect(() => {
    if (state.editor.content) {
      const extractedHeadings = extractHeadings(state.editor.content);
      setHeadings(extractedHeadings);
    } else {
      setHeadings([]);
    }
  }, [state.editor.content]);

  // Handle scroll synchronization
  const handleScroll = () => {
    if (!previewRef.current || isScrollSyncing) return;

    if (onScroll) {
      const { scrollTop, scrollHeight, clientHeight } = previewRef.current;
      onScroll(scrollTop, scrollHeight, clientHeight);
    }
  };

  // Sync scroll position from editor
  useEffect(() => {
    if (scrollToPosition && previewRef.current && !isScrollSyncing) {
      setIsScrollSyncing(true);

      const { scrollTop, scrollHeight, clientHeight } = scrollToPosition;
      const previewElement = previewRef.current;

      // Calculate proportional scroll position
      const scrollRatio = scrollHeight > clientHeight ? scrollTop / (scrollHeight - clientHeight) : 0;
      const targetScrollTop = Math.max(0, scrollRatio * Math.max(0, previewElement.scrollHeight - previewElement.clientHeight));

      previewElement.scrollTop = targetScrollTop;

      // Reset sync flag after a short delay
      setTimeout(() => setIsScrollSyncing(false), 50);
    }
  }, [scrollToPosition]);

  // Handle anchor link clicks
  const handleAnchorClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
      event.preventDefault();
      const id = target.getAttribute('href')?.substring(1);
      if (id && previewRef.current) {
        const element = previewRef.current.querySelector(`#${id}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }
  };

  // Handle copy code button clicks
  useEffect(() => {
    const handleCopyCode = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.classList.contains('copy-code-btn')) {
        const code = decodeURIComponent(target.getAttribute('data-code') || '');
        navigator.clipboard.writeText(code).then(() => {
          target.textContent = 'Copied!';
          setTimeout(() => {
            target.textContent = 'Copy';
          }, 2000);
        }).catch(() => {
          target.textContent = 'Failed';
          setTimeout(() => {
            target.textContent = 'Copy';
          }, 2000);
        });
      }
    };

    const previewElement = previewRef.current;
    if (previewElement) {
      previewElement.addEventListener('click', handleCopyCode);
      return () => previewElement.removeEventListener('click', handleCopyCode);
    }
  }, [state.preview.htmlContent]);

  const scrollToHeading = (id: string) => {
    if (previewRef.current) {
      const element = previewRef.current.querySelector(`#${id}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900">
      {/* Preview header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Preview</h2>

        <div className="flex items-center space-x-2">
          {/* Table of contents toggle */}
          {headings.length > 0 && (
            <button
              onClick={() => setShowToc(!showToc)}
              className={`p-2 rounded-md transition-colors ${
                showToc
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title="Table of Contents"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Table of contents sidebar */}
        {showToc && headings.length > 0 && (
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                Table of Contents
              </h3>
              <nav className="space-y-1">
                {headings.map((heading, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(heading.id)}
                    className={`block w-full text-left text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors ${
                      heading.level > 1 ? `ml-${(heading.level - 1) * 3}` : ''
                    }`}
                    style={{ paddingLeft: `${(heading.level - 1) * 12}px` }}
                  >
                    {heading.text}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}

        {/* Preview content */}
        <div className="flex-1 relative">
          {state.editor.currentFile ? (
            <div
              ref={previewRef}
              className="h-full overflow-y-auto p-6 print-full-width"
              onScroll={handleScroll}
              onClick={handleAnchorClick}
            >
              {state.preview.isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600 dark:text-gray-400">Rendering preview...</p>
                  </div>
                </div>
              ) : state.preview.error ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <svg className="w-12 h-12 text-red-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-600 dark:text-red-400">{state.preview.error}</p>
                  </div>
                </div>
              ) : state.preview.htmlContent ? (
                <div
                  className="prose prose-lg dark:prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: state.preview.htmlContent }}
                />
              ) : (
                <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <p>Start typing to see the preview</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <h3 className="text-lg font-medium mb-2">No file selected</h3>
                <p className="text-sm">Select a file to see the preview</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Custom styles for preview */}
      <style jsx global>{`
        .prose {
          color: inherit;
        }

        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
          position: relative;
        }

        .prose .anchor-link {
          position: absolute;
          left: -1.5rem;
          opacity: 0;
          transition: opacity 0.2s ease;
          text-decoration: none;
          color: #6b7280;
        }

        .prose h1:hover .anchor-link,
        .prose h2:hover .anchor-link,
        .prose h3:hover .anchor-link,
        .prose h4:hover .anchor-link,
        .prose h5:hover .anchor-link,
        .prose h6:hover .anchor-link {
          opacity: 1;
        }

        .prose .code-block-wrapper {
          position: relative;
          margin: 1rem 0;
        }

        .prose .code-block-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }

        .dark .prose .code-block-header {
          background: #374151;
          border-color: #4b5563;
        }

        .prose .code-language {
          font-weight: 500;
          color: #6b7280;
        }

        .dark .prose .code-language {
          color: #9ca3af;
        }

        .prose .copy-code-btn {
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 0.25rem;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .prose .copy-code-btn:hover {
          background: #2563eb;
        }

        .prose .code-block-wrapper pre {
          margin: 0;
          border-radius: 0 0 0.375rem 0.375rem;
          border: 1px solid #e5e7eb;
          border-top: none;
        }

        .dark .prose .code-block-wrapper pre {
          border-color: #4b5563;
        }

        .prose .table-wrapper {
          overflow-x: auto;
          margin: 1rem 0;
        }

        .prose .markdown-table {
          width: 100%;
          border-collapse: collapse;
        }

        .prose .markdown-table th,
        .prose .markdown-table td {
          border: 1px solid #e5e7eb;
          padding: 0.5rem;
          text-align: left;
        }

        .dark .prose .markdown-table th,
        .dark .prose .markdown-table td {
          border-color: #4b5563;
        }

        .prose .markdown-table th {
          background: #f9fafb;
          font-weight: 600;
        }

        .dark .prose .markdown-table th {
          background: #374151;
        }

        /* Print styles */
        @media print {
          .prose {
            max-width: none !important;
          }

          .prose .anchor-link {
            display: none;
          }

          .prose .copy-code-btn {
            display: none;
          }

          .prose .code-block-header {
            background: #f9fafb !important;
            color: #000 !important;
          }
        }
      `}</style>
    </div>
  );
}
