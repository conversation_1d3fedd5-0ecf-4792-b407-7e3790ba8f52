/**
 * Main layout component with resizable panes
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Allotment } from 'allotment';
import { useApp } from '@/contexts/AppContext';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { EditorPane } from '../Editor/EditorPane';
import { PreviewPane } from '../Preview/PreviewPane';
import { StatusBar } from './StatusBar';
import 'allotment/dist/style.css';

interface MainLayoutProps {
  children?: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { state } = useApp();
  const [sidebarWidth, setSidebarWidth] = useState(300);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editorScrollPosition, setEditorScrollPosition] = useState<{
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
  } | null>(null);
  const [previewScrollPosition, setPreviewScrollPosition] = useState<{
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
  } | null>(null);

  // Initialize layout
  useEffect(() => {
    // Load saved layout preferences
    const savedSidebarWidth = localStorage.getItem('mdeditor_sidebar_width');
    const savedSidebarCollapsed = localStorage.getItem('mdeditor_sidebar_collapsed');

    if (savedSidebarWidth) {
      setSidebarWidth(parseInt(savedSidebarWidth, 10));
    }

    if (savedSidebarCollapsed) {
      setIsSidebarCollapsed(savedSidebarCollapsed === 'true');
    }

    setIsLoading(false);
  }, []);

  // Save layout preferences
  useEffect(() => {
    if (!isLoading) {
      localStorage.setItem('mdeditor_sidebar_width', sidebarWidth.toString());
      localStorage.setItem('mdeditor_sidebar_collapsed', isSidebarCollapsed.toString());
    }
  }, [sidebarWidth, isSidebarCollapsed, isLoading]);

  const handleSidebarToggle = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  // Handle scroll synchronization
  const handleEditorScroll = (scrollTop: number, scrollHeight: number, clientHeight: number) => {
    if (state.settings.viewMode === 'split') {
      setPreviewScrollPosition({ scrollTop, scrollHeight, clientHeight });
    }
  };

  const handlePreviewScroll = (scrollTop: number, scrollHeight: number, clientHeight: number) => {
    if (state.settings.viewMode === 'split') {
      setEditorScrollPosition({ scrollTop, scrollHeight, clientHeight });
    }
  };

  const renderMainContent = () => {
    const { previewMode } = state.settings;

    switch (previewMode) {
      case 'edit':
        return <EditorPane onScroll={handleEditorScroll} />;
      case 'preview':
        return <PreviewPane onScroll={handlePreviewScroll} />;
      case 'side':
      default:
        return (
          <Allotment
            defaultSizes={[50, 50]}
            minSize={200}
            resizerStyle={{
              width: '4px',
              backgroundColor: 'var(--border-color)',
              cursor: 'col-resize'
            }}
          >
            <Allotment.Pane>
              <EditorPane onScroll={handleEditorScroll} />
            </Allotment.Pane>
            <Allotment.Pane>
              <PreviewPane
                onScroll={handlePreviewScroll}
                scrollToPosition={previewScrollPosition}
              />
            </Allotment.Pane>
          </Allotment>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 mx-auto mb-4">
              <div className="absolute inset-0 rounded-full border-4 border-blue-200 dark:border-blue-800"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-600 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-blue-400 animate-spin animation-delay-150"></div>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 animate-pulse">Loading Markdown Editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-screen flex flex-col ${state.settings.theme === 'dark' ? 'dark' : ''}`}>
      {/* Header */}
      <Header onSidebarToggle={handleSidebarToggle} />

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Sidebar with smooth animations */}
        <div
          className={`h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out ${
            isSidebarCollapsed ? 'w-0 opacity-0' : `opacity-100`
          }`}
          style={{
            width: isSidebarCollapsed ? '0px' : `${sidebarWidth}px`,
            minWidth: isSidebarCollapsed ? '0px' : '200px'
          }}
        >
          <div className={`h-full transition-opacity duration-200 ${isSidebarCollapsed ? 'opacity-0' : 'opacity-100'}`}>
            <Sidebar />
          </div>
        </div>

        {/* Resizer */}
        {!isSidebarCollapsed && (
          <div
            className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 cursor-col-resize transition-colors duration-200 flex-shrink-0"
            onMouseDown={(e) => {
              const startX = e.clientX;
              const startWidth = sidebarWidth;

              const handleMouseMove = (e: MouseEvent) => {
                const newWidth = Math.max(200, Math.min(600, startWidth + (e.clientX - startX)));
                setSidebarWidth(newWidth);
              };

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
              };

              document.addEventListener('mousemove', handleMouseMove);
              document.addEventListener('mouseup', handleMouseUp);
            }}
          />
        )}

        {/* Main editor/preview area */}
        <div className={`flex-1 h-full bg-white dark:bg-gray-900 transition-all duration-300 ease-in-out ${
          isSidebarCollapsed ? 'ml-0' : ''
        }`}>
          {renderMainContent()}
        </div>
      </div>

      {/* Status bar */}
      <StatusBar />

      {/* Custom styles */}
      <style jsx global>{`
        :root {
          --border-color: #e5e7eb;
          --sidebar-bg: #f9fafb;
          --sidebar-border: #e5e7eb;
        }

        .dark {
          --border-color: #374151;
          --sidebar-bg: #1f2937;
          --sidebar-border: #374151;
        }

        .allotment > .allotment-pane {
          overflow: hidden;
        }

        .allotment-separator {
          background-color: var(--border-color) !important;
          transition: background-color 0.2s ease;
        }

        .allotment-separator:hover {
          background-color: #3b82f6 !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .allotment-separator {
            width: 2px !important;
          }
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        ::-webkit-scrollbar-track {
          background: transparent;
        }

        ::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        .dark ::-webkit-scrollbar-thumb {
          background: #4b5563;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }

        /* Focus styles */
        .allotment:focus-within .allotment-separator {
          background-color: #3b82f6 !important;
        }

        /* Animation for smooth transitions */
        .transition-layout {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Print styles */
        @media print {
          .no-print {
            display: none !important;
          }

          .print-full-width {
            width: 100% !important;
            max-width: none !important;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .allotment-separator {
            background-color: #000 !important;
          }

          .dark .allotment-separator {
            background-color: #fff !important;
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .transition-layout,
          .allotment-separator {
            transition: none !important;
          }
        }
      `}</style>
    </div>
  );
}
