(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_ae9a9a6b._.js",
  "static/chunks/node_modules_9beba542._.js",
  "static/chunks/node_modules_jspdf_dist_jspdf_es_min_181b10b8.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/docx/dist/index.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_044a38da._.js",
  "static/chunks/node_modules_docx_dist_index_mjs_181b10b8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/docx/dist/index.mjs [app-client] (ecmascript)");
    });
});
}}),
}]);