# Markdown Editor - Professional Markdown Editing

A powerful, feature-rich markdown editor built with Next.js 14+ and TypeScript, featuring real-time preview, file management, and export capabilities.

## ✨ Features

### Core Functionality
- **Two-pane resizable layout** with editor and preview
- **Real-time markdown preview** with syntax highlighting
- **Comprehensive toolbar** with formatting buttons
- **File management system** with create, rename, delete operations
- **Local storage persistence** for files and settings
- **Dark/light mode toggle** with system preference detection

### Editor Features
- **Syntax highlighting** for markdown
- **Line numbers** (toggleable)
- **Word wrap** support
- **Auto-save** functionality with configurable intervals
- **Keyboard shortcuts** (Ctrl+B for bold, Ctrl+I for italic, etc.)
- **Drag & drop image support**
- **Tab indentation** with Shift+Tab unindent

### Export Options
- **PDF export** with proper formatting
- **DOCX export** for Microsoft Word
- **HTML export** with embedded styles
- **Plain text export**
- **Lazy loading** for export libraries to optimize bundle size

### File Management
- **Create new files** with .md extension auto-addition
- **Upload existing files** with drag-and-drop support
- **File organization** with folders and file listing
- **File size and modification date** display

### Advanced Features
- **Table of contents** generation from headings
- **Word count and reading time** statistics
- **Markdown validation** with error reporting
- **Print support** with optimized layouts
- **Responsive design** for mobile, tablet, and desktop
- **Accessibility features** with ARIA labels and keyboard navigation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS with custom components
- **State Management**: React Context with useReducer
- **Layout**: Allotment for resizable panes
- **Markdown Processing**: Marked.js with DOMPurify sanitization

### Project Structure
```
src/
├── app/                 # Next.js app router
├── components/          # React components
│   ├── Editor/         # Editor-related components
│   ├── Layout/         # Layout components
│   └── Preview/        # Preview components
├── contexts/           # React contexts
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## 🎨 Key Features Implemented

### ✅ Sidebar Collapse & Full-Screen Stretch
- When sidebar is collapsed, the main content area stretches to full width
- No empty space left behind when sidebar is hidden
- Smooth transitions and responsive behavior

### ✅ Dark/Light Theme Toggle
- Automatic system preference detection
- Manual theme switching via header button
- Persistent theme settings in localStorage
- Proper CSS class application to document root

### ✅ Resizable Layout
- Two-pane layout with Allotment for smooth resizing
- Configurable pane sizes with minimum width constraints
- Layout preferences saved to localStorage

### ✅ File Management
- Create, upload, and delete markdown files
- Local storage persistence
- File metadata display (size, date modified)
- Drag-and-drop file upload support

### ✅ Real-time Preview
- Markdown to HTML conversion with marked.js
- XSS protection with DOMPurify
- Debounced updates for performance
- Table of contents generation

### ✅ Export Functionality
- PDF, DOCX, HTML, and TXT export options
- Lazy loading of export libraries
- Proper formatting preservation

## 🎯 Usage

1. **Create a new file**: Click "New" in the sidebar or header
2. **Toggle sidebar**: Click the hamburger menu in the header
3. **Switch themes**: Click the sun/moon icon in the header
4. **Change preview mode**: Use the Edit/Split/Preview buttons in the header
5. **Export files**: Click "Export" and choose your format
6. **Upload files**: Click "Upload" in the sidebar or drag files into the editor

## 📱 Responsive Design

- **Mobile**: Collapsible sidebar with touch-friendly controls
- **Tablet**: Resizable panes with optimized spacing
- **Desktop**: Full feature set with keyboard shortcuts

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel deploy
```

### Other Platforms
```bash
npm run build
npm start
```
