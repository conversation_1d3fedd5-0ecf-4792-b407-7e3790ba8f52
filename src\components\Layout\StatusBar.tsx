/**
 * Status bar component showing file info and editor stats
 */

'use client';

import React, { useMemo } from 'react';
import { useApp } from '@/contexts/AppContext';
import { getWordCount, validateMarkdown } from '@/utils/markdown';

export function StatusBar() {
  const { state } = useApp();

  // Calculate word count and validation
  const stats = useMemo(() => {
    if (!state.editor.content) {
      return {
        words: 0,
        characters: 0,
        charactersNoSpaces: 0,
        paragraphs: 0,
        readingTime: 0,
        isValid: true,
        errors: []
      };
    }

    const wordCount = getWordCount(state.editor.content);
    const validation = validateMarkdown(state.editor.content);

    return {
      ...wordCount,
      ...validation
    };
  }, [state.editor.content]);

  const formatTime = (minutes: number): string => {
    if (minutes < 1) return '< 1 min read';
    if (minutes === 1) return '1 min read';
    return `${minutes} min read`;
  };

  return (
    <div className="h-6 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 text-xs text-gray-600 dark:text-gray-400 no-print">
      {/* Left section - File info */}
      <div className="flex items-center space-x-4">
        {state.editor.currentFile ? (
          <>
            <span className="flex items-center space-x-1">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>{state.editor.currentFile.name}</span>
            </span>

            {state.editor.isModified && (
              <span className="flex items-center space-x-1 text-orange-600 dark:text-orange-400">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>Modified</span>
              </span>
            )}

            {state.settings.autoSave && (
              <span className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Auto-save</span>
              </span>
            )}
          </>
        ) : (
          <span>No file open</span>
        )}
      </div>

      {/* Center section - Document stats */}
      <div className="flex items-center space-x-4">
        {state.editor.content && (
          <>
            <span>{stats.words} words</span>
            <span>•</span>
            <span>{stats.characters} characters</span>
            <span>•</span>
            <span>{stats.paragraphs} paragraphs</span>
            <span>•</span>
            <span>{formatTime(stats.readingTime)}</span>
          </>
        )}
      </div>

      {/* Right section - Editor info */}
      <div className="flex items-center space-x-4">
        {/* Validation status */}
        {state.editor.content && (
          <div className="flex items-center space-x-1">
            {stats.isValid ? (
              <>
                <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-green-600 dark:text-green-400">Valid</span>
              </>
            ) : (
              <>
                <svg className="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-red-600 dark:text-red-400">
                  {stats.errors.filter(e => e.type === 'error').length} errors
                </span>
              </>
            )}
          </div>
        )}

        {/* Cursor position */}
        <span>
          Ln {state.editor.cursorPosition.line}, Col {state.editor.cursorPosition.column}
        </span>

        {/* Theme indicator */}
        <span className="flex items-center space-x-1">
          {state.settings.theme === 'light' ? (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          ) : (
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          )}
          <span className="capitalize">{state.settings.theme}</span>
        </span>

        {/* Preview mode */}
        <span className="capitalize">{state.settings.previewMode}</span>

        {/* Loading indicators */}
        {(state.editor.isLoading || state.preview.isLoading || state.fileManager.isLoading) && (
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
            <span>Loading...</span>
          </div>
        )}

        {/* Error indicators */}
        {(state.editor.error || state.preview.error || state.fileManager.error) && (
          <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Error</span>
          </div>
        )}
      </div>

      {/* Tooltip for validation errors */}
      {!stats.isValid && stats.errors.length > 0 && (
        <div className="fixed bottom-8 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-3 max-w-sm z-50">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Validation Issues</h4>
          <div className="space-y-1">
            {stats.errors.slice(0, 5).map((error, index) => (
              <div key={index} className="text-xs">
                <span className={`font-medium ${error.type === 'error' ? 'text-red-600' : 'text-yellow-600'}`}>
                  Line {error.line}:
                </span>
                <span className="ml-1 text-gray-600 dark:text-gray-400">
                  {error.message}
                </span>
              </div>
            ))}
            {stats.errors.length > 5 && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                ... and {stats.errors.length - 5} more
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
