/**
 * Export utilities for different file formats
 */

import { ExportOptions, ExportError } from '@/types';
import { markdownToHtml } from './markdown';
import { downloadFile } from './file';

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(markdown, textFilename, 'text/plain');
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);
    
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #333;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        h1 { font-size: 2.5rem; }
        h2 { font-size: 2rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        h5 { font-size: 1.125rem; }
        h6 { font-size: 1rem; }
        
        p {
            margin-bottom: 1rem;
        }
        
        code {
            background-color: #f5f5f5;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        pre {
            background-color: #f5f5f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        pre code {
            background: none;
            padding: 0;
        }
        
        blockquote {
            border-left: 4px solid #ddd;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #666;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1rem 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 0.5rem;
            text-align: left;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        
        a {
            color: #0066cc;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        
        li {
            margin-bottom: 0.5rem;
        }
        
        hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 2rem 0;
        }
        
        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
        }
        
        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }
            
            .anchor-link {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;
    
    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF (lazy loaded)
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load jsPDF
    const { jsPDF } = await import('jspdf');
    
    const htmlContent = markdownToHtml(markdown);
    
    // Create a temporary div to render HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.width = '800px';
    tempDiv.style.fontFamily = 'Arial, sans-serif';
    tempDiv.style.fontSize = '12px';
    tempDiv.style.lineHeight = '1.6';
    
    document.body.appendChild(tempDiv);
    
    try {
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: options.pageSize || 'a4'
      });
      
      // Set margins
      const margins = options.margins || { top: 20, right: 20, bottom: 20, left: 20 };
      
      // Add title if available
      const title = filename.replace(/\.[^/.]+$/, '');
      pdf.setFontSize(16);
      pdf.text(title, margins.left, margins.top);
      
      // Convert HTML to text and add to PDF
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      const lines = pdf.splitTextToSize(textContent, 210 - margins.left - margins.right);
      
      pdf.setFontSize(12);
      let yPosition = margins.top + 15;
      
      lines.forEach((line: string) => {
        if (yPosition > 297 - margins.bottom) {
          pdf.addPage();
          yPosition = margins.top;
        }
        pdf.text(line, margins.left, yPosition);
        yPosition += 6;
      });
      
      // Add metadata if requested
      if (options.includeMetadata) {
        pdf.setProperties({
          title: title,
          creator: 'Markdown Editor',
          creationDate: new Date()
        });
      }
      
      const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
      pdf.save(pdfFilename);
    } finally {
      document.body.removeChild(tempDiv);
    }
  } catch (error) {
    throw new ExportError('Failed to export as PDF', 'pdf', error);
  }
}

/**
 * Export markdown as DOCX (lazy loaded)
 */
export async function exportAsDocx(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load docx
    const { Document, Packer, Paragraph, TextRun, HeadingLevel } = await import('docx');
    
    // Parse markdown and convert to DOCX elements
    const lines = markdown.split('\n');
    const paragraphs: any[] = [];
    
    for (const line of lines) {
      if (line.trim() === '') {
        paragraphs.push(new Paragraph({ text: '' }));
        continue;
      }
      
      // Handle headings
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headingMatch) {
        const level = headingMatch[1].length;
        const text = headingMatch[2];
        
        const headingLevels = [
          HeadingLevel.HEADING_1,
          HeadingLevel.HEADING_2,
          HeadingLevel.HEADING_3,
          HeadingLevel.HEADING_4,
          HeadingLevel.HEADING_5,
          HeadingLevel.HEADING_6
        ];
        
        paragraphs.push(new Paragraph({
          text,
          heading: headingLevels[level - 1] || HeadingLevel.HEADING_6
        }));
        continue;
      }
      
      // Handle bold and italic text
      const textRuns: any[] = [];
      let currentText = line;
      
      // Simple bold/italic parsing (basic implementation)
      const boldRegex = /\*\*(.*?)\*\*/g;
      const italicRegex = /\*(.*?)\*/g;
      
      // For now, just add as plain text
      // TODO: Implement proper markdown parsing for rich text
      textRuns.push(new TextRun({ text: currentText }));
      
      paragraphs.push(new Paragraph({ children: textRuns }));
    }
    
    const doc = new Document({
      sections: [{
        properties: {},
        children: paragraphs
      }]
    });
    
    // Generate and download the document
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => URL.revokeObjectURL(url), 100);
  } catch (error) {
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

/**
 * Main export function that delegates to specific format handlers
 */
export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename, options);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename, options);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
