/**
 * Export utilities for different file formats
 */

import { ExportOptions, ExportError } from '@/types';
import { markdownToHtml } from './markdown';
import { downloadFile } from './file';

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(markdown, textFilename, 'text/plain');
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);

    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.75;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #374151;
            background: #ffffff;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #111827;
        }

        h1 { font-size: 2.25rem; line-height: 2.5rem; }
        h2 { font-size: 1.875rem; line-height: 2.25rem; }
        h3 { font-size: 1.5rem; line-height: 2rem; }
        h4 { font-size: 1.25rem; line-height: 1.75rem; }
        h5 { font-size: 1.125rem; line-height: 1.75rem; }
        h6 { font-size: 1rem; line-height: 1.5rem; }

        p {
            margin-bottom: 1rem;
            line-height: 1.75;
        }

        /* Code styling to match preview */
        .code-block-wrapper {
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .code-block-header {
            background-color: #f9fafb;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-language {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
        }

        .copy-code-btn {
            display: none; /* Hide in exports */
        }

        .code-block-wrapper pre {
            background-color: #f3f4f6;
            padding: 1rem;
            margin: 0;
            overflow-x: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .code-block-wrapper code {
            background: none;
            padding: 0;
            color: #374151;
        }

        /* Inline code */
        code:not(.code-block-wrapper code) {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #374151;
        }

        /* Table styling to match preview */
        .table-wrapper {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .markdown-table th,
        .markdown-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #6b7280;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        a {
            color: #3b82f6;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2rem 0;
        }

        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
            color: #6b7280;
        }

        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }

        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }

            .anchor-link {
                display: none;
            }

            .copy-code-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF (lazy loaded)
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load jsPDF
    const { jsPDF } = await import('jspdf');

    const htmlContent = markdownToHtml(markdown);

    // Create a temporary div to render HTML with preview styling
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.width = '800px';
    tempDiv.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
    tempDiv.style.fontSize = '14px';
    tempDiv.style.lineHeight = '1.75';
    tempDiv.style.color = '#374151';

    // Apply preview-like styling
    const style = document.createElement('style');
    style.textContent = `
      .temp-export h1, .temp-export h2, .temp-export h3, .temp-export h4, .temp-export h5, .temp-export h6 {
        font-weight: 600;
        color: #111827;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
      }
      .temp-export h1 { font-size: 2rem; }
      .temp-export h2 { font-size: 1.75rem; }
      .temp-export h3 { font-size: 1.5rem; }
      .temp-export h4 { font-size: 1.25rem; }
      .temp-export h5 { font-size: 1.125rem; }
      .temp-export h6 { font-size: 1rem; }
      .temp-export p { margin-bottom: 1rem; }
      .temp-export code { background-color: #f3f4f6; padding: 0.125rem 0.25rem; border-radius: 0.25rem; }
      .temp-export pre { background-color: #f3f4f6; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; }
      .temp-export blockquote { border-left: 4px solid #e5e7eb; padding-left: 1rem; color: #6b7280; margin: 1rem 0; }
      .temp-export ul, .temp-export ol { margin: 1rem 0; padding-left: 2rem; }
      .temp-export li { margin-bottom: 0.5rem; }
    `;
    tempDiv.className = 'temp-export';
    document.head.appendChild(style);
    document.body.appendChild(tempDiv);

    try {
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: options.pageSize || 'a4'
      });

      // Set margins
      const margins = options.margins || { top: 20, right: 20, bottom: 20, left: 20 };

      // Add title if available
      const title = filename.replace(/\.[^/.]+$/, '');
      pdf.setFontSize(16);
      pdf.text(title, margins.left, margins.top);

      // Convert HTML to text and add to PDF
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      const lines = pdf.splitTextToSize(textContent, 210 - margins.left - margins.right);

      pdf.setFontSize(12);
      let yPosition = margins.top + 15;

      lines.forEach((line: string) => {
        if (yPosition > 297 - margins.bottom) {
          pdf.addPage();
          yPosition = margins.top;
        }
        pdf.text(line, margins.left, yPosition);
        yPosition += 6;
      });

      // Add metadata if requested
      if (options.includeMetadata) {
        pdf.setProperties({
          title: title,
          creator: 'Markdown Editor',
          creationDate: new Date()
        });
      }

      const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
      pdf.save(pdfFilename);
    } finally {
      document.body.removeChild(tempDiv);
      document.head.removeChild(style);
    }
  } catch (error) {
    throw new ExportError('Failed to export as PDF', 'pdf', error);
  }
}

/**
 * Export markdown as DOCX (lazy loaded)
 */
export async function exportAsDocx(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load docx
    const { Document, Packer, Paragraph, TextRun, HeadingLevel } = await import('docx');

    // Parse markdown and convert to DOCX elements
    const lines = markdown.split('\n');
    const paragraphs: any[] = [];

    for (const line of lines) {
      if (line.trim() === '') {
        paragraphs.push(new Paragraph({ text: '' }));
        continue;
      }

      // Handle headings
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headingMatch) {
        const level = headingMatch[1].length;
        const text = headingMatch[2];

        const headingLevels = [
          HeadingLevel.HEADING_1,
          HeadingLevel.HEADING_2,
          HeadingLevel.HEADING_3,
          HeadingLevel.HEADING_4,
          HeadingLevel.HEADING_5,
          HeadingLevel.HEADING_6
        ];

        paragraphs.push(new Paragraph({
          text,
          heading: headingLevels[level - 1] || HeadingLevel.HEADING_6
        }));
        continue;
      }

      // Handle bold and italic text
      const textRuns: any[] = [];
      let currentText = line;

      // Simple bold/italic parsing (basic implementation)
      const boldRegex = /\*\*(.*?)\*\*/g;
      const italicRegex = /\*(.*?)\*/g;

      // For now, just add as plain text
      // TODO: Implement proper markdown parsing for rich text
      textRuns.push(new TextRun({ text: currentText }));

      paragraphs.push(new Paragraph({ children: textRuns }));
    }

    const doc = new Document({
      sections: [{
        properties: {},
        children: paragraphs
      }]
    });

    // Generate and download the document
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => URL.revokeObjectURL(url), 100);
  } catch (error) {
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

/**
 * Main export function that delegates to specific format handlers
 */
export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename, options);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename, options);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
