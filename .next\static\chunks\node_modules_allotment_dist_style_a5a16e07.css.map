{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/node_modules/allotment/dist/style.css"], "sourcesContent": [":root {\n  --separator-border: rgba(128, 128, 128, 0.35);\n  --sash-hover-transition-duration: 0.1s;\n}\n\n.allotment-module_splitView__L-yRc {\n  height: 100%;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n\n.allotment-module_splitView__L-yRc > .allotment-module_sashContainer__fzwJF {\n  height: 100%;\n  pointer-events: none;\n  position: absolute;\n  width: 100%;\n}\n\n.allotment-module_splitView__L-yRc > .allotment-module_sashContainer__fzwJF > .allotment-module_sash__QA-2t {\n  pointer-events: auto;\n}\n\n.allotment-module_splitView__L-yRc > .allotment-module_splitViewContainer__rQnVa {\n  height: 100%;\n  position: relative;\n  white-space: nowrap;\n  width: 100%;\n}\n\n.allotment-module_splitView__L-yRc > .allotment-module_splitViewContainer__rQnVa > .allotment-module_splitViewView__MGZ6O {\n  overflow: hidden;\n  position: absolute;\n  white-space: initial;\n}\n\n.allotment-module_splitView__L-yRc.allotment-module_vertical__WSwwa > .allotment-module_splitViewContainer__rQnVa > .allotment-module_splitViewView__MGZ6O {\n  width: 100%;\n}\n\n.allotment-module_splitView__L-yRc.allotment-module_horizontal__7doS8 > .allotment-module_splitViewContainer__rQnVa > .allotment-module_splitViewView__MGZ6O {\n  height: 100%;\n}\n\n.allotment-module_splitView__L-yRc.allotment-module_separatorBorder__x-rDS\n  > .allotment-module_splitViewContainer__rQnVa\n  > .allotment-module_splitViewView__MGZ6O:not(:first-child)::before {\n  background-color: var(--separator-border);\n  content: \" \";\n  left: 0;\n  pointer-events: none;\n  position: absolute;\n  top: 0;\n  z-index: 5;\n}\n\n.allotment-module_splitView__L-yRc.allotment-module_separatorBorder__x-rDS.allotment-module_vertical__WSwwa\n  > .allotment-module_splitViewContainer__rQnVa\n  > .allotment-module_splitViewView__MGZ6O:not(:first-child)::before {\n  height: 1px;\n  width: 100%;\n}\n\n.allotment-module_splitView__L-yRc.allotment-module_separatorBorder__x-rDS.allotment-module_horizontal__7doS8\n  > .allotment-module_splitViewContainer__rQnVa\n  > .allotment-module_splitViewView__MGZ6O:not(:first-child)::before {\n  height: 100%;\n  width: 1px;\n}\n\n:root {\n  --focus-border: #007fd4;\n  --sash-size: 8px;\n  --sash-hover-size: 4px;\n}\n\n.sash-module_sash__K-9lB {\n  position: absolute;\n  z-index: 35;\n  touch-action: none;\n  pointer-events: auto;\n  text-align: initial;\n}\n\n.sash-module_sash__K-9lB.sash-module_disabled__Hm-wx {\n  pointer-events: none;\n}\n\n.sash-module_sash__K-9lB.sash-module_mac__Jf6OJ.sash-module_vertical__pB-rs {\n  cursor: col-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs.sash-module_minimum__-UKxp {\n  cursor: e-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs.sash-module_maximum__TCWxD {\n  cursor: w-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_mac__Jf6OJ.sash-module_horizontal__kFbiw {\n  cursor: row-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_minimum__-UKxp {\n  cursor: s-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_maximum__TCWxD {\n  cursor: n-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_disabled__Hm-wx {\n  cursor: default !important;\n  pointer-events: none !important;\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs {\n  cursor: ew-resize;\n  top: 0;\n  width: var(--sash-size);\n  height: 100%;\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw {\n  cursor: ns-resize;\n  left: 0;\n  width: 100%;\n  height: var(--sash-size);\n}\n\n.sash-module_sash__K-9lB:not(.sash-module_disabled__Hm-wx) > .sash-module_orthogonal-drag-handle__Yii2- {\n  content: \" \";\n  height: calc(var(--sash-size) * 2);\n  width: calc(var(--sash-size) * 2);\n  z-index: 100;\n  display: block;\n  cursor: all-scroll;\n  position: absolute;\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_orthogonal-edge-north__f7Noe:not(.sash-module_disabled__Hm-wx)\n  > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_start__uZEDk,\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_orthogonal-edge-south__6ZrFC:not(.sash-module_disabled__Hm-wx)\n  > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_end__0TP-R {\n  cursor: nwse-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_orthogonal-edge-north__f7Noe:not(.sash-module_disabled__Hm-wx)\n  > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_end__0TP-R,\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw.sash-module_orthogonal-edge-south__6ZrFC:not(.sash-module_disabled__Hm-wx)\n  > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_start__uZEDk {\n  cursor: nesw-resize;\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_start__uZEDk {\n  left: calc(var(--sash-size) * -0.5);\n  top: calc(var(--sash-size) * -1);\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_end__0TP-R {\n  left: calc(var(--sash-size) * -0.5);\n  bottom: calc(var(--sash-size) * -1);\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_start__uZEDk {\n  top: calc(var(--sash-size) * -0.5);\n  left: calc(var(--sash-size) * -1);\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw > .sash-module_orthogonal-drag-handle__Yii2-.sash-module_end__0TP-R {\n  top: calc(var(--sash-size) * -0.5);\n  right: calc(var(--sash-size) * -1);\n}\n\n.sash-module_sash__K-9lB:before {\n  content: \"\";\n  pointer-events: none;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  transition: background-color var(--sash-hover-transition-duration) ease-out;\n  background: transparent;\n}\n\n.sash-module_sash__K-9lB.sash-module_vertical__pB-rs:before {\n  width: var(--sash-hover-size);\n  left: calc(50% - (var(--sash-hover-size) / 2));\n}\n\n.sash-module_sash__K-9lB.sash-module_horizontal__kFbiw:before {\n  height: var(--sash-hover-size);\n  top: calc(50% - (var(--sash-hover-size) / 2));\n}\n\n.sash-module_sash__K-9lB.sash-module_hover__80W6I:before,\n.sash-module_sash__K-9lB.sash-module_active__bJspD:before {\n  background: var(--focus-border);\n}\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;AAYA;;;;;AAOA;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;AAOA;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA", "ignoreList": [0], "debugId": null}}]}