/**
 * File system utilities
 */

import { MarkdownFile, FileSystemError } from '@/types';

/**
 * Generate unique file ID
 */
export function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.slice(lastDot + 1).toLowerCase();
}

/**
 * Check if file is a markdown file
 */
export function isMarkdownFile(filename: string): boolean {
  const extension = getFileExtension(filename);
  return ['md', 'markdown', 'mdown', 'mkd', 'mdx'].includes(extension);
}

/**
 * Validate filename
 */
export function validateFilename(filename: string): {
  isValid: boolean;
  error?: string;
} {
  if (!filename.trim()) {
    return { isValid: false, error: 'Filename cannot be empty' };
  }

  if (filename.length > 255) {
    return { isValid: false, error: 'Filename is too long (max 255 characters)' };
  }

  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(filename)) {
    return { isValid: false, error: 'Filename contains invalid characters' };
  }

  // Check for reserved names (Windows)
  const reservedNames = [
    'CON', 'PRN', 'AUX', 'NUL',
    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
  ];

  const nameWithoutExt = filename.split('.')[0].toUpperCase();
  if (reservedNames.includes(nameWithoutExt)) {
    return { isValid: false, error: 'Filename is reserved by the system' };
  }

  return { isValid: true };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

/**
 * Format date for display
 */
export function formatDate(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays === 1) {
    return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Create a new markdown file
 */
export function createMarkdownFile(
  name: string,
  content: string = '',
  parentId?: string
): MarkdownFile {
  const validation = validateFilename(name);
  if (!validation.isValid) {
    throw new FileSystemError(validation.error!, 'create');
  }

  // Ensure .md extension
  const filename = name.endsWith('.md') ? name : `${name}.md`;

  return {
    id: generateFileId(),
    name: filename,
    content,
    path: parentId ? `${parentId}/${filename}` : filename,
    lastModified: new Date(),
    size: new Blob([content]).size,
    isDirectory: false,
    parentId
  };
}

/**
 * Create a new directory
 */
export function createDirectory(name: string, parentId?: string): MarkdownFile {
  const validation = validateFilename(name);
  if (!validation.isValid) {
    throw new FileSystemError(validation.error!, 'create');
  }

  return {
    id: generateFileId(),
    name,
    content: '',
    path: parentId ? `${parentId}/${name}` : name,
    lastModified: new Date(),
    size: 0,
    isDirectory: true,
    parentId
  };
}

/**
 * Local storage utilities
 */
export const localStorage = {
  /**
   * Save files to local storage
   */
  saveFiles(files: MarkdownFile[]): void {
    try {
      const serialized = JSON.stringify(files, (key, value) => {
        if (key === 'lastModified' && value instanceof Date) {
          return value.toISOString();
        }
        return value;
      });
      window.localStorage.setItem('mdeditor_files', serialized);
    } catch (error) {
      console.error('Failed to save files to localStorage:', error);
      throw new FileSystemError('Failed to save files', 'save');
    }
  },

  /**
   * Load files from local storage
   */
  loadFiles(): MarkdownFile[] {
    try {
      const stored = window.localStorage.getItem('mdeditor_files');
      if (!stored) return [];

      const parsed = JSON.parse(stored);
      return parsed.map((file: any) => ({
        ...file,
        lastModified: new Date(file.lastModified)
      }));
    } catch (error) {
      console.error('Failed to load files from localStorage:', error);
      return [];
    }
  },

  /**
   * Save editor settings
   */
  saveSettings(settings: any): void {
    try {
      window.localStorage.setItem('mdeditor_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  },

  /**
   * Load editor settings
   */
  loadSettings(): any {
    try {
      const stored = window.localStorage.getItem('mdeditor_settings');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return null;
    }
  },

  /**
   * Clear all stored data
   */
  clear(): void {
    try {
      window.localStorage.removeItem('mdeditor_files');
      window.localStorage.removeItem('mdeditor_settings');
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  }
};

/**
 * File download utilities
 */
export function downloadFile(content: string, filename: string, mimeType: string = 'text/plain'): void {
  try {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    setTimeout(() => URL.revokeObjectURL(url), 100);
  } catch (error) {
    console.error('Failed to download file:', error);
    throw new FileSystemError('Failed to download file', 'download');
  }
}

/**
 * Read file from input element
 */
export function readFileFromInput(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!isMarkdownFile(file.name)) {
      reject(new FileSystemError('File is not a markdown file', 'read'));
      return;
    }

    const reader = new FileReader();

    reader.onload = (event) => {
      const content = event.target?.result as string;
      resolve(content);
    };

    reader.onerror = () => {
      reject(new FileSystemError('Failed to read file', 'read'));
    };

    reader.readAsText(file);
  });
}

/**
 * Debounce function for auto-save
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
