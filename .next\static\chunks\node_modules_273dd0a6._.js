(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/next/dist/compiled/client-only/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/node_modules/styled-jsx/dist/index/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
__turbopack_context__.r("[project]/node_modules/next/dist/compiled/client-only/index.js [app-client] (ecmascript)");
var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function _interopDefaultLegacy(e) {
    return e && typeof e === 'object' && 'default' in e ? e : {
        'default': e
    };
}
var React__default = /*#__PURE__*/ _interopDefaultLegacy(React);
/*
Based on Glamor's sheet
https://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js
*/ function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _createClass(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
var isProd = typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] !== "undefined" && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env && ("TURBOPACK compile-time value", "development") === "production";
var isString = function(o) {
    return Object.prototype.toString.call(o) === "[object String]";
};
var StyleSheet = /*#__PURE__*/ function() {
    function StyleSheet(param) {
        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? "stylesheet" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;
        invariant$1(isString(name), "`name` must be a string");
        this._name = name;
        this._deletedRulePlaceholder = "#" + name + "-deleted-rule____{}";
        invariant$1(typeof optimizeForSpeed === "boolean", "`optimizeForSpeed` must be a boolean");
        this._optimizeForSpeed = optimizeForSpeed;
        this._serverSheet = undefined;
        this._tags = [];
        this._injected = false;
        this._rulesCount = 0;
        var node = typeof window !== "undefined" && document.querySelector('meta[property="csp-nonce"]');
        this._nonce = node ? node.getAttribute("content") : null;
    }
    var _proto = StyleSheet.prototype;
    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {
        invariant$1(typeof bool === "boolean", "`setOptimizeForSpeed` accepts a boolean");
        invariant$1(this._rulesCount === 0, "optimizeForSpeed cannot be when rules have already been inserted");
        this.flush();
        this._optimizeForSpeed = bool;
        this.inject();
    };
    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {
        return this._optimizeForSpeed;
    };
    _proto.inject = function inject() {
        var _this = this;
        invariant$1(!this._injected, "sheet already injected");
        this._injected = true;
        if (typeof window !== "undefined" && this._optimizeForSpeed) {
            this._tags[0] = this.makeStyleTag(this._name);
            this._optimizeForSpeed = "insertRule" in this.getSheet();
            if (!this._optimizeForSpeed) {
                if ("TURBOPACK compile-time truthy", 1) {
                    console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.");
                }
                this.flush();
                this._injected = true;
            }
            return;
        }
        this._serverSheet = {
            cssRules: [],
            insertRule: function(rule, index) {
                if (typeof index === "number") {
                    _this._serverSheet.cssRules[index] = {
                        cssText: rule
                    };
                } else {
                    _this._serverSheet.cssRules.push({
                        cssText: rule
                    });
                }
                return index;
            },
            deleteRule: function(index) {
                _this._serverSheet.cssRules[index] = null;
            }
        };
    };
    _proto.getSheetForTag = function getSheetForTag(tag) {
        if (tag.sheet) {
            return tag.sheet;
        }
        // this weirdness brought to you by firefox
        for(var i = 0; i < document.styleSheets.length; i++){
            if (document.styleSheets[i].ownerNode === tag) {
                return document.styleSheets[i];
            }
        }
    };
    _proto.getSheet = function getSheet() {
        return this.getSheetForTag(this._tags[this._tags.length - 1]);
    };
    _proto.insertRule = function insertRule(rule, index) {
        invariant$1(isString(rule), "`insertRule` accepts only strings");
        if (typeof window === "undefined") {
            if (typeof index !== "number") {
                index = this._serverSheet.cssRules.length;
            }
            this._serverSheet.insertRule(rule, index);
            return this._rulesCount++;
        }
        if (this._optimizeForSpeed) {
            var sheet = this.getSheet();
            if (typeof index !== "number") {
                index = sheet.cssRules.length;
            }
            // this weirdness for perf, and chrome's weird bug
            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule
            try {
                sheet.insertRule(rule, index);
            } catch (error) {
                if ("TURBOPACK compile-time truthy", 1) {
                    console.warn("StyleSheet: illegal rule: \n\n" + rule + "\n\nSee https://stackoverflow.com/q/20007992 for more info");
                }
                return -1;
            }
        } else {
            var insertionPoint = this._tags[index];
            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));
        }
        return this._rulesCount++;
    };
    _proto.replaceRule = function replaceRule(index, rule) {
        if (this._optimizeForSpeed || typeof window === "undefined") {
            var sheet = typeof window !== "undefined" ? this.getSheet() : this._serverSheet;
            if (!rule.trim()) {
                rule = this._deletedRulePlaceholder;
            }
            if (!sheet.cssRules[index]) {
                // @TBD Should we throw an error?
                return index;
            }
            sheet.deleteRule(index);
            try {
                sheet.insertRule(rule, index);
            } catch (error) {
                if ("TURBOPACK compile-time truthy", 1) {
                    console.warn("StyleSheet: illegal rule: \n\n" + rule + "\n\nSee https://stackoverflow.com/q/20007992 for more info");
                }
                // In order to preserve the indices we insert a deleteRulePlaceholder
                sheet.insertRule(this._deletedRulePlaceholder, index);
            }
        } else {
            var tag = this._tags[index];
            invariant$1(tag, "old rule at index `" + index + "` not found");
            tag.textContent = rule;
        }
        return index;
    };
    _proto.deleteRule = function deleteRule(index) {
        if (typeof window === "undefined") {
            this._serverSheet.deleteRule(index);
            return;
        }
        if (this._optimizeForSpeed) {
            this.replaceRule(index, "");
        } else {
            var tag = this._tags[index];
            invariant$1(tag, "rule at index `" + index + "` not found");
            tag.parentNode.removeChild(tag);
            this._tags[index] = null;
        }
    };
    _proto.flush = function flush() {
        this._injected = false;
        this._rulesCount = 0;
        if (typeof window !== "undefined") {
            this._tags.forEach(function(tag) {
                return tag && tag.parentNode.removeChild(tag);
            });
            this._tags = [];
        } else {
            // simpler on server
            this._serverSheet.cssRules = [];
        }
    };
    _proto.cssRules = function cssRules() {
        var _this = this;
        if (typeof window === "undefined") {
            return this._serverSheet.cssRules;
        }
        return this._tags.reduce(function(rules, tag) {
            if (tag) {
                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {
                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;
                }));
            } else {
                rules.push(null);
            }
            return rules;
        }, []);
    };
    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {
        if (cssString) {
            invariant$1(isString(cssString), "makeStyleTag accepts only strings as second parameter");
        }
        var tag = document.createElement("style");
        if (this._nonce) tag.setAttribute("nonce", this._nonce);
        tag.type = "text/css";
        tag.setAttribute("data-" + name, "");
        if (cssString) {
            tag.appendChild(document.createTextNode(cssString));
        }
        var head = document.head || document.getElementsByTagName("head")[0];
        if (relativeToTag) {
            head.insertBefore(tag, relativeToTag);
        } else {
            head.appendChild(tag);
        }
        return tag;
    };
    _createClass(StyleSheet, [
        {
            key: "length",
            get: function get() {
                return this._rulesCount;
            }
        }
    ]);
    return StyleSheet;
}();
function invariant$1(condition, message) {
    if (!condition) {
        throw new Error("StyleSheet: " + message + ".");
    }
}
function hash(str) {
    var _$hash = 5381, i = str.length;
    while(i){
        _$hash = _$hash * 33 ^ str.charCodeAt(--i);
    }
    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed
   * integers. Since we want the results to be always positive, convert the
   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;
}
var stringHash = hash;
var sanitize = function(rule) {
    return rule.replace(/\/style/gi, "\\/style");
};
var cache = {};
/**
 * computeId
 *
 * Compute and memoize a jsx id from a basedId and optionally props.
 */ function computeId(baseId, props) {
    if (!props) {
        return "jsx-" + baseId;
    }
    var propsToString = String(props);
    var key = baseId + propsToString;
    if (!cache[key]) {
        cache[key] = "jsx-" + stringHash(baseId + "-" + propsToString);
    }
    return cache[key];
}
/**
 * computeSelector
 *
 * Compute and memoize dynamic selectors.
 */ function computeSelector(id, css) {
    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;
    // Sanitize SSR-ed CSS.
    // Client side code doesn't need to be sanitized since we use
    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).
    if (typeof window === "undefined") {
        css = sanitize(css);
    }
    var idcss = id + css;
    if (!cache[idcss]) {
        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);
    }
    return cache[idcss];
}
function mapRulesToStyle(cssRules, options) {
    if (options === void 0) options = {};
    return cssRules.map(function(args) {
        var id = args[0];
        var css = args[1];
        return /*#__PURE__*/ React__default["default"].createElement("style", {
            id: "__" + id,
            // Avoid warnings upon render with a key
            key: "__" + id,
            nonce: options.nonce ? options.nonce : undefined,
            dangerouslySetInnerHTML: {
                __html: css
            }
        });
    });
}
var StyleSheetRegistry = /*#__PURE__*/ function() {
    function StyleSheetRegistry(param) {
        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;
        this._sheet = styleSheet || new StyleSheet({
            name: "styled-jsx",
            optimizeForSpeed: optimizeForSpeed
        });
        this._sheet.inject();
        if (styleSheet && typeof optimizeForSpeed === "boolean") {
            this._sheet.setOptimizeForSpeed(optimizeForSpeed);
            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();
        }
        this._fromServer = undefined;
        this._indices = {};
        this._instancesCounts = {};
    }
    var _proto = StyleSheetRegistry.prototype;
    _proto.add = function add(props) {
        var _this = this;
        if (undefined === this._optimizeForSpeed) {
            this._optimizeForSpeed = Array.isArray(props.children);
            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);
            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();
        }
        if (typeof window !== "undefined" && !this._fromServer) {
            this._fromServer = this.selectFromServer();
            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {
                acc[tagName] = 0;
                return acc;
            }, {});
        }
        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;
        // Deduping: just increase the instances count.
        if (styleId in this._instancesCounts) {
            this._instancesCounts[styleId] += 1;
            return;
        }
        var indices = rules.map(function(rule) {
            return _this._sheet.insertRule(rule);
        }) // Filter out invalid rules
        .filter(function(index) {
            return index !== -1;
        });
        this._indices[styleId] = indices;
        this._instancesCounts[styleId] = 1;
    };
    _proto.remove = function remove(props) {
        var _this = this;
        var styleId = this.getIdAndRules(props).styleId;
        invariant(styleId in this._instancesCounts, "styleId: `" + styleId + "` not found");
        this._instancesCounts[styleId] -= 1;
        if (this._instancesCounts[styleId] < 1) {
            var tagFromServer = this._fromServer && this._fromServer[styleId];
            if (tagFromServer) {
                tagFromServer.parentNode.removeChild(tagFromServer);
                delete this._fromServer[styleId];
            } else {
                this._indices[styleId].forEach(function(index) {
                    return _this._sheet.deleteRule(index);
                });
                delete this._indices[styleId];
            }
            delete this._instancesCounts[styleId];
        }
    };
    _proto.update = function update(props, nextProps) {
        this.add(nextProps);
        this.remove(props);
    };
    _proto.flush = function flush() {
        this._sheet.flush();
        this._sheet.inject();
        this._fromServer = undefined;
        this._indices = {};
        this._instancesCounts = {};
    };
    _proto.cssRules = function cssRules() {
        var _this = this;
        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {
            return [
                styleId,
                _this._fromServer[styleId]
            ];
        }) : [];
        var cssRules = this._sheet.cssRules();
        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {
            return [
                styleId,
                _this._indices[styleId].map(function(index) {
                    return cssRules[index].cssText;
                }).join(_this._optimizeForSpeed ? "" : "\n")
            ];
        }) // filter out empty rules
        .filter(function(rule) {
            return Boolean(rule[1]);
        }));
    };
    _proto.styles = function styles(options) {
        return mapRulesToStyle(this.cssRules(), options);
    };
    _proto.getIdAndRules = function getIdAndRules(props) {
        var css = props.children, dynamic = props.dynamic, id = props.id;
        if (dynamic) {
            var styleId = computeId(id, dynamic);
            return {
                styleId: styleId,
                rules: Array.isArray(css) ? css.map(function(rule) {
                    return computeSelector(styleId, rule);
                }) : [
                    computeSelector(styleId, css)
                ]
            };
        }
        return {
            styleId: computeId(id),
            rules: Array.isArray(css) ? css : [
                css
            ]
        };
    };
    /**
   * selectFromServer
   *
   * Collects style tags from the document with id __jsx-XXX
   */ _proto.selectFromServer = function selectFromServer() {
        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]'));
        return elements.reduce(function(acc, element) {
            var id = element.id.slice(2);
            acc[id] = element;
            return acc;
        }, {});
    };
    return StyleSheetRegistry;
}();
function invariant(condition, message) {
    if (!condition) {
        throw new Error("StyleSheetRegistry: " + message + ".");
    }
}
var StyleSheetContext = /*#__PURE__*/ React.createContext(null);
StyleSheetContext.displayName = "StyleSheetContext";
function createStyleRegistry() {
    return new StyleSheetRegistry();
}
function StyleRegistry(param) {
    var configuredRegistry = param.registry, children = param.children;
    var rootRegistry = React.useContext(StyleSheetContext);
    var ref = React.useState({
        "StyleRegistry.useState[ref]": function() {
            return rootRegistry || configuredRegistry || createStyleRegistry();
        }
    }["StyleRegistry.useState[ref]"]), registry = ref[0];
    return /*#__PURE__*/ React__default["default"].createElement(StyleSheetContext.Provider, {
        value: registry
    }, children);
}
function useStyleRegistry() {
    return React.useContext(StyleSheetContext);
}
// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.
// https://github.com/reactwg/react-18/discussions/110
var useInsertionEffect = React__default["default"].useInsertionEffect || React__default["default"].useLayoutEffect;
var defaultRegistry = typeof window !== "undefined" ? createStyleRegistry() : undefined;
function JSXStyle(props) {
    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();
    // If `registry` does not exist, we do nothing here.
    if (!registry) {
        return null;
    }
    if (typeof window === "undefined") {
        registry.add(props);
        return null;
    }
    useInsertionEffect({
        "JSXStyle.useInsertionEffect": function() {
            registry.add(props);
            return ({
                "JSXStyle.useInsertionEffect": function() {
                    registry.remove(props);
                }
            })["JSXStyle.useInsertionEffect"];
        // props.children can be string[], will be striped since id is identical
        }
    }["JSXStyle.useInsertionEffect"], [
        props.id,
        String(props.dynamic)
    ]);
    return null;
}
JSXStyle.dynamic = function(info) {
    return info.map(function(tagInfo) {
        var baseId = tagInfo[0];
        var props = tagInfo[1];
        return computeId(baseId, props);
    }).join(" ");
};
exports.StyleRegistry = StyleRegistry;
exports.createStyleRegistry = createStyleRegistry;
exports.style = JSXStyle;
exports.useStyleRegistry = useStyleRegistry;
}}),
"[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/styled-jsx/dist/index/index.js [app-client] (ecmascript)").style;
}}),
"[project]/node_modules/allotment/dist/modern.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Allotment": (()=>xe),
    "LayoutPriority": (()=>fe),
    "setSashSize": (()=>Ie)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function _extends() {
    return _extends = ("TURBOPACK compile-time truthy", 1) ? Object.assign.bind() : ("TURBOPACK unreachable", undefined), _extends.apply(null, arguments);
}
;
var c = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {};
function h(e) {
    return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var l, f = {
    exports: {}
};
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/ var m, d, p, v = (l || (l = 1, m = f, function() {
    var e = {}.hasOwnProperty;
    function t() {
        for(var i = [], n = 0; n < arguments.length; n++){
            var r = arguments[n];
            if (r) {
                var s = typeof r;
                if ("string" === s || "number" === s) i.push(r);
                else if (Array.isArray(r)) {
                    if (r.length) {
                        var o = t.apply(null, r);
                        o && i.push(o);
                    }
                } else if ("object" === s) if (r.toString === Object.prototype.toString) for(var a in r)e.call(r, a) && r[a] && i.push(a);
                else i.push(r.toString());
            }
        }
        return i.join(" ");
    }
    m.exports ? (t.default = t, m.exports = t) : window.classNames = t;
}()), f.exports), y = h(v);
var b, g = h(function() {
    if (p) return d;
    p = 1;
    var e = NaN, t = "[object Symbol]", i = /^\s+|\s+$/g, n = /^[-+]0x[0-9a-f]+$/i, r = /^0b[01]+$/i, s = /^0o[0-7]+$/i, o = parseInt, a = Object.prototype.toString;
    function u(e) {
        var t = typeof e;
        return !!e && ("object" == t || "function" == t);
    }
    function c(c) {
        if ("number" == typeof c) return c;
        if (function(e) {
            return "symbol" == typeof e || function(e) {
                return !!e && "object" == typeof e;
            }(e) && a.call(e) == t;
        }(c)) return e;
        if (u(c)) {
            var h = "function" == typeof c.valueOf ? c.valueOf() : c;
            c = u(h) ? h + "" : h;
        }
        if ("string" != typeof c) return 0 === c ? c : +c;
        c = c.replace(i, "");
        var l = r.test(c);
        return l || s.test(c) ? o(c.slice(2), l ? 2 : 8) : n.test(c) ? e : +c;
    }
    return d = function(e, t, i) {
        return void 0 === i && (i = t, t = void 0), void 0 !== i && (i = (i = c(i)) == i ? i : 0), void 0 !== t && (t = (t = c(t)) == t ? t : 0), function(e, t, i) {
            return e == e && (void 0 !== i && (e = e <= i ? e : i), void 0 !== t && (e = e >= t ? e : t)), e;
        }(c(e), t, i);
    };
}()), z = {
    exports: {}
};
var w = (b || (b = 1, function(e, t) {
    var i = "__lodash_hash_undefined__", n = 1, r = 2, s = 9007199254740991, o = "[object Arguments]", a = "[object Array]", u = "[object AsyncFunction]", h = "[object Boolean]", l = "[object Date]", f = "[object Error]", m = "[object Function]", d = "[object GeneratorFunction]", p = "[object Map]", v = "[object Number]", y = "[object Null]", b = "[object Object]", g = "[object Promise]", z = "[object Proxy]", w = "[object RegExp]", S = "[object Set]", _ = "[object String]", I = "[object Symbol]", x = "[object Undefined]", E = "[object WeakMap]", V = "[object ArrayBuffer]", j = "[object DataView]", L = /^\[object .+?Constructor\]$/, O = /^(?:0|[1-9]\d*)$/, D = {};
    D["[object Float32Array]"] = D["[object Float64Array]"] = D["[object Int8Array]"] = D["[object Int16Array]"] = D["[object Int32Array]"] = D["[object Uint8Array]"] = D["[object Uint8ClampedArray]"] = D["[object Uint16Array]"] = D["[object Uint32Array]"] = !0, D[o] = D[a] = D[V] = D[h] = D[j] = D[l] = D[f] = D[m] = D[p] = D[v] = D[b] = D[w] = D[S] = D[_] = D[E] = !1;
    var N = "object" == typeof c && c && c.Object === Object && c, M = "object" == typeof self && self && self.Object === Object && self, P = N || M || Function("return this")(), A = t && !t.nodeType && t, T = A && e && !e.nodeType && e, C = T && T.exports === A, F = C && N.process, k = function() {
        try {
            return F && F.binding && F.binding("util");
        } catch (e) {}
    }(), $ = k && k.isTypedArray;
    function H(e, t) {
        for(var i = -1, n = null == e ? 0 : e.length; ++i < n;)if (t(e[i], i, e)) return !0;
        return !1;
    }
    function Y(e) {
        var t = -1, i = Array(e.size);
        return e.forEach(function(e, n) {
            i[++t] = [
                n,
                e
            ];
        }), i;
    }
    function B(e) {
        var t = -1, i = Array(e.size);
        return e.forEach(function(e) {
            i[++t] = e;
        }), i;
    }
    var R, W, G, U = Array.prototype, X = Function.prototype, J = Object.prototype, K = P["__core-js_shared__"], Z = X.toString, Q = J.hasOwnProperty, q = (R = /[^.]+$/.exec(K && K.keys && K.keys.IE_PROTO || "")) ? "Symbol(src)_1." + R : "", ee = J.toString, te = RegExp("^" + Z.call(Q).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"), ie = C ? P.Buffer : void 0, ne = P.Symbol, re = P.Uint8Array, se = J.propertyIsEnumerable, oe = U.splice, ae = ne ? ne.toStringTag : void 0, ue = Object.getOwnPropertySymbols, ce = ie ? ie.isBuffer : void 0, he = (W = Object.keys, G = Object, function(e) {
        return W(G(e));
    }), le = ke(P, "DataView"), fe = ke(P, "Map"), me = ke(P, "Promise"), de = ke(P, "Set"), pe = ke(P, "WeakMap"), ve = ke(Object, "create"), ye = Be(le), be = Be(fe), ge = Be(me), ze = Be(de), we = Be(pe), Se = ne ? ne.prototype : void 0, _e = Se ? Se.valueOf : void 0;
    function Ie(e) {
        var t = -1, i = null == e ? 0 : e.length;
        for(this.clear(); ++t < i;){
            var n = e[t];
            this.set(n[0], n[1]);
        }
    }
    function xe(e) {
        var t = -1, i = null == e ? 0 : e.length;
        for(this.clear(); ++t < i;){
            var n = e[t];
            this.set(n[0], n[1]);
        }
    }
    function Ee(e) {
        var t = -1, i = null == e ? 0 : e.length;
        for(this.clear(); ++t < i;){
            var n = e[t];
            this.set(n[0], n[1]);
        }
    }
    function Ve(e) {
        var t = -1, i = null == e ? 0 : e.length;
        for(this.__data__ = new Ee(); ++t < i;)this.add(e[t]);
    }
    function je(e) {
        var t = this.__data__ = new xe(e);
        this.size = t.size;
    }
    function Le(e, t) {
        var i = Ge(e), n = !i && We(e), r = !i && !n && Ue(e), s = !i && !n && !r && Qe(e), o = i || n || r || s, a = o ? function(e, t) {
            for(var i = -1, n = Array(e); ++i < e;)n[i] = t(i);
            return n;
        }(e.length, String) : [], u = a.length;
        for(var c in e)!Q.call(e, c) || o && ("length" == c || r && ("offset" == c || "parent" == c) || s && ("buffer" == c || "byteLength" == c || "byteOffset" == c) || Ye(c, u)) || a.push(c);
        return a;
    }
    function Oe(e, t) {
        for(var i = e.length; i--;)if (Re(e[i][0], t)) return i;
        return -1;
    }
    function De(e) {
        return null == e ? void 0 === e ? x : y : ae && ae in Object(e) ? function(e) {
            var t = Q.call(e, ae), i = e[ae];
            try {
                e[ae] = void 0;
                var n = !0;
            } catch (e) {}
            var r = ee.call(e);
            return n && (t ? e[ae] = i : delete e[ae]), r;
        }(e) : function(e) {
            return ee.call(e);
        }(e);
    }
    function Ne(e) {
        return Ze(e) && De(e) == o;
    }
    function Me(e, t, i, s, u) {
        return e === t || (null == e || null == t || !Ze(e) && !Ze(t) ? e != e && t != t : function(e, t, i, s, u, c) {
            var m = Ge(e), d = Ge(t), y = m ? a : He(e), g = d ? a : He(t), z = (y = y == o ? b : y) == b, x = (g = g == o ? b : g) == b, E = y == g;
            if (E && Ue(e)) {
                if (!Ue(t)) return !1;
                m = !0, z = !1;
            }
            if (E && !z) return c || (c = new je()), m || Qe(e) ? Te(e, t, i, s, u, c) : function(e, t, i, s, o, a, u) {
                switch(i){
                    case j:
                        if (e.byteLength != t.byteLength || e.byteOffset != t.byteOffset) return !1;
                        e = e.buffer, t = t.buffer;
                    case V:
                        return !(e.byteLength != t.byteLength || !a(new re(e), new re(t)));
                    case h:
                    case l:
                    case v:
                        return Re(+e, +t);
                    case f:
                        return e.name == t.name && e.message == t.message;
                    case w:
                    case _:
                        return e == t + "";
                    case p:
                        var c = Y;
                    case S:
                        var m = s & n;
                        if (c || (c = B), e.size != t.size && !m) return !1;
                        var d = u.get(e);
                        if (d) return d == t;
                        s |= r, u.set(e, t);
                        var y = Te(c(e), c(t), s, o, a, u);
                        return u.delete(e), y;
                    case I:
                        if (_e) return _e.call(e) == _e.call(t);
                }
                return !1;
            }(e, t, y, i, s, u, c);
            if (!(i & n)) {
                var L = z && Q.call(e, "__wrapped__"), O = x && Q.call(t, "__wrapped__");
                if (L || O) {
                    var D = L ? e.value() : e, N = O ? t.value() : t;
                    return c || (c = new je()), u(D, N, i, s, c);
                }
            }
            return !!E && (c || (c = new je()), function(e, t, i, r, s, o) {
                var a = i & n, u = Ce(e), c = u.length, h = Ce(t), l = h.length;
                if (c != l && !a) return !1;
                for(var f = c; f--;){
                    var m = u[f];
                    if (!(a ? m in t : Q.call(t, m))) return !1;
                }
                var d = o.get(e);
                if (d && o.get(t)) return d == t;
                var p = !0;
                o.set(e, t), o.set(t, e);
                for(var v = a; ++f < c;){
                    var y = e[m = u[f]], b = t[m];
                    if (r) var g = a ? r(b, y, m, t, e, o) : r(y, b, m, e, t, o);
                    if (!(void 0 === g ? y === b || s(y, b, i, r, o) : g)) {
                        p = !1;
                        break;
                    }
                    v || (v = "constructor" == m);
                }
                if (p && !v) {
                    var z = e.constructor, w = t.constructor;
                    z == w || !("constructor" in e) || !("constructor" in t) || "function" == typeof z && z instanceof z && "function" == typeof w && w instanceof w || (p = !1);
                }
                return o.delete(e), o.delete(t), p;
            }(e, t, i, s, u, c));
        }(e, t, i, s, Me, u));
    }
    function Pe(e) {
        return !(!Ke(e) || function(e) {
            return !!q && q in e;
        }(e)) && (Xe(e) ? te : L).test(Be(e));
    }
    function Ae(e) {
        if (i = (t = e) && t.constructor, n = "function" == typeof i && i.prototype || J, t !== n) return he(e);
        var t, i, n, r = [];
        for(var s in Object(e))Q.call(e, s) && "constructor" != s && r.push(s);
        return r;
    }
    function Te(e, t, i, s, o, a) {
        var u = i & n, c = e.length, h = t.length;
        if (c != h && !(u && h > c)) return !1;
        var l = a.get(e);
        if (l && a.get(t)) return l == t;
        var f = -1, m = !0, d = i & r ? new Ve() : void 0;
        for(a.set(e, t), a.set(t, e); ++f < c;){
            var p = e[f], v = t[f];
            if (s) var y = u ? s(v, p, f, t, e, a) : s(p, v, f, e, t, a);
            if (void 0 !== y) {
                if (y) continue;
                m = !1;
                break;
            }
            if (d) {
                if (!H(t, function(e, t) {
                    if (n = t, !d.has(n) && (p === e || o(p, e, i, s, a))) return d.push(t);
                    var n;
                })) {
                    m = !1;
                    break;
                }
            } else if (p !== v && !o(p, v, i, s, a)) {
                m = !1;
                break;
            }
        }
        return a.delete(e), a.delete(t), m;
    }
    function Ce(e) {
        return function(e, t, i) {
            var n = t(e);
            return Ge(e) ? n : function(e, t) {
                for(var i = -1, n = t.length, r = e.length; ++i < n;)e[r + i] = t[i];
                return e;
            }(n, i(e));
        }(e, qe, $e);
    }
    function Fe(e, t) {
        var i, n, r = e.__data__;
        return ("string" == (n = typeof (i = t)) || "number" == n || "symbol" == n || "boolean" == n ? "__proto__" !== i : null === i) ? r["string" == typeof t ? "string" : "hash"] : r.map;
    }
    function ke(e, t) {
        var i = function(e, t) {
            return null == e ? void 0 : e[t];
        }(e, t);
        return Pe(i) ? i : void 0;
    }
    Ie.prototype.clear = function() {
        this.__data__ = ve ? ve(null) : {}, this.size = 0;
    }, Ie.prototype.delete = function(e) {
        var t = this.has(e) && delete this.__data__[e];
        return this.size -= t ? 1 : 0, t;
    }, Ie.prototype.get = function(e) {
        var t = this.__data__;
        if (ve) {
            var n = t[e];
            return n === i ? void 0 : n;
        }
        return Q.call(t, e) ? t[e] : void 0;
    }, Ie.prototype.has = function(e) {
        var t = this.__data__;
        return ve ? void 0 !== t[e] : Q.call(t, e);
    }, Ie.prototype.set = function(e, t) {
        var n = this.__data__;
        return this.size += this.has(e) ? 0 : 1, n[e] = ve && void 0 === t ? i : t, this;
    }, xe.prototype.clear = function() {
        this.__data__ = [], this.size = 0;
    }, xe.prototype.delete = function(e) {
        var t = this.__data__, i = Oe(t, e);
        return !(i < 0 || (i == t.length - 1 ? t.pop() : oe.call(t, i, 1), --this.size, 0));
    }, xe.prototype.get = function(e) {
        var t = this.__data__, i = Oe(t, e);
        return i < 0 ? void 0 : t[i][1];
    }, xe.prototype.has = function(e) {
        return Oe(this.__data__, e) > -1;
    }, xe.prototype.set = function(e, t) {
        var i = this.__data__, n = Oe(i, e);
        return n < 0 ? (++this.size, i.push([
            e,
            t
        ])) : i[n][1] = t, this;
    }, Ee.prototype.clear = function() {
        this.size = 0, this.__data__ = {
            hash: new Ie(),
            map: new (fe || xe)(),
            string: new Ie()
        };
    }, Ee.prototype.delete = function(e) {
        var t = Fe(this, e).delete(e);
        return this.size -= t ? 1 : 0, t;
    }, Ee.prototype.get = function(e) {
        return Fe(this, e).get(e);
    }, Ee.prototype.has = function(e) {
        return Fe(this, e).has(e);
    }, Ee.prototype.set = function(e, t) {
        var i = Fe(this, e), n = i.size;
        return i.set(e, t), this.size += i.size == n ? 0 : 1, this;
    }, Ve.prototype.add = Ve.prototype.push = function(e) {
        return this.__data__.set(e, i), this;
    }, Ve.prototype.has = function(e) {
        return this.__data__.has(e);
    }, je.prototype.clear = function() {
        this.__data__ = new xe(), this.size = 0;
    }, je.prototype.delete = function(e) {
        var t = this.__data__, i = t.delete(e);
        return this.size = t.size, i;
    }, je.prototype.get = function(e) {
        return this.__data__.get(e);
    }, je.prototype.has = function(e) {
        return this.__data__.has(e);
    }, je.prototype.set = function(e, t) {
        var i = this.__data__;
        if (i instanceof xe) {
            var n = i.__data__;
            if (!fe || n.length < 199) return n.push([
                e,
                t
            ]), this.size = ++i.size, this;
            i = this.__data__ = new Ee(n);
        }
        return i.set(e, t), this.size = i.size, this;
    };
    var $e = ue ? function(e) {
        return null == e ? [] : (e = Object(e), function(e, t) {
            for(var i = -1, n = null == e ? 0 : e.length, r = 0, s = []; ++i < n;){
                var o = e[i];
                t(o, i, e) && (s[r++] = o);
            }
            return s;
        }(ue(e), function(t) {
            return se.call(e, t);
        }));
    } : function() {
        return [];
    }, He = De;
    function Ye(e, t) {
        return !!(t = null == t ? s : t) && ("number" == typeof e || O.test(e)) && e > -1 && e % 1 == 0 && e < t;
    }
    function Be(e) {
        if (null != e) {
            try {
                return Z.call(e);
            } catch (e) {}
            try {
                return e + "";
            } catch (e) {}
        }
        return "";
    }
    function Re(e, t) {
        return e === t || e != e && t != t;
    }
    (le && He(new le(new ArrayBuffer(1))) != j || fe && He(new fe()) != p || me && He(me.resolve()) != g || de && He(new de()) != S || pe && He(new pe()) != E) && (He = function(e) {
        var t = De(e), i = t == b ? e.constructor : void 0, n = i ? Be(i) : "";
        if (n) switch(n){
            case ye:
                return j;
            case be:
                return p;
            case ge:
                return g;
            case ze:
                return S;
            case we:
                return E;
        }
        return t;
    });
    var We = Ne(function() {
        return arguments;
    }()) ? Ne : function(e) {
        return Ze(e) && Q.call(e, "callee") && !se.call(e, "callee");
    }, Ge = Array.isArray, Ue = ce || function() {
        return !1;
    };
    function Xe(e) {
        if (!Ke(e)) return !1;
        var t = De(e);
        return t == m || t == d || t == u || t == z;
    }
    function Je(e) {
        return "number" == typeof e && e > -1 && e % 1 == 0 && e <= s;
    }
    function Ke(e) {
        var t = typeof e;
        return null != e && ("object" == t || "function" == t);
    }
    function Ze(e) {
        return null != e && "object" == typeof e;
    }
    var Qe = $ ? function(e) {
        return function(t) {
            return e(t);
        };
    }($) : function(e) {
        return Ze(e) && Je(e.length) && !!D[De(e)];
    };
    function qe(e) {
        return null != (t = e) && Je(t.length) && !Xe(t) ? Le(e) : Ae(e);
        "TURBOPACK unreachable";
        var t;
    }
    e.exports = function(e, t) {
        return Me(e, t);
    };
}(z, z.exports)), z.exports), S = h(w);
function _(e, t, i) {
    return e[t] ? e[t][0] ? e[t][0][i] : e[t][i] : "contentBoxSize" === t ? e.contentRect["inlineSize" === i ? "width" : "height"] : void 0;
}
function I(e) {
    void 0 === e && (e = {});
    var o = e.onResize, a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(void 0);
    a.current = o;
    var u = e.round || Math.round, c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        width: void 0,
        height: void 0
    }), l = h[0], f = h[1], m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        return m.current = !1, function() {
            m.current = !0;
        };
    }, []);
    var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        width: void 0,
        height: void 0
    }), p = function(e, i) {
        var s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
        o.current = i;
        var a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function() {
            u();
        });
        var u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(function() {
            var t = a.current, i = o.current, n = t || (i ? i instanceof Element ? i : i.current : null);
            s.current && s.current.element === n && s.current.subscriber === e || (s.current && s.current.cleanup && s.current.cleanup(), s.current = {
                element: n,
                subscriber: e,
                cleanup: n ? e(n) : void 0
            });
        }, [
            e
        ]);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function() {
            return function() {
                s.current && s.current.cleanup && (s.current.cleanup(), s.current = null);
            };
        }, []), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(function(e) {
            a.current = e, u();
        }, [
            u
        ]);
    }((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(function(t) {
        return c.current && c.current.box === e.box && c.current.round === u || (c.current = {
            box: e.box,
            round: u,
            instance: new ResizeObserver(function(t) {
                var i = t[0], n = "border-box" === e.box ? "borderBoxSize" : "device-pixel-content-box" === e.box ? "devicePixelContentBoxSize" : "contentBoxSize", r = _(i, n, "inlineSize"), s = _(i, n, "blockSize"), o = r ? u(r) : void 0, c = s ? u(s) : void 0;
                if (d.current.width !== o || d.current.height !== c) {
                    var h = {
                        width: o,
                        height: c
                    };
                    d.current.width = o, d.current.height = c, a.current ? a.current(h) : m.current || f(h);
                }
            })
        }), c.current.instance.observe(t, {
            box: e.box
        }), function() {
            c.current && c.current.instance.unobserve(t);
        };
    }, [
        e.box,
        u
    ]), e.ref);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        return {
            ref: p,
            width: l.width,
            height: l.height
        };
    }, [
        p,
        l.width,
        l.height
    ]);
}
var x = "allotment-module_splitView__L-yRc", E = "allotment-module_sashContainer__fzwJF", V = "allotment-module_splitViewContainer__rQnVa", j = "allotment-module_splitViewView__MGZ6O", L = "allotment-module_vertical__WSwwa", O = "allotment-module_horizontal__7doS8", D = "allotment-module_separatorBorder__x-rDS";
let N, M = !1, P = !1;
"object" == typeof navigator && (N = navigator.userAgent, P = N.indexOf("Macintosh") >= 0, M = (N.indexOf("Macintosh") >= 0 || N.indexOf("iPad") >= 0 || N.indexOf("iPhone") >= 0) && !!navigator.maxTouchPoints && navigator.maxTouchPoints > 0);
const A = M, T = P, C = "undefined" != typeof window && void 0 !== window.document && void 0 !== window.document.createElement ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
class F {
    constructor(){
        this._size = void 0;
    }
    getSize() {
        return this._size;
    }
    setSize(e) {
        this._size = e;
    }
}
function k(e, t) {
    const i = e.length, n = i - t.length;
    return n >= 0 && e.slice(n, i) === t;
}
var $, H = {
    exports: {}
};
var Y, B, R = ($ || ($ = 1, function(e) {
    var t = Object.prototype.hasOwnProperty, i = "~";
    function n() {}
    function r(e, t, i) {
        this.fn = e, this.context = t, this.once = i || !1;
    }
    function s(e, t, n, s, o) {
        if ("function" != typeof n) throw new TypeError("The listener must be a function");
        var a = new r(n, s || e, o), u = i ? i + t : t;
        return e._events[u] ? e._events[u].fn ? e._events[u] = [
            e._events[u],
            a
        ] : e._events[u].push(a) : (e._events[u] = a, e._eventsCount++), e;
    }
    function o(e, t) {
        0 == --e._eventsCount ? e._events = new n() : delete e._events[t];
    }
    function a() {
        this._events = new n(), this._eventsCount = 0;
    }
    Object.create && (n.prototype = Object.create(null), new n().__proto__ || (i = !1)), a.prototype.eventNames = function() {
        var e, n, r = [];
        if (0 === this._eventsCount) return r;
        for(n in e = this._events)t.call(e, n) && r.push(i ? n.slice(1) : n);
        return Object.getOwnPropertySymbols ? r.concat(Object.getOwnPropertySymbols(e)) : r;
    }, a.prototype.listeners = function(e) {
        var t = i ? i + e : e, n = this._events[t];
        if (!n) return [];
        if (n.fn) return [
            n.fn
        ];
        for(var r = 0, s = n.length, o = new Array(s); r < s; r++)o[r] = n[r].fn;
        return o;
    }, a.prototype.listenerCount = function(e) {
        var t = i ? i + e : e, n = this._events[t];
        return n ? n.fn ? 1 : n.length : 0;
    }, a.prototype.emit = function(e, t, n, r, s, o) {
        var a = i ? i + e : e;
        if (!this._events[a]) return !1;
        var u, c, h = this._events[a], l = arguments.length;
        if (h.fn) {
            switch(h.once && this.removeListener(e, h.fn, void 0, !0), l){
                case 1:
                    return h.fn.call(h.context), !0;
                case 2:
                    return h.fn.call(h.context, t), !0;
                case 3:
                    return h.fn.call(h.context, t, n), !0;
                case 4:
                    return h.fn.call(h.context, t, n, r), !0;
                case 5:
                    return h.fn.call(h.context, t, n, r, s), !0;
                case 6:
                    return h.fn.call(h.context, t, n, r, s, o), !0;
            }
            for(c = 1, u = new Array(l - 1); c < l; c++)u[c - 1] = arguments[c];
            h.fn.apply(h.context, u);
        } else {
            var f, m = h.length;
            for(c = 0; c < m; c++)switch(h[c].once && this.removeListener(e, h[c].fn, void 0, !0), l){
                case 1:
                    h[c].fn.call(h[c].context);
                    break;
                case 2:
                    h[c].fn.call(h[c].context, t);
                    break;
                case 3:
                    h[c].fn.call(h[c].context, t, n);
                    break;
                case 4:
                    h[c].fn.call(h[c].context, t, n, r);
                    break;
                default:
                    if (!u) for(f = 1, u = new Array(l - 1); f < l; f++)u[f - 1] = arguments[f];
                    h[c].fn.apply(h[c].context, u);
            }
        }
        return !0;
    }, a.prototype.on = function(e, t, i) {
        return s(this, e, t, i, !1);
    }, a.prototype.once = function(e, t, i) {
        return s(this, e, t, i, !0);
    }, a.prototype.removeListener = function(e, t, n, r) {
        var s = i ? i + e : e;
        if (!this._events[s]) return this;
        if (!t) return o(this, s), this;
        var a = this._events[s];
        if (a.fn) a.fn !== t || r && !a.once || n && a.context !== n || o(this, s);
        else {
            for(var u = 0, c = [], h = a.length; u < h; u++)(a[u].fn !== t || r && !a[u].once || n && a[u].context !== n) && c.push(a[u]);
            c.length ? this._events[s] = 1 === c.length ? c[0] : c : o(this, s);
        }
        return this;
    }, a.prototype.removeAllListeners = function(e) {
        var t;
        return e ? (t = i ? i + e : e, this._events[t] && o(this, t)) : (this._events = new n(), this._eventsCount = 0), this;
    }, a.prototype.off = a.prototype.removeListener, a.prototype.addListener = a.prototype.on, a.prefixed = i, a.EventEmitter = a, e.exports = a;
}(H)), H.exports), W = h(R);
function G(e, t) {
    const i = e.indexOf(t);
    i > -1 && (e.splice(i, 1), e.unshift(t));
}
function U(e, t) {
    const i = e.indexOf(t);
    i > -1 && (e.splice(i, 1), e.push(t));
}
function X(e, t, i = 1) {
    const n = Math.max(0, Math.ceil((t - e) / i)), r = new Array(n);
    let s = -1;
    for(; ++s < n;)r[s] = e + s * i;
    return r;
}
var J = h(function() {
    if (B) return Y;
    B = 1;
    var e = NaN, t = "[object Symbol]", i = /^\s+|\s+$/g, n = /^[-+]0x[0-9a-f]+$/i, r = /^0b[01]+$/i, s = /^0o[0-7]+$/i, o = parseInt, a = "object" == typeof c && c && c.Object === Object && c, u = "object" == typeof self && self && self.Object === Object && self, h = a || u || Function("return this")(), l = Object.prototype.toString, f = Math.max, m = Math.min, d = function d() {
        return h.Date.now();
    };
    function p(e) {
        var t = typeof e;
        return !!e && ("object" == t || "function" == t);
    }
    function v(a) {
        if ("number" == typeof a) return a;
        if (function(e) {
            return "symbol" == typeof e || function(e) {
                return !!e && "object" == typeof e;
            }(e) && l.call(e) == t;
        }(a)) return e;
        if (p(a)) {
            var u = "function" == typeof a.valueOf ? a.valueOf() : a;
            a = p(u) ? u + "" : u;
        }
        if ("string" != typeof a) return 0 === a ? a : +a;
        a = a.replace(i, "");
        var c = r.test(a);
        return c || s.test(a) ? o(a.slice(2), c ? 2 : 8) : n.test(a) ? e : +a;
    }
    return Y = function(e, t, i) {
        var n, r, s, o, a, u, c = 0, h = !1, l = !1, y = !0;
        if ("function" != typeof e) throw new TypeError("Expected a function");
        function b(t) {
            var i = n, s = r;
            return n = r = void 0, c = t, o = e.apply(s, i);
        }
        function g(e) {
            var i = e - u;
            return void 0 === u || i >= t || i < 0 || l && e - c >= s;
        }
        function z() {
            var e = d();
            if (g(e)) return w(e);
            a = setTimeout(z, function(e) {
                var i = t - (e - u);
                return l ? m(i, s - (e - c)) : i;
            }(e));
        }
        function w(e) {
            return a = void 0, y && n ? b(e) : (n = r = void 0, o);
        }
        function S() {
            var e = d(), i = g(e);
            if (n = arguments, r = this, u = e, i) {
                if (void 0 === a) return function(e) {
                    return c = e, a = setTimeout(z, t), h ? b(e) : o;
                }(u);
                if (l) return a = setTimeout(z, t), b(u);
            }
            return void 0 === a && (a = setTimeout(z, t)), o;
        }
        return t = v(t) || 0, p(i) && (h = !!i.leading, s = (l = "maxWait" in i) ? f(v(i.maxWait) || 0, t) : s, y = "trailing" in i ? !!i.trailing : y), S.cancel = function() {
            void 0 !== a && clearTimeout(a), c = 0, n = u = r = a = void 0;
        }, S.flush = function() {
            return void 0 === a ? o : w(d());
        }, S;
    };
}()), K = "sash-module_sash__K-9lB", Z = "sash-module_disabled__Hm-wx", Q = "sash-module_mac__Jf6OJ", q = "sash-module_vertical__pB-rs", ee = "sash-module_minimum__-UKxp", te = "sash-module_maximum__TCWxD", ie = "sash-module_horizontal__kFbiw", ne = "sash-module_hover__80W6I", re = "sash-module_active__bJspD";
let se = function(e) {
    return e.Vertical = "VERTICAL", e.Horizontal = "HORIZONTAL", e;
}({}), oe = function(e) {
    return e.Disabled = "DISABLED", e.Minimum = "MINIMUM", e.Maximum = "MAXIMUM", e.Enabled = "ENABLED", e;
}({}), ae = A ? 20 : 8;
const ue = new W();
class ce extends W {
    get state() {
        return this._state;
    }
    set state(e) {
        this._state !== e && (this.el.classList.toggle(Z, e === oe.Disabled), this.el.classList.toggle("sash-disabled", e === oe.Disabled), this.el.classList.toggle(ee, e === oe.Minimum), this.el.classList.toggle("sash-minimum", e === oe.Minimum), this.el.classList.toggle(te, e === oe.Maximum), this.el.classList.toggle("sash-maximum", e === oe.Maximum), this._state = e, this.emit("enablementChange", e));
    }
    constructor(e, t, i){
        var _i$orientation;
        super(), this.el = void 0, this.layoutProvider = void 0, this.orientation = void 0, this.size = void 0, this.hoverDelay = 300, this.hoverDelayer = J((e)=>e.classList.add("sash-hover", ne), this.hoverDelay), this._state = oe.Enabled, this.onPointerStart = (e)=>{
            const t = e.pageX, i = e.pageY, n = {
                startX: t,
                currentX: t,
                startY: i,
                currentY: i
            };
            this.el.classList.add("sash-active", re), this.emit("start", n), this.el.setPointerCapture(e.pointerId);
            const r = (e)=>{
                e.preventDefault();
                const n = {
                    startX: t,
                    currentX: e.pageX,
                    startY: i,
                    currentY: e.pageY
                };
                this.emit("change", n);
            }, s = (e)=>{
                e.preventDefault(), this.el.classList.remove("sash-active", re), this.hoverDelayer.cancel(), this.emit("end"), this.el.releasePointerCapture(e.pointerId), window.removeEventListener("pointermove", r), window.removeEventListener("pointerup", s);
            };
            window.addEventListener("pointermove", r), window.addEventListener("pointerup", s);
        }, this.onPointerDoublePress = ()=>{
            this.emit("reset");
        }, this.onMouseEnter = ()=>{
            this.el.classList.contains(re) ? (this.hoverDelayer.cancel(), this.el.classList.add("sash-hover", ne)) : this.hoverDelayer(this.el);
        }, this.onMouseLeave = ()=>{
            this.hoverDelayer.cancel(), this.el.classList.remove("sash-hover", ne);
        }, this.el = document.createElement("div"), this.el.classList.add("sash", K), this.el.dataset.testid = "sash", e.append(this.el), T && this.el.classList.add("sash-mac", Q), this.el.addEventListener("pointerdown", this.onPointerStart), this.el.addEventListener("dblclick", this.onPointerDoublePress), this.el.addEventListener("mouseenter", this.onMouseEnter), this.el.addEventListener("mouseleave", this.onMouseLeave), "number" == typeof i.size ? (this.size = i.size, i.orientation === se.Vertical ? this.el.style.width = `${this.size}px` : this.el.style.height = `${this.size}px`) : (this.size = ae, ue.on("onDidChangeGlobalSize", (e)=>{
            this.size = e, this.layout();
        })), this.layoutProvider = t, this.orientation = (_i$orientation = i.orientation) != null ? _i$orientation : se.Vertical, this.orientation === se.Horizontal ? (this.el.classList.add("sash-horizontal", ie), this.el.classList.remove("sash-vertical", q)) : (this.el.classList.remove("sash-horizontal", ie), this.el.classList.add("sash-vertical", q)), this.layout();
    }
    layout() {
        if (this.orientation === se.Vertical) {
            const e = this.layoutProvider;
            this.el.style.left = e.getVerticalSashLeft(this) - this.size / 2 + "px", e.getVerticalSashTop && (this.el.style.top = e.getVerticalSashTop(this) + "px"), e.getVerticalSashHeight && (this.el.style.height = e.getVerticalSashHeight(this) + "px");
        } else {
            const e = this.layoutProvider;
            this.el.style.top = e.getHorizontalSashTop(this) - this.size / 2 + "px", e.getHorizontalSashLeft && (this.el.style.left = e.getHorizontalSashLeft(this) + "px"), e.getHorizontalSashWidth && (this.el.style.width = e.getHorizontalSashWidth(this) + "px");
        }
    }
    dispose() {
        this.el.removeEventListener("pointerdown", this.onPointerStart), this.el.removeEventListener("dblclick", this.onPointerDoublePress), this.el.removeEventListener("mouseenter", this.onMouseEnter), this.el.removeEventListener("mouseleave", ()=>this.onMouseLeave), this.el.remove();
    }
}
let he;
var le;
(le = he || (he = {})).Distribute = {
    type: "distribute"
}, le.Split = function(e) {
    return {
        type: "split",
        index: e
    };
}, le.Invisible = function(e) {
    return {
        type: "invisible",
        cachedVisibleSize: e
    };
};
let fe = function(e) {
    return e.Normal = "NORMAL", e.Low = "LOW", e.High = "HIGH", e;
}({});
class me {
    constructor(e, t, i){
        this.container = void 0, this.view = void 0, this._size = void 0, this._cachedVisibleSize = void 0, this.container = e, this.view = t, this.container.classList.add("split-view-view", j), this.container.dataset.testid = "split-view-view", "number" == typeof i ? (this._size = i, this._cachedVisibleSize = void 0, e.classList.add("split-view-view-visible")) : (this._size = 0, this._cachedVisibleSize = i.cachedVisibleSize);
    }
    set size(e) {
        this._size = e;
    }
    get size() {
        return this._size;
    }
    get priority() {
        return this.view.priority;
    }
    get snap() {
        return !!this.view.snap;
    }
    get cachedVisibleSize() {
        return this._cachedVisibleSize;
    }
    get visible() {
        return void 0 === this._cachedVisibleSize;
    }
    setVisible(e, t) {
        e !== this.visible && (e ? (this.size = g(this._cachedVisibleSize, this.viewMinimumSize, this.viewMaximumSize), this._cachedVisibleSize = void 0) : (this._cachedVisibleSize = "number" == typeof t ? t : this.size, this.size = 0), this.container.classList.toggle("split-view-view-visible", e), this.view.setVisible && this.view.setVisible(e));
    }
    get minimumSize() {
        return this.visible ? this.view.minimumSize : 0;
    }
    get viewMinimumSize() {
        return this.view.minimumSize;
    }
    get maximumSize() {
        return this.visible ? this.view.maximumSize : 0;
    }
    get viewMaximumSize() {
        return this.view.maximumSize;
    }
    set enabled(e) {
        this.container.style.pointerEvents = e ? "" : "none";
    }
    layout(e) {
        this.layoutContainer(e), this.view.layout(this.size, e);
    }
}
class de extends me {
    layoutContainer(e) {
        this.container.style.left = `${e}px`, this.container.style.width = `${this.size}px`;
    }
}
class pe extends me {
    layoutContainer(e) {
        this.container.style.top = `${e}px`, this.container.style.height = `${this.size}px`;
    }
}
class ve extends W {
    get startSnappingEnabled() {
        return this._startSnappingEnabled;
    }
    set startSnappingEnabled(e) {
        this._startSnappingEnabled !== e && (this._startSnappingEnabled = e, this.updateSashEnablement());
    }
    get endSnappingEnabled() {
        return this._endSnappingEnabled;
    }
    set endSnappingEnabled(e) {
        this._endSnappingEnabled !== e && (this._endSnappingEnabled = e, this.updateSashEnablement());
    }
    constructor(e, t = {}, i, n, r){
        var _t$orientation, _t$proportionalLayout;
        if (super(), this.onDidChange = void 0, this.onDidDragStart = void 0, this.onDidDragEnd = void 0, this.orientation = void 0, this.sashContainer = void 0, this.size = 0, this.contentSize = 0, this.proportions = void 0, this.viewItems = [], this.sashItems = [], this.sashDragState = void 0, this.proportionalLayout = void 0, this.getSashOrthogonalSize = void 0, this._startSnappingEnabled = !0, this._endSnappingEnabled = !0, this.onSashEnd = (e)=>{
            this.emit("sashchange", e), this.saveProportions();
            for (const _e2 of this.viewItems)_e2.enabled = !0;
        }, this.orientation = (_t$orientation = t.orientation) != null ? _t$orientation : se.Vertical, this.proportionalLayout = (_t$proportionalLayout = t.proportionalLayout) != null ? _t$proportionalLayout : !0, this.getSashOrthogonalSize = t.getSashOrthogonalSize, i && (this.onDidChange = i), n && (this.onDidDragStart = n), r && (this.onDidDragEnd = r), this.sashContainer = document.createElement("div"), this.sashContainer.classList.add("sash-container", E), e.prepend(this.sashContainer), t.descriptor) {
            this.size = t.descriptor.size;
            for (const [_e3, _i] of t.descriptor.views.entries()){
                const _t = _i.size, _n = _i.container, _r = _i.view;
                this.addView(_n, _r, _t, _e3, !0);
            }
            this.contentSize = this.viewItems.reduce((e, t)=>e + t.size, 0), this.saveProportions();
        }
    }
    addView(e, t, i, n = this.viewItems.length, r) {
        let s;
        s = "number" == typeof i ? i : "split" === i.type ? this.getViewSize(i.index) / 2 : "invisible" === i.type ? {
            cachedVisibleSize: i.cachedVisibleSize
        } : t.minimumSize;
        const o = this.orientation === se.Vertical ? new pe(e, t, s) : new de(e, t, s);
        if (this.viewItems.splice(n, 0, o), this.viewItems.length > 1) {
            const _e4 = this.orientation === se.Vertical ? new ce(this.sashContainer, {
                getHorizontalSashTop: (e)=>this.getSashPosition(e),
                getHorizontalSashWidth: this.getSashOrthogonalSize
            }, {
                orientation: se.Horizontal
            }) : new ce(this.sashContainer, {
                getVerticalSashLeft: (e)=>this.getSashPosition(e),
                getVerticalSashHeight: this.getSashOrthogonalSize
            }, {
                orientation: se.Vertical
            }), _t2 = this.orientation === se.Vertical ? (t)=>({
                    sash: _e4,
                    start: t.startY,
                    current: t.currentY
                }) : (t)=>({
                    sash: _e4,
                    start: t.startX,
                    current: t.currentX
                });
            _e4.on("start", (e)=>{
                var _this$onDidDragStart;
                this.emit("sashDragStart"), this.onSashStart(_t2(e));
                const i = this.viewItems.map((e)=>e.size);
                (_this$onDidDragStart = this.onDidDragStart) == null || _this$onDidDragStart.call(this, i);
            }), _e4.on("change", (e)=>this.onSashChange(_t2(e))), _e4.on("end", ()=>{
                var _this$onDidDragEnd;
                this.emit("sashDragEnd"), this.onSashEnd(this.sashItems.findIndex((t)=>t.sash === _e4));
                const t = this.viewItems.map((e)=>e.size);
                (_this$onDidDragEnd = this.onDidDragEnd) == null || _this$onDidDragEnd.call(this, t);
            }), _e4.on("reset", ()=>{
                const t = this.sashItems.findIndex((t)=>t.sash === _e4), i = X(t, -1, -1), n = X(t + 1, this.viewItems.length), r = this.findFirstSnapIndex(i), s = this.findFirstSnapIndex(n);
                ("number" != typeof r || this.viewItems[r].visible) && ("number" != typeof s || this.viewItems[s].visible) && this.emit("sashreset", t);
            });
            const _i2 = {
                sash: _e4
            };
            this.sashItems.splice(n - 1, 0, _i2);
        }
        r || this.relayout(), r || "number" == typeof i || "distribute" !== i.type || this.distributeViewSizes();
    }
    removeView(e, t) {
        if (e < 0 || e >= this.viewItems.length) throw new Error("Index out of bounds");
        const i = this.viewItems.splice(e, 1)[0].view;
        if (this.viewItems.length >= 1) {
            const _t3 = Math.max(e - 1, 0);
            this.sashItems.splice(_t3, 1)[0].sash.dispose();
        }
        return this.relayout(), t && "distribute" === t.type && this.distributeViewSizes(), i;
    }
    moveView(e, t, i) {
        const n = this.getViewCachedVisibleSize(t), r = void 0 === n ? this.getViewSize(t) : he.Invisible(n), s = this.removeView(t);
        this.addView(e, s, r, i);
    }
    getViewCachedVisibleSize(e) {
        if (e < 0 || e >= this.viewItems.length) throw new Error("Index out of bounds");
        return this.viewItems[e].cachedVisibleSize;
    }
    layout(e = this.size) {
        const t = Math.max(this.size, this.contentSize);
        if (this.size = e, this.proportions) for(let _t4 = 0; _t4 < this.viewItems.length; _t4++){
            const i = this.viewItems[_t4];
            i.size = g(Math.round(this.proportions[_t4] * e), i.minimumSize, i.maximumSize);
        }
        else {
            const i = X(0, this.viewItems.length), n = i.filter((e)=>this.viewItems[e].priority === fe.Low), r = i.filter((e)=>this.viewItems[e].priority === fe.High);
            this.resize(this.viewItems.length - 1, e - t, void 0, n, r);
        }
        this.distributeEmptySpace(), this.layoutViews();
    }
    resizeView(e, t) {
        if (e < 0 || e >= this.viewItems.length) return;
        const i = X(0, this.viewItems.length).filter((t)=>t !== e), n = [
            ...i.filter((e)=>this.viewItems[e].priority === fe.Low),
            e
        ], r = i.filter((e)=>this.viewItems[e].priority === fe.High), s = this.viewItems[e];
        t = Math.round(t), t = g(t, s.minimumSize, Math.min(s.maximumSize, this.size)), s.size = t, this.relayout(n, r);
    }
    resizeViews(e) {
        for(let t = 0; t < e.length; t++){
            const i = this.viewItems[t];
            let n = e[t];
            n = Math.round(n), n = g(n, i.minimumSize, Math.min(i.maximumSize, this.size)), i.size = n;
        }
        this.contentSize = this.viewItems.reduce((e, t)=>e + t.size, 0), this.saveProportions(), this.layout(this.size);
    }
    getViewSize(e) {
        return e < 0 || e >= this.viewItems.length ? -1 : this.viewItems[e].size;
    }
    isViewVisible(e) {
        if (e < 0 || e >= this.viewItems.length) throw new Error("Index out of bounds");
        return this.viewItems[e].visible;
    }
    setViewVisible(e, t) {
        if (e < 0 || e >= this.viewItems.length) throw new Error("Index out of bounds");
        this.viewItems[e].setVisible(t), this.distributeEmptySpace(e), this.layoutViews(), this.saveProportions();
    }
    distributeViewSizes() {
        const e = [];
        let t = 0;
        for (const _i3 of this.viewItems)_i3.maximumSize - _i3.minimumSize > 0 && (e.push(_i3), t += _i3.size);
        const i = Math.floor(t / e.length);
        for (const _t5 of e)_t5.size = g(i, _t5.minimumSize, _t5.maximumSize);
        const n = X(0, this.viewItems.length), r = n.filter((e)=>this.viewItems[e].priority === fe.Low), s = n.filter((e)=>this.viewItems[e].priority === fe.High);
        this.relayout(r, s);
    }
    dispose() {
        this.sashItems.forEach((e)=>e.sash.dispose()), this.sashItems = [], this.sashContainer.remove();
    }
    relayout(e, t) {
        const i = this.viewItems.reduce((e, t)=>e + t.size, 0);
        this.resize(this.viewItems.length - 1, this.size - i, void 0, e, t), this.distributeEmptySpace(), this.layoutViews(), this.saveProportions();
    }
    onSashStart({ sash: e, start: t }) {
        const i = this.sashItems.findIndex((t)=>t.sash === e);
        ((e)=>{
            const t = this.viewItems.map((e)=>e.size);
            let n, r, s = Number.NEGATIVE_INFINITY, o = Number.POSITIVE_INFINITY;
            const a = X(i, -1, -1), u = X(i + 1, this.viewItems.length), c = a.reduce((e, i)=>e + (this.viewItems[i].minimumSize - t[i]), 0), h = a.reduce((e, i)=>e + (this.viewItems[i].viewMaximumSize - t[i]), 0), l = 0 === u.length ? Number.POSITIVE_INFINITY : u.reduce((e, i)=>e + (t[i] - this.viewItems[i].minimumSize), 0), f = 0 === u.length ? Number.NEGATIVE_INFINITY : u.reduce((e, i)=>e + (t[i] - this.viewItems[i].viewMaximumSize), 0);
            s = Math.max(c, f), o = Math.min(l, h);
            const m = this.findFirstSnapIndex(a), d = this.findFirstSnapIndex(u);
            if ("number" == typeof m) {
                const _e5 = this.viewItems[m], _t6 = Math.floor(_e5.viewMinimumSize / 2);
                n = {
                    index: m,
                    limitDelta: _e5.visible ? s - _t6 : s + _t6,
                    size: _e5.size
                };
            }
            if ("number" == typeof d) {
                const _e6 = this.viewItems[d], _t7 = Math.floor(_e6.viewMinimumSize / 2);
                r = {
                    index: d,
                    limitDelta: _e6.visible ? o + _t7 : o - _t7,
                    size: _e6.size
                };
            }
            this.sashDragState = {
                start: e,
                current: e,
                index: i,
                sizes: t,
                minDelta: s,
                maxDelta: o,
                snapBefore: n,
                snapAfter: r
            };
        })(t);
    }
    onSashChange({ current: e }) {
        const { index: t, start: i, sizes: n, minDelta: r, maxDelta: s, snapBefore: o, snapAfter: a } = this.sashDragState;
        this.sashDragState.current = e;
        const u = e - i;
        this.resize(t, u, n, void 0, void 0, r, s, o, a), this.distributeEmptySpace(), this.layoutViews();
    }
    getSashPosition(e) {
        let t = 0;
        for(let i = 0; i < this.sashItems.length; i++)if (t += this.viewItems[i].size, this.sashItems[i].sash === e) return t;
        return 0;
    }
    resize(e, t, i = this.viewItems.map((e)=>e.size), n, r, s = Number.NEGATIVE_INFINITY, o = Number.POSITIVE_INFINITY, a, u) {
        if (e < 0 || e >= this.viewItems.length) return 0;
        const c = X(e, -1, -1), h = X(e + 1, this.viewItems.length);
        if (r) for (const _e7 of r)G(c, _e7), G(h, _e7);
        if (n) for (const _e8 of n)U(c, _e8), U(h, _e8);
        const l = c.map((e)=>this.viewItems[e]), f = c.map((e)=>i[e]), m = h.map((e)=>this.viewItems[e]), d = h.map((e)=>i[e]), p = c.reduce((e, t)=>e + (this.viewItems[t].minimumSize - i[t]), 0), v = c.reduce((e, t)=>e + (this.viewItems[t].maximumSize - i[t]), 0), y = 0 === h.length ? Number.POSITIVE_INFINITY : h.reduce((e, t)=>e + (i[t] - this.viewItems[t].minimumSize), 0), b = 0 === h.length ? Number.NEGATIVE_INFINITY : h.reduce((e, t)=>e + (i[t] - this.viewItems[t].maximumSize), 0), z = Math.max(p, b, s), w = Math.min(y, v, o);
        let S = !1;
        if (a) {
            const _e9 = this.viewItems[a.index], _i4 = t >= a.limitDelta;
            S = _i4 !== _e9.visible, _e9.setVisible(_i4, a.size);
        }
        if (!S && u) {
            const _e10 = this.viewItems[u.index], _i5 = t < u.limitDelta;
            S = _i5 !== _e10.visible, _e10.setVisible(_i5, u.size);
        }
        if (S) return this.resize(e, t, i, n, r, s, o);
        for(let _e11 = 0, _i6 = t = g(t, z, w); _e11 < l.length; _e11++){
            const _t8 = l[_e11], _n2 = g(f[_e11] + _i6, _t8.minimumSize, _t8.maximumSize);
            _i6 -= _n2 - f[_e11], _t8.size = _n2;
        }
        for(let _e12 = 0, _i7 = t; _e12 < m.length; _e12++){
            const _t9 = m[_e12], _n3 = g(d[_e12] - _i7, _t9.minimumSize, _t9.maximumSize);
            _i7 += _n3 - d[_e12], _t9.size = _n3;
        }
        return t;
    }
    distributeEmptySpace(e) {
        const t = this.viewItems.reduce((e, t)=>e + t.size, 0);
        let i = this.size - t;
        const n = X(this.viewItems.length - 1, -1, -1);
        "number" == typeof e && U(n, e);
        for(let _e13 = 0; 0 !== i && _e13 < n.length; _e13++){
            const _t10 = this.viewItems[n[_e13]], r = g(_t10.size + i, _t10.minimumSize, _t10.maximumSize);
            i -= r - _t10.size, _t10.size = r;
        }
    }
    layoutViews() {
        var _this$onDidChange;
        this.contentSize = this.viewItems.reduce((e, t)=>e + t.size, 0);
        let e = 0;
        for (const t of this.viewItems)t.layout(e), e += t.size;
        (_this$onDidChange = this.onDidChange) != null && _this$onDidChange.call(this, this.viewItems.map((e)=>e.size)), this.sashItems.forEach((e)=>e.sash.layout()), this.updateSashEnablement();
    }
    saveProportions() {
        this.proportionalLayout && this.contentSize > 0 && (this.proportions = this.viewItems.map((e)=>e.size / this.contentSize));
    }
    updateSashEnablement() {
        let e = !1;
        const t = this.viewItems.map((t)=>e = t.size - t.minimumSize > 0 || e);
        e = !1;
        const i = this.viewItems.map((t)=>e = t.maximumSize - t.size > 0 || e), n = [
            ...this.viewItems
        ].reverse();
        e = !1;
        const r = n.map((t)=>e = t.size - t.minimumSize > 0 || e).reverse();
        e = !1;
        const s = n.map((t)=>e = t.maximumSize - t.size > 0 || e).reverse();
        let o = 0;
        for(let _e14 = 0; _e14 < this.sashItems.length; _e14++){
            const { sash: _n4 } = this.sashItems[_e14];
            o += this.viewItems[_e14].size;
            const a = !(t[_e14] && s[_e14 + 1]), u = !(i[_e14] && r[_e14 + 1]);
            if (a && u) {
                const _i8 = X(_e14, -1, -1), _s = X(_e14 + 1, this.viewItems.length), _a = this.findFirstSnapIndex(_i8), _u = this.findFirstSnapIndex(_s), c = "number" == typeof _a && !this.viewItems[_a].visible, h = "number" == typeof _u && !this.viewItems[_u].visible;
                c && r[_e14] && (o > 0 || this.startSnappingEnabled) ? _n4.state = oe.Minimum : h && t[_e14] && (o < this.contentSize || this.endSnappingEnabled) ? _n4.state = oe.Maximum : _n4.state = oe.Disabled;
            } else _n4.state = a && !u ? oe.Minimum : !a && u ? oe.Maximum : oe.Enabled;
        }
    }
    findFirstSnapIndex(e) {
        for (const t of e){
            const _e15 = this.viewItems[t];
            if (_e15.visible && _e15.snap) return t;
        }
        for (const t of e){
            const _e16 = this.viewItems[t];
            if (_e16.visible && _e16.maximumSize - _e16.minimumSize > 0) return;
            if (!_e16.visible && _e16.snap) return t;
        }
    }
}
class ye {
    constructor(e){
        this.size = void 0, this.size = e;
    }
    getPreferredSize() {
        return this.size;
    }
}
class be {
    constructor(e, t){
        this.proportion = void 0, this.layoutService = void 0, this.proportion = e, this.layoutService = t;
    }
    getPreferredSize() {
        return this.proportion * this.layoutService.getSize();
    }
}
class ge {
    getPreferredSize() {}
}
class ze {
    get preferredSize() {
        return this.layoutStrategy.getPreferredSize();
    }
    set preferredSize(e) {
        if ("number" == typeof e) this.layoutStrategy = new ye(e);
        else if ("string" == typeof e) {
            const t = e.trim();
            if (k(t, "%")) {
                const _e17 = Number(t.slice(0, -1)) / 100;
                this.layoutStrategy = new be(_e17, this.layoutService);
            } else if (k(t, "px")) {
                const _e18 = Number(t.slice(0, -2)) / 100;
                this.layoutStrategy = new ye(_e18);
            } else if ("number" == typeof Number.parseFloat(t)) {
                const _e19 = Number.parseFloat(t);
                this.layoutStrategy = new ye(_e19);
            } else this.layoutStrategy = new ge();
        } else this.layoutStrategy = new ge();
    }
    constructor(e, t){
        var _t$priority;
        if (this.minimumSize = 0, this.maximumSize = Number.POSITIVE_INFINITY, this.element = void 0, this.priority = void 0, this.snap = void 0, this.layoutService = void 0, this.layoutStrategy = void 0, this.layoutService = e, this.element = t.element, this.minimumSize = "number" == typeof t.minimumSize ? t.minimumSize : 30, this.maximumSize = "number" == typeof t.maximumSize ? t.maximumSize : Number.POSITIVE_INFINITY, "number" == typeof t.preferredSize) this.layoutStrategy = new ye(t.preferredSize);
        else if ("string" == typeof t.preferredSize) {
            const _e20 = t.preferredSize.trim();
            if (k(_e20, "%")) {
                const _t11 = Number(_e20.slice(0, -1)) / 100;
                this.layoutStrategy = new be(_t11, this.layoutService);
            } else if (k(_e20, "px")) {
                const _t12 = Number(_e20.slice(0, -2));
                this.layoutStrategy = new ye(_t12);
            } else if ("number" == typeof Number.parseFloat(_e20)) {
                const _t13 = Number.parseFloat(_e20);
                this.layoutStrategy = new ye(_t13);
            } else this.layoutStrategy = new ge();
        } else this.layoutStrategy = new ge();
        this.priority = (_t$priority = t.priority) != null ? _t$priority : fe.Normal, this.snap = "boolean" == typeof t.snap && t.snap;
    }
    layout(e) {}
}
function we(e) {
    return void 0 !== e.minSize || void 0 !== e.maxSize || void 0 !== e.preferredSize || void 0 !== e.priority || void 0 !== e.visible;
}
const Se = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ className: t, children: i }, n)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: n,
        className: y("split-view-view", j, t)
    }, i));
Se.displayName = "Allotment.Pane";
const _e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ children: o, className: a, id: c, maxSize: h = 1 / 0, minSize: l = 30, proportionalLayout: f = !0, separator: m = !0, sizes: d, defaultSizes: p = d, snap: v = !1, vertical: b = !1, onChange: g, onReset: z, onVisibleChange: w, onDragStart: _, onDragEnd: E }, j)=>{
    const N = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), M = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]), P = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Map()), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), k = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Map()), $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new F()), H = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]), [Y, B] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1);
    "production" !== ("TURBOPACK compile-time value", "development") && d && console.warn("Prop sizes is deprecated. Please use defaultSizes instead.");
    const R = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Children.toArray(o).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isValidElement), [
        o
    ]), W = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        var _H$current, _T$current;
        const t = (_H$current = H.current) == null ? void 0 : _H$current[e];
        return "number" == typeof (t == null ? void 0 : t.preferredSize) && ((_T$current = T.current) != null && _T$current.resizeView(e, Math.round(t.preferredSize)), !0);
    }, []);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(j, ()=>({
            reset: ()=>{
                if (z) z();
                else {
                    var _T$current2;
                    (_T$current2 = T.current) == null || _T$current2.distributeViewSizes();
                    for(let e = 0; e < H.current.length; e++)W(e);
                }
            },
            resize: (e)=>{
                var _T$current3;
                (_T$current3 = T.current) == null || _T$current3.resizeViews(e);
            }
        })), C(()=>{
        let e = !0;
        p && k.current.size !== p.length && (e = !1, console.warn(`Expected ${p.length} children based on defaultSizes but found ${k.current.size}`)), e && p && (M.current = R.map((e)=>e.key));
        const t = _extends({
            orientation: b ? se.Vertical : se.Horizontal,
            proportionalLayout: f
        }, e && p && {
            descriptor: {
                size: p.reduce((e, t)=>e + t, 0),
                views: p.map((e, t)=>{
                    var _i$minSize, _i$maxSize, _i$priority, _i$snap;
                    const i = P.current.get(M.current[t]), n = new ze($.current, _extends({
                        element: document.createElement("div"),
                        minimumSize: (_i$minSize = i == null ? void 0 : i.minSize) != null ? _i$minSize : l,
                        maximumSize: (_i$maxSize = i == null ? void 0 : i.maxSize) != null ? _i$maxSize : h,
                        priority: (_i$priority = i == null ? void 0 : i.priority) != null ? _i$priority : fe.Normal
                    }, (i == null ? void 0 : i.preferredSize) && {
                        preferredSize: i == null ? void 0 : i.preferredSize
                    }, {
                        snap: (_i$snap = i == null ? void 0 : i.snap) != null ? _i$snap : v
                    }));
                    return H.current.push(n), {
                        container: [
                            ...k.current.values()
                        ][t],
                        size: e,
                        view: n
                    };
                })
            }
        });
        T.current = new ve(N.current, t, g, _, E), T.current.on("sashDragStart", ()=>{
            var _N$current;
            (_N$current = N.current) == null || _N$current.classList.add("split-view-sash-dragging");
        }), T.current.on("sashDragEnd", ()=>{
            var _N$current2;
            (_N$current2 = N.current) == null || _N$current2.classList.remove("split-view-sash-dragging");
        }), T.current.on("sashchange", (e)=>{
            if (w && T.current) {
                const _e21 = R.map((e)=>e.key);
                for(let t = 0; t < _e21.length; t++){
                    const i = P.current.get(_e21[t]);
                    void 0 !== (i == null ? void 0 : i.visible) && i.visible !== T.current.isViewVisible(t) && w(t, T.current.isViewVisible(t));
                }
            }
        }), T.current.on("sashreset", (e)=>{
            if (z) z();
            else {
                var _T$current4;
                if (W(e)) return;
                if (W(e + 1)) return;
                (_T$current4 = T.current) == null || _T$current4.distributeViewSizes();
            }
        });
        const i = T.current;
        return ()=>{
            i.dispose();
        };
    }, []), C(()=>{
        if (Y) {
            const e = R.map((e)=>e.key), t = [
                ...M.current
            ], i = e.filter((e)=>!M.current.includes(e)), n = e.filter((e)=>M.current.includes(e)), r = M.current.map((t)=>!e.includes(t));
            for(let _e22 = r.length - 1; _e22 >= 0; _e22--){
                var _T$current5;
                r[_e22] && ((_T$current5 = T.current) != null && _T$current5.removeView(_e22), t.splice(_e22, 1), H.current.splice(_e22, 1));
            }
            for (const _n5 of i){
                var _i9$minSize, _i9$maxSize, _i9$priority, _i9$snap, _T$current6;
                const _i9 = P.current.get(_n5), _r2 = new ze($.current, _extends({
                    element: document.createElement("div"),
                    minimumSize: (_i9$minSize = _i9 == null ? void 0 : _i9.minSize) != null ? _i9$minSize : l,
                    maximumSize: (_i9$maxSize = _i9 == null ? void 0 : _i9.maxSize) != null ? _i9$maxSize : h,
                    priority: (_i9$priority = _i9 == null ? void 0 : _i9.priority) != null ? _i9$priority : fe.Normal
                }, (_i9 == null ? void 0 : _i9.preferredSize) && {
                    preferredSize: _i9 == null ? void 0 : _i9.preferredSize
                }, {
                    snap: (_i9$snap = _i9 == null ? void 0 : _i9.snap) != null ? _i9$snap : v
                }));
                (_T$current6 = T.current) != null && _T$current6.addView(k.current.get(_n5), _r2, he.Distribute, e.findIndex((e)=>e === _n5)), t.splice(e.findIndex((e)=>e === _n5), 0, _n5), H.current.splice(e.findIndex((e)=>e === _n5), 0, _r2);
            }
            for(; !S(e, t);)for (const [_i10, _n6] of e.entries()){
                const _e23 = t.findIndex((e)=>e === _n6);
                if (_e23 !== _i10) {
                    var _T$current7;
                    (_T$current7 = T.current) == null || _T$current7.moveView(k.current.get(_n6), _e23, _i10);
                    const _r3 = t[_e23];
                    t.splice(_e23, 1), t.splice(_i10, 0, _r3);
                    break;
                }
            }
            for (const _t14 of i){
                var _T$current8;
                const _i11 = e.findIndex((e)=>e === _t14), _n7 = H.current[_i11].preferredSize;
                void 0 !== _n7 && ((_T$current8 = T.current) == null ? void 0 : _T$current8.resizeView(_i11, _n7));
            }
            for (const _t15 of [
                ...i,
                ...n
            ]){
                var _T$current9, _T$current10;
                const _i12 = P.current.get(_t15), _n8 = e.findIndex((e)=>e === _t15);
                _i12 && we(_i12) && void 0 !== _i12.visible && ((_T$current9 = T.current) == null ? void 0 : _T$current9.isViewVisible(_n8)) !== _i12.visible && ((_T$current10 = T.current) == null ? void 0 : _T$current10.setViewVisible(_n8, _i12.visible));
            }
            for (const _t16 of n){
                const _i13 = P.current.get(_t16), _n9 = e.findIndex((e)=>e === _t16);
                if (_i13 && we(_i13)) {
                    var _T$current11;
                    void 0 !== _i13.preferredSize && H.current[_n9].preferredSize !== _i13.preferredSize && (H.current[_n9].preferredSize = _i13.preferredSize);
                    let _e24 = !1;
                    void 0 !== _i13.minSize && H.current[_n9].minimumSize !== _i13.minSize && (H.current[_n9].minimumSize = _i13.minSize, _e24 = !0), void 0 !== _i13.maxSize && H.current[_n9].maximumSize !== _i13.maxSize && (H.current[_n9].maximumSize = _i13.maxSize, _e24 = !0), _e24 && ((_T$current11 = T.current) == null ? void 0 : _T$current11.layout());
                }
            }
            (i.length > 0 || r.length > 0) && (M.current = e);
        }
    }, [
        R,
        Y,
        h,
        l,
        v
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        T.current && (T.current.onDidChange = g);
    }, [
        g
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        T.current && (T.current.onDidDragStart = _);
    }, [
        _
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        T.current && (T.current.onDidDragEnd = E);
    }, [
        E
    ]), I({
        ref: N,
        onResize: ({ width: e, height: t })=>{
            var _T$current12;
            e && t && ((_T$current12 = T.current) != null && _T$current12.layout(b ? t : e), $.current.setSize(b ? t : e), B(!0));
        }
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        A && Ie(20);
    }, []), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: N,
        className: y("split-view", b ? "split-view-vertical" : "split-view-horizontal", {
            "split-view-separator-border": m
        }, x, b ? L : O, {
            [D]: m
        }, a),
        id: c
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: y("split-view-container", V)
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Children.toArray(o).map((t)=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isValidElement(t)) return null;
        const i = t.key;
        return "Allotment.Pane" === t.type.displayName ? (P.current.set(i, t.props), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cloneElement(t, {
            key: i,
            ref: (e)=>{
                const n = t.ref;
                n && (n.current = e), e ? k.current.set(i, e) : k.current.delete(i);
            }
        })) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(Se, {
            key: i,
            ref: (e)=>{
                e ? k.current.set(i, e) : k.current.delete(i);
            }
        }, t);
    })));
});
function Ie(e) {
    const t = g(e, 4, 20), i = g(e, 1, 8);
    document.documentElement.style.setProperty("--sash-size", t + "px"), document.documentElement.style.setProperty("--sash-hover-size", i + "px"), function(e) {
        ae = e, ue.emit("onDidChangeGlobalSize", e);
    }(t);
}
_e.displayName = "Allotment";
var xe = Object.assign(_e, {
    Pane: Se
});
;
}}),
}]);

//# sourceMappingURL=node_modules_273dd0a6._.js.map