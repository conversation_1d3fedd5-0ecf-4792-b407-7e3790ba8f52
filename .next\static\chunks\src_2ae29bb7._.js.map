{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/utils/export.ts"], "sourcesContent": ["/**\n * Export utilities for different file formats\n */\n\nimport { ExportOptions, ExportError } from '@/types';\nimport { markdownToHtml } from './markdown';\nimport { downloadFile } from './file';\n\n/**\n * Export markdown as plain text\n */\nexport async function exportAsText(\n  markdown: string,\n  filename: string\n): Promise<void> {\n  try {\n    const textFilename = filename.replace(/\\.[^/.]+$/, '.txt');\n    downloadFile(markdown, textFilename, 'text/plain');\n  } catch (error) {\n    throw new ExportError('Failed to export as text', 'txt', error);\n  }\n}\n\n/**\n * Export markdown as HTML\n */\nexport async function exportAsHtml(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    const htmlContent = markdownToHtml(markdown);\n    \n    const fullHtml = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${filename}</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            line-height: 1.6;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 2rem;\n            color: #333;\n        }\n        \n        h1, h2, h3, h4, h5, h6 {\n            margin-top: 2rem;\n            margin-bottom: 1rem;\n            font-weight: 600;\n        }\n        \n        h1 { font-size: 2.5rem; }\n        h2 { font-size: 2rem; }\n        h3 { font-size: 1.5rem; }\n        h4 { font-size: 1.25rem; }\n        h5 { font-size: 1.125rem; }\n        h6 { font-size: 1rem; }\n        \n        p {\n            margin-bottom: 1rem;\n        }\n        \n        code {\n            background-color: #f5f5f5;\n            padding: 0.2rem 0.4rem;\n            border-radius: 3px;\n            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n        }\n        \n        pre {\n            background-color: #f5f5f5;\n            padding: 1rem;\n            border-radius: 5px;\n            overflow-x: auto;\n        }\n        \n        pre code {\n            background: none;\n            padding: 0;\n        }\n        \n        blockquote {\n            border-left: 4px solid #ddd;\n            margin: 1rem 0;\n            padding-left: 1rem;\n            color: #666;\n        }\n        \n        table {\n            border-collapse: collapse;\n            width: 100%;\n            margin: 1rem 0;\n        }\n        \n        th, td {\n            border: 1px solid #ddd;\n            padding: 0.5rem;\n            text-align: left;\n        }\n        \n        th {\n            background-color: #f5f5f5;\n            font-weight: 600;\n        }\n        \n        img {\n            max-width: 100%;\n            height: auto;\n        }\n        \n        a {\n            color: #0066cc;\n            text-decoration: none;\n        }\n        \n        a:hover {\n            text-decoration: underline;\n        }\n        \n        ul, ol {\n            margin: 1rem 0;\n            padding-left: 2rem;\n        }\n        \n        li {\n            margin-bottom: 0.5rem;\n        }\n        \n        hr {\n            border: none;\n            border-top: 1px solid #ddd;\n            margin: 2rem 0;\n        }\n        \n        .anchor-link {\n            opacity: 0;\n            margin-left: 0.5rem;\n            text-decoration: none;\n        }\n        \n        h1:hover .anchor-link,\n        h2:hover .anchor-link,\n        h3:hover .anchor-link,\n        h4:hover .anchor-link,\n        h5:hover .anchor-link,\n        h6:hover .anchor-link {\n            opacity: 1;\n        }\n        \n        @media print {\n            body {\n                margin: 0;\n                padding: 1rem;\n            }\n            \n            .anchor-link {\n                display: none;\n            }\n        }\n    </style>\n</head>\n<body>\n    ${htmlContent}\n</body>\n</html>`;\n    \n    const htmlFilename = filename.replace(/\\.[^/.]+$/, '.html');\n    downloadFile(fullHtml, htmlFilename, 'text/html');\n  } catch (error) {\n    throw new ExportError('Failed to export as HTML', 'html', error);\n  }\n}\n\n/**\n * Export markdown as PDF (lazy loaded)\n */\nexport async function exportAsPdf(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    // Lazy load jsPDF\n    const { jsPDF } = await import('jspdf');\n    \n    const htmlContent = markdownToHtml(markdown);\n    \n    // Create a temporary div to render HTML\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = htmlContent;\n    tempDiv.style.position = 'absolute';\n    tempDiv.style.left = '-9999px';\n    tempDiv.style.width = '800px';\n    tempDiv.style.fontFamily = 'Arial, sans-serif';\n    tempDiv.style.fontSize = '12px';\n    tempDiv.style.lineHeight = '1.6';\n    \n    document.body.appendChild(tempDiv);\n    \n    try {\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: options.pageSize || 'a4'\n      });\n      \n      // Set margins\n      const margins = options.margins || { top: 20, right: 20, bottom: 20, left: 20 };\n      \n      // Add title if available\n      const title = filename.replace(/\\.[^/.]+$/, '');\n      pdf.setFontSize(16);\n      pdf.text(title, margins.left, margins.top);\n      \n      // Convert HTML to text and add to PDF\n      const textContent = tempDiv.textContent || tempDiv.innerText || '';\n      const lines = pdf.splitTextToSize(textContent, 210 - margins.left - margins.right);\n      \n      pdf.setFontSize(12);\n      let yPosition = margins.top + 15;\n      \n      lines.forEach((line: string) => {\n        if (yPosition > 297 - margins.bottom) {\n          pdf.addPage();\n          yPosition = margins.top;\n        }\n        pdf.text(line, margins.left, yPosition);\n        yPosition += 6;\n      });\n      \n      // Add metadata if requested\n      if (options.includeMetadata) {\n        pdf.setProperties({\n          title: title,\n          creator: 'Markdown Editor',\n          creationDate: new Date()\n        });\n      }\n      \n      const pdfFilename = filename.replace(/\\.[^/.]+$/, '.pdf');\n      pdf.save(pdfFilename);\n    } finally {\n      document.body.removeChild(tempDiv);\n    }\n  } catch (error) {\n    throw new ExportError('Failed to export as PDF', 'pdf', error);\n  }\n}\n\n/**\n * Export markdown as DOCX (lazy loaded)\n */\nexport async function exportAsDocx(\n  markdown: string,\n  filename: string,\n  options: Partial<ExportOptions> = {}\n): Promise<void> {\n  try {\n    // Lazy load docx\n    const { Document, Packer, Paragraph, TextRun, HeadingLevel } = await import('docx');\n    \n    // Parse markdown and convert to DOCX elements\n    const lines = markdown.split('\\n');\n    const paragraphs: any[] = [];\n    \n    for (const line of lines) {\n      if (line.trim() === '') {\n        paragraphs.push(new Paragraph({ text: '' }));\n        continue;\n      }\n      \n      // Handle headings\n      const headingMatch = line.match(/^(#{1,6})\\s+(.+)$/);\n      if (headingMatch) {\n        const level = headingMatch[1].length;\n        const text = headingMatch[2];\n        \n        const headingLevels = [\n          HeadingLevel.HEADING_1,\n          HeadingLevel.HEADING_2,\n          HeadingLevel.HEADING_3,\n          HeadingLevel.HEADING_4,\n          HeadingLevel.HEADING_5,\n          HeadingLevel.HEADING_6\n        ];\n        \n        paragraphs.push(new Paragraph({\n          text,\n          heading: headingLevels[level - 1] || HeadingLevel.HEADING_6\n        }));\n        continue;\n      }\n      \n      // Handle bold and italic text\n      const textRuns: any[] = [];\n      let currentText = line;\n      \n      // Simple bold/italic parsing (basic implementation)\n      const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n      const italicRegex = /\\*(.*?)\\*/g;\n      \n      // For now, just add as plain text\n      // TODO: Implement proper markdown parsing for rich text\n      textRuns.push(new TextRun({ text: currentText }));\n      \n      paragraphs.push(new Paragraph({ children: textRuns }));\n    }\n    \n    const doc = new Document({\n      sections: [{\n        properties: {},\n        children: paragraphs\n      }]\n    });\n    \n    // Generate and download the document\n    const buffer = await Packer.toBuffer(doc);\n    const blob = new Blob([buffer], { \n      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n    });\n    \n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename.replace(/\\.[^/.]+$/, '.docx');\n    link.style.display = 'none';\n    \n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    setTimeout(() => URL.revokeObjectURL(url), 100);\n  } catch (error) {\n    throw new ExportError('Failed to export as DOCX', 'docx', error);\n  }\n}\n\n/**\n * Main export function that delegates to specific format handlers\n */\nexport async function exportMarkdown(\n  markdown: string,\n  filename: string,\n  options: ExportOptions\n): Promise<void> {\n  switch (options.format) {\n    case 'txt':\n      return exportAsText(markdown, filename);\n    case 'html':\n      return exportAsHtml(markdown, filename, options);\n    case 'pdf':\n      return exportAsPdf(markdown, filename, options);\n    case 'docx':\n      return exportAsDocx(markdown, filename, options);\n    default:\n      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);\n  }\n}\n\n/**\n * Get available export formats\n */\nexport function getAvailableFormats(): Array<{\n  value: string;\n  label: string;\n  description: string;\n}> {\n  return [\n    {\n      value: 'txt',\n      label: 'Plain Text',\n      description: 'Export as plain text file (.txt)'\n    },\n    {\n      value: 'html',\n      label: 'HTML',\n      description: 'Export as HTML file (.html)'\n    },\n    {\n      value: 'pdf',\n      label: 'PDF',\n      description: 'Export as PDF document (.pdf)'\n    },\n    {\n      value: 'docx',\n      label: 'Word Document',\n      description: 'Export as Microsoft Word document (.docx)'\n    }\n  ];\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAED;AACA;AACA;;;;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB;IAEhB,IAAI;QACF,MAAM,eAAe,SAAS,OAAO,CAAC,aAAa;QACnD,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,cAAc;IACvC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,wHAAA,CAAA,cAAW,CAAC,4BAA4B,OAAO;IAC3D;AACF;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;QAEnC,MAAM,WAAW,CAAC;;;;;WAKX,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgIlB,EAAE,YAAY;;OAEX,CAAC;QAEJ,MAAM,eAAe,SAAS,OAAO,CAAC,aAAa;QACnD,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,cAAc;IACvC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,wHAAA,CAAA,cAAW,CAAC,4BAA4B,QAAQ;IAC5D;AACF;AAKO,eAAe,YACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,kBAAkB;QAClB,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,MAAM,cAAc,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE;QAEnC,wCAAwC;QACxC,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QACpB,QAAQ,KAAK,CAAC,QAAQ,GAAG;QACzB,QAAQ,KAAK,CAAC,IAAI,GAAG;QACrB,QAAQ,KAAK,CAAC,KAAK,GAAG;QACtB,QAAQ,KAAK,CAAC,UAAU,GAAG;QAC3B,QAAQ,KAAK,CAAC,QAAQ,GAAG;QACzB,QAAQ,KAAK,CAAC,UAAU,GAAG;QAE3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,IAAI,MAAM;gBACpB,aAAa;gBACb,MAAM;gBACN,QAAQ,QAAQ,QAAQ,IAAI;YAC9B;YAEA,cAAc;YACd,MAAM,UAAU,QAAQ,OAAO,IAAI;gBAAE,KAAK;gBAAI,OAAO;gBAAI,QAAQ;gBAAI,MAAM;YAAG;YAE9E,yBAAyB;YACzB,MAAM,QAAQ,SAAS,OAAO,CAAC,aAAa;YAC5C,IAAI,WAAW,CAAC;YAChB,IAAI,IAAI,CAAC,OAAO,QAAQ,IAAI,EAAE,QAAQ,GAAG;YAEzC,sCAAsC;YACtC,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;YAChE,MAAM,QAAQ,IAAI,eAAe,CAAC,aAAa,MAAM,QAAQ,IAAI,GAAG,QAAQ,KAAK;YAEjF,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,QAAQ,GAAG,GAAG;YAE9B,MAAM,OAAO,CAAC,CAAC;gBACb,IAAI,YAAY,MAAM,QAAQ,MAAM,EAAE;oBACpC,IAAI,OAAO;oBACX,YAAY,QAAQ,GAAG;gBACzB;gBACA,IAAI,IAAI,CAAC,MAAM,QAAQ,IAAI,EAAE;gBAC7B,aAAa;YACf;YAEA,4BAA4B;YAC5B,IAAI,QAAQ,eAAe,EAAE;gBAC3B,IAAI,aAAa,CAAC;oBAChB,OAAO;oBACP,SAAS;oBACT,cAAc,IAAI;gBACpB;YACF;YAEA,MAAM,cAAc,SAAS,OAAO,CAAC,aAAa;YAClD,IAAI,IAAI,CAAC;QACX,SAAU;YACR,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,wHAAA,CAAA,cAAW,CAAC,2BAA2B,OAAO;IAC1D;AACF;AAKO,eAAe,aACpB,QAAgB,EAChB,QAAgB,EAChB,UAAkC,CAAC,CAAC;IAEpC,IAAI;QACF,iBAAiB;QACjB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG;QAE/D,8CAA8C;QAC9C,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,MAAM,aAAoB,EAAE;QAE5B,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,OAAO,IAAI;gBACtB,WAAW,IAAI,CAAC,IAAI,UAAU;oBAAE,MAAM;gBAAG;gBACzC;YACF;YAEA,kBAAkB;YAClB,MAAM,eAAe,KAAK,KAAK,CAAC;YAChC,IAAI,cAAc;gBAChB,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,MAAM;gBACpC,MAAM,OAAO,YAAY,CAAC,EAAE;gBAE5B,MAAM,gBAAgB;oBACpB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;oBACtB,aAAa,SAAS;iBACvB;gBAED,WAAW,IAAI,CAAC,IAAI,UAAU;oBAC5B;oBACA,SAAS,aAAa,CAAC,QAAQ,EAAE,IAAI,aAAa,SAAS;gBAC7D;gBACA;YACF;YAEA,8BAA8B;YAC9B,MAAM,WAAkB,EAAE;YAC1B,IAAI,cAAc;YAElB,oDAAoD;YACpD,MAAM,YAAY;YAClB,MAAM,cAAc;YAEpB,kCAAkC;YAClC,wDAAwD;YACxD,SAAS,IAAI,CAAC,IAAI,QAAQ;gBAAE,MAAM;YAAY;YAE9C,WAAW,IAAI,CAAC,IAAI,UAAU;gBAAE,UAAU;YAAS;QACrD;QAEA,MAAM,MAAM,IAAI,SAAS;YACvB,UAAU;gBAAC;oBACT,YAAY,CAAC;oBACb,UAAU;gBACZ;aAAE;QACJ;QAEA,qCAAqC;QACrC,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC;QACrC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAO,EAAE;YAC9B,MAAM;QACR;QAEA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,SAAS,OAAO,CAAC,aAAa;QAC9C,KAAK,KAAK,CAAC,OAAO,GAAG;QAErB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,WAAW,IAAM,IAAI,eAAe,CAAC,MAAM;IAC7C,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,wHAAA,CAAA,cAAW,CAAC,4BAA4B,QAAQ;IAC5D;AACF;AAKO,eAAe,eACpB,QAAgB,EAChB,QAAgB,EAChB,OAAsB;IAEtB,OAAQ,QAAQ,MAAM;QACpB,KAAK;YACH,OAAO,aAAa,UAAU;QAChC,KAAK;YACH,OAAO,aAAa,UAAU,UAAU;QAC1C,KAAK;YACH,OAAO,YAAY,UAAU,UAAU;QACzC,KAAK;YACH,OAAO,aAAa,UAAU,UAAU;QAC1C;YACE,MAAM,IAAI,wHAAA,CAAA,cAAW,CAAC,CAAC,2BAA2B,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,MAAM;IACxF;AACF;AAKO,SAAS;IAKd,OAAO;QACL;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;AACH", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/Header.tsx"], "sourcesContent": ["/**\n * Header component with toolbar and navigation\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { exportMarkdown } from '@/utils/export';\nimport { ExportOptions } from '@/types';\n\ninterface HeaderProps {\n  onSidebarToggle: () => void;\n}\n\nexport function Header({ onSidebarToggle }: HeaderProps) {\n  const { state, saveCurrentFile, createNewFile, updateSettings } = useApp();\n  const [showExportMenu, setShowExportMenu] = useState(false);\n  const [isExporting, setIsExporting] = useState(false);\n\n  const handleNewFile = () => {\n    const fileName = prompt('Enter file name:');\n    if (fileName) {\n      createNewFile(fileName);\n    }\n  };\n\n  const handleSave = () => {\n    if (state.editor.currentFile) {\n      saveCurrentFile();\n    }\n  };\n\n  const handleExport = async (format: 'pdf' | 'docx' | 'txt' | 'html') => {\n    if (!state.editor.currentFile || !state.editor.content) {\n      alert('No file to export');\n      return;\n    }\n\n    setIsExporting(true);\n    setShowExportMenu(false);\n\n    try {\n      const options: ExportOptions = {\n        format,\n        includeMetadata: true,\n        pageSize: 'A4',\n        margins: { top: 20, right: 20, bottom: 20, left: 20 }\n      };\n\n      await exportMarkdown(\n        state.editor.content,\n        state.editor.currentFile.name,\n        options\n      );\n    } catch (error) {\n      console.error('Export failed:', error);\n      alert('Export failed. Please try again.');\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  const handleThemeToggle = () => {\n    updateSettings({\n      theme: state.settings.theme === 'light' ? 'dark' : 'light'\n    });\n  };\n\n  const handlePreviewModeChange = (mode: 'side' | 'preview' | 'edit') => {\n    updateSettings({ previewMode: mode });\n  };\n\n  return (\n    <header className=\"h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 no-print\">\n      {/* Left section */}\n      <div className=\"flex items-center space-x-4\">\n        {/* Sidebar toggle */}\n        <button\n          onClick={onSidebarToggle}\n          className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          title=\"Toggle sidebar\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        {/* Logo */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center\">\n            <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n          </div>\n          <span className=\"font-semibold text-gray-900 dark:text-white\">Markdown Editor</span>\n        </div>\n      </div>\n\n      {/* Center section - File actions */}\n      <div className=\"flex-1 flex items-center justify-center space-x-2\">\n        <button\n          onClick={handleNewFile}\n          className=\"px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n          title=\"New file (Ctrl+N)\"\n        >\n          New\n        </button>\n\n        <button\n          onClick={handleSave}\n          disabled={!state.editor.isModified}\n          className=\"px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n          title=\"Save file (Ctrl+S)\"\n        >\n          Save\n        </button>\n\n        {/* Export dropdown */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setShowExportMenu(!showExportMenu)}\n            disabled={!state.editor.currentFile || isExporting}\n            className=\"px-3 py-1.5 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-1\"\n            title=\"Export file\"\n          >\n            {isExporting ? (\n              <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n            ) : (\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            )}\n            <span>Export</span>\n          </button>\n\n          {showExportMenu && (\n            <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50\">\n              <div className=\"py-1\">\n                <button\n                  onClick={() => handleExport('txt')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  Plain Text (.txt)\n                </button>\n                <button\n                  onClick={() => handleExport('html')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  HTML (.html)\n                </button>\n                <button\n                  onClick={() => handleExport('pdf')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  PDF (.pdf)\n                </button>\n                <button\n                  onClick={() => handleExport('docx')}\n                  className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700\"\n                >\n                  Word Document (.docx)\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Right section */}\n      <div className=\"flex items-center space-x-2\">\n        {/* Preview mode toggle */}\n        <div className=\"flex items-center bg-gray-100 dark:bg-gray-700 rounded-md p-1\">\n          <button\n            onClick={() => handlePreviewModeChange('edit')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'edit'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Editor only\"\n          >\n            Edit\n          </button>\n          <button\n            onClick={() => handlePreviewModeChange('side')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'side'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Side by side\"\n          >\n            Split\n          </button>\n          <button\n            onClick={() => handlePreviewModeChange('preview')}\n            className={`px-2 py-1 text-xs rounded ${\n              state.settings.previewMode === 'preview'\n                ? 'bg-white dark:bg-gray-600 shadow-sm'\n                : 'hover:bg-gray-200 dark:hover:bg-gray-600'\n            } transition-colors`}\n            title=\"Preview only\"\n          >\n            Preview\n          </button>\n        </div>\n\n        {/* Theme toggle */}\n        <button\n          onClick={handleThemeToggle}\n          className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          title=\"Toggle theme\"\n        >\n          {state.settings.theme === 'light' ? (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n            </svg>\n          ) : (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n            </svg>\n          )}\n        </button>\n\n        {/* Current file indicator */}\n        {state.editor.currentFile && (\n          <div className=\"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400\">\n            <span>{state.editor.currentFile.name}</span>\n            {state.editor.isModified && (\n              <div className=\"w-2 h-2 bg-orange-500 rounded-full\" title=\"Unsaved changes\"></div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Click outside to close export menu */}\n      {showExportMenu && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => setShowExportMenu(false)}\n        ></div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;;;AAJA;;;;AAWO,SAAS,OAAO,EAAE,eAAe,EAAe;;IACrD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB,MAAM,WAAW,OAAO;QACxB,IAAI,UAAU;YACZ,cAAc;QAChB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,MAAM,MAAM,CAAC,WAAW,EAAE;YAC5B;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE;YACtD,MAAM;YACN;QACF;QAEA,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,UAAyB;gBAC7B;gBACA,iBAAiB;gBACjB,UAAU;gBACV,SAAS;oBAAE,KAAK;oBAAI,OAAO;oBAAI,QAAQ;oBAAI,MAAM;gBAAG;YACtD;YAEA,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EACjB,MAAM,MAAM,CAAC,OAAO,EACpB,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI,EAC7B;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;YACb,OAAO,MAAM,QAAQ,CAAC,KAAK,KAAK,UAAU,SAAS;QACrD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,eAAe;YAAE,aAAa;QAAK;IACrC;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAKzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC5E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAK,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;0BAKlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCACP;;;;;;kCAID,6LAAC;wBACC,SAAS;wBACT,UAAU,CAAC,MAAM,MAAM,CAAC,UAAU;wBAClC,WAAU;wBACV,OAAM;kCACP;;;;;;kCAKD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,UAAU,CAAC,MAAM,MAAM,CAAC,WAAW,IAAI;gCACvC,WAAU;gCACV,OAAM;;oCAEL,4BACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAGzE,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,SAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,SAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,0BAA0B,EACpC,MAAM,QAAQ,CAAC,WAAW,KAAK,YAC3B,wCACA,2CACL,kBAAkB,CAAC;gCACpB,OAAM;0CACP;;;;;;;;;;;;kCAMH,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEL,MAAM,QAAQ,CAAC,KAAK,KAAK,wBACxB,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;iDAGvE,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;oBAM1E,MAAM,MAAM,CAAC,WAAW,kBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAM,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI;;;;;;4BACnC,MAAM,MAAM,CAAC,UAAU,kBACtB,6LAAC;gCAAI,WAAU;gCAAqC,OAAM;;;;;;;;;;;;;;;;;;YAOjE,gCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,kBAAkB;;;;;;;;;;;;AAK3C;GAtOgB;;QACoD,iIAAA,CAAA,SAAM;;;KAD1D", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["/**\n * Sidebar component with file manager\n */\n\n'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { MarkdownFile } from '@/types';\nimport { formatDate, formatFileSize, readFileFromInput } from '@/utils/file';\n\nexport function Sidebar() {\n  const { state, openFile, createNewFile, deleteFile } = useApp();\n  const [showNewFileDialog, setShowNewFileDialog] = useState(false);\n  const [newFileName, setNewFileName] = useState('');\n  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileClick = (file: MarkdownFile) => {\n    if (!file.isDirectory) {\n      openFile(file);\n      setSelectedFileId(file.id);\n    }\n  };\n\n  const handleNewFile = () => {\n    if (newFileName.trim()) {\n      createNewFile(newFileName.trim());\n      setNewFileName('');\n      setShowNewFileDialog(false);\n    }\n  };\n\n  const handleDeleteFile = (fileId: string, fileName: string) => {\n    if (confirm(`Are you sure you want to delete \"${fileName}\"?`)) {\n      deleteFile(fileId);\n      if (selectedFileId === fileId) {\n        setSelectedFileId(null);\n      }\n    }\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files) return;\n\n    for (const file of Array.from(files)) {\n      try {\n        const content = await readFileFromInput(file);\n        createNewFile(file.name, content);\n      } catch (error) {\n        console.error('Failed to upload file:', error);\n        alert(`Failed to upload ${file.name}: ${error}`);\n      }\n    }\n\n    // Reset input\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const sortedFiles = [...state.fileManager.files].sort((a, b) => {\n    // Directories first, then files\n    if (a.isDirectory && !b.isDirectory) return -1;\n    if (!a.isDirectory && b.isDirectory) return 1;\n    // Then sort by name\n    return a.name.localeCompare(b.name);\n  });\n\n  return (\n    <div className=\"h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">Files</h2>\n        \n        {/* Action buttons */}\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setShowNewFileDialog(true)}\n            className=\"flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-1\"\n            title=\"Create new file\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n            </svg>\n            <span>New</span>\n          </button>\n          \n          <button\n            onClick={() => fileInputRef.current?.click()}\n            className=\"flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center justify-center space-x-1\"\n            title=\"Upload files\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n            </svg>\n            <span>Upload</span>\n          </button>\n        </div>\n\n        {/* Hidden file input */}\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\".md,.markdown,.mdown,.mkd,.mdx,.txt\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n      </div>\n\n      {/* File list */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {state.fileManager.isLoading ? (\n          <div className=\"p-4 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Loading files...</p>\n          </div>\n        ) : state.fileManager.error ? (\n          <div className=\"p-4 text-center\">\n            <p className=\"text-sm text-red-600 dark:text-red-400\">{state.fileManager.error}</p>\n          </div>\n        ) : sortedFiles.length === 0 ? (\n          <div className=\"p-4 text-center\">\n            <svg className=\"w-12 h-12 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">No files yet</p>\n            <p className=\"text-xs text-gray-500 dark:text-gray-500\">Create a new file or upload existing ones</p>\n          </div>\n        ) : (\n          <div className=\"p-2\">\n            {sortedFiles.map((file) => (\n              <div\n                key={file.id}\n                className={`group flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${\n                  selectedFileId === file.id\n                    ? 'bg-blue-100 dark:bg-blue-900/50'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                onClick={() => handleFileClick(file)}\n              >\n                <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n                  {/* File icon */}\n                  <div className=\"flex-shrink-0\">\n                    {file.isDirectory ? (\n                      <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    )}\n                  </div>\n\n                  {/* File info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </p>\n                    <div className=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                      <span>{formatDate(file.lastModified)}</span>\n                      {!file.isDirectory && (\n                        <>\n                          <span>•</span>\n                          <span>{formatFileSize(file.size)}</span>\n                        </>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {!file.isDirectory && (\n                  <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDeleteFile(file.id, file.name);\n                      }}\n                      className=\"p-1 text-red-600 hover:text-red-700 transition-colors\"\n                      title=\"Delete file\"\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                      </svg>\n                    </button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* New file dialog */}\n      {showNewFileDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Create New File\n            </h3>\n            \n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                File Name\n              </label>\n              <input\n                type=\"text\"\n                value={newFileName}\n                onChange={(e) => setNewFileName(e.target.value)}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter') {\n                    handleNewFile();\n                  } else if (e.key === 'Escape') {\n                    setShowNewFileDialog(false);\n                    setNewFileName('');\n                  }\n                }}\n                placeholder=\"my-document.md\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                autoFocus\n              />\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                .md extension will be added automatically if not provided\n              </p>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => {\n                  setShowNewFileDialog(false);\n                  setNewFileName('');\n                }}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleNewFile}\n                disabled={!newFileName.trim()}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n              >\n                Create\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AAEA;;;AALA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,SAAS;YACT,kBAAkB,KAAK,EAAE;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY,IAAI,IAAI;YACtB,cAAc,YAAY,IAAI;YAC9B,eAAe;YACf,qBAAqB;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,QAAQ,CAAC,iCAAiC,EAAE,SAAS,EAAE,CAAC,GAAG;YAC7D,WAAW;YACX,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;YACpB;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,OAAO;QAEZ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;YACpC,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACxC,cAAc,KAAK,IAAI,EAAE;YAC3B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,OAAO;YACjD;QACF;QAEA,cAAc;QACd,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,cAAc;WAAI,MAAM,WAAW,CAAC,KAAK;KAAC,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,gCAAgC;QAChC,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;QAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;QAC5C,oBAAoB;QACpB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IACpC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;gCACV,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCACC,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,WAAU;gCACV,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACZ,MAAM,WAAW,CAAC,SAAS,iBAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;2BAExD,MAAM,WAAW,CAAC,KAAK,iBACzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA0C,MAAM,WAAW,CAAC,KAAK;;;;;;;;;;2BAE9E,YAAY,MAAM,KAAK,kBACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAC7D,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;yCAG1D,6LAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;4BAEC,WAAW,CAAC,wFAAwF,EAClG,mBAAmB,KAAK,EAAE,GACtB,oCACA,4CACJ;4BACF,SAAS,IAAM,gBAAgB;;8CAE/B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW,iBACf,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;qEAGV,6LAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAM3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,YAAY;;;;;;wDAClC,CAAC,KAAK,WAAW,kBAChB;;8EACE,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQxC,CAAC,KAAK,WAAW,kBAChB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;wCACrC;wCACA,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;2BAnDxE,KAAK,EAAE;;;;;;;;;;;;;;;YA+DrB,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,qBAAqB;4CACrB,eAAe;wCACjB;oCACF;oCACA,aAAY;oCACZ,WAAU;oCACV,SAAS;;;;;;8CAEX,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;wCACP,qBAAqB;wCACrB,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC,YAAY,IAAI;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAlPgB;;QACyC,iIAAA,CAAA,SAAM;;;KAD/C", "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Editor/EditorToolbar.tsx"], "sourcesContent": ["/**\n * Editor toolbar with formatting buttons\n */\n\n'use client';\n\nimport React, { useState } from 'react';\n\ninterface EditorToolbarProps {\n  onInsertMarkdown: (before: string, after: string, placeholder: string) => void;\n}\n\nexport function EditorToolbar({ onInsertMarkdown }: EditorToolbarProps) {\n  const [showHeadingMenu, setShowHeadingMenu] = useState(false);\n  const [showTableDialog, setShowTableDialog] = useState(false);\n  const [tableRows, setTableRows] = useState(3);\n  const [tableCols, setTableCols] = useState(3);\n\n  const toolbarButtons = [\n    {\n      group: 'text',\n      buttons: [\n        {\n          icon: 'B',\n          title: 'Bold (Ctrl+B)',\n          action: () => onInsertMarkdown('**', '**', 'bold text'),\n          className: 'font-bold'\n        },\n        {\n          icon: 'I',\n          title: 'Italic (Ctrl+I)',\n          action: () => onInsertMarkdown('*', '*', 'italic text'),\n          className: 'italic'\n        },\n        {\n          icon: 'S',\n          title: 'Strikethrough',\n          action: () => onInsertMarkdown('~~', '~~', 'strikethrough text'),\n          className: 'line-through'\n        },\n        {\n          icon: '`',\n          title: 'Inline Code',\n          action: () => onInsertMarkdown('`', '`', 'code'),\n          className: 'font-mono'\n        }\n      ]\n    },\n    {\n      group: 'structure',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h7\" />\n            </svg>\n          ),\n          title: 'Headings',\n          action: () => setShowHeadingMenu(!showHeadingMenu),\n          hasDropdown: true\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n            </svg>\n          ),\n          title: 'Unordered List',\n          action: () => onInsertMarkdown('- ', '', 'list item')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n          ),\n          title: 'Ordered List',\n          action: () => onInsertMarkdown('1. ', '', 'list item')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n          ),\n          title: 'Blockquote',\n          action: () => onInsertMarkdown('> ', '', 'blockquote text')\n        }\n      ]\n    },\n    {\n      group: 'media',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n            </svg>\n          ),\n          title: 'Link (Ctrl+K)',\n          action: () => onInsertMarkdown('[', '](url)', 'link text')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n          ),\n          title: 'Image',\n          action: () => onInsertMarkdown('![', '](image-url)', 'alt text')\n        },\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n            </svg>\n          ),\n          title: 'Table',\n          action: () => setShowTableDialog(true)\n        }\n      ]\n    },\n    {\n      group: 'code',\n      buttons: [\n        {\n          icon: (\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n            </svg>\n          ),\n          title: 'Code Block',\n          action: () => onInsertMarkdown('```\\n', '\\n```', 'code here')\n        },\n        {\n          icon: '---',\n          title: 'Horizontal Rule',\n          action: () => onInsertMarkdown('\\n---\\n', '', ''),\n          className: 'text-xs'\n        }\n      ]\n    }\n  ];\n\n  const insertHeading = (level: number) => {\n    const hashes = '#'.repeat(level);\n    onInsertMarkdown(`${hashes} `, '', `Heading ${level}`);\n    setShowHeadingMenu(false);\n  };\n\n  const insertTable = () => {\n    const headers = Array(tableCols).fill('Header').map((h, i) => `${h} ${i + 1}`).join(' | ');\n    const separator = Array(tableCols).fill('---').join(' | ');\n    const rows = Array(tableRows - 1).fill(null).map((_, rowIndex) => \n      Array(tableCols).fill('Cell').map((c, colIndex) => `${c} ${rowIndex + 1}-${colIndex + 1}`).join(' | ')\n    ).join('\\n');\n    \n    const table = `| ${headers} |\\n| ${separator} |\\n| ${rows} |`;\n    onInsertMarkdown('\\n', '\\n', table);\n    setShowTableDialog(false);\n  };\n\n  return (\n    <div className=\"border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-2\">\n      <div className=\"flex items-center space-x-1\">\n        {toolbarButtons.map((group, groupIndex) => (\n          <React.Fragment key={group.group}>\n            {groupIndex > 0 && (\n              <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\"></div>\n            )}\n            \n            {group.buttons.map((button, buttonIndex) => (\n              <div key={buttonIndex} className=\"relative\">\n                <button\n                  onClick={button.action}\n                  className={`p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${button.className || ''}`}\n                  title={button.title}\n                >\n                  {typeof button.icon === 'string' ? (\n                    <span className=\"text-sm font-medium\">{button.icon}</span>\n                  ) : (\n                    button.icon\n                  )}\n                </button>\n\n                {/* Heading dropdown */}\n                {button.hasDropdown && showHeadingMenu && (\n                  <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50\">\n                    <div className=\"py-1\">\n                      {[1, 2, 3, 4, 5, 6].map(level => (\n                        <button\n                          key={level}\n                          onClick={() => insertHeading(level)}\n                          className=\"w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2\"\n                        >\n                          <span className=\"font-mono text-gray-500\">{'#'.repeat(level)}</span>\n                          <span style={{ fontSize: `${20 - level}px` }} className=\"font-semibold\">\n                            Heading {level}\n                          </span>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </React.Fragment>\n        ))}\n      </div>\n\n      {/* Table dialog */}\n      {showTableDialog && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              Insert Table\n            </h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Rows\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"2\"\n                  max=\"20\"\n                  value={tableRows}\n                  onChange={(e) => setTableRows(parseInt(e.target.value) || 2)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Columns\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"2\"\n                  max=\"10\"\n                  value={tableCols}\n                  onChange={(e) => setTableCols(parseInt(e.target.value) || 2)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowTableDialog(false)}\n                className=\"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={insertTable}\n                className=\"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Insert Table\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Click outside to close menus */}\n      {(showHeadingMenu || showTableDialog) && (\n        <div\n          className=\"fixed inset-0 z-40\"\n          onClick={() => {\n            setShowHeadingMenu(false);\n            setShowTableDialog(false);\n          }}\n        ></div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;;;AAFA;;AAQO,SAAS,cAAc,EAAE,gBAAgB,EAAsB;;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,SAAS;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,MAAM;oBAC3C,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,KAAK;oBACzC,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,MAAM;oBAC3C,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,KAAK;oBACzC,WAAW;gBACb;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,mBAAmB,CAAC;oBAClC,aAAa;gBACf;gBACA;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,IAAI;gBAC3C;gBACA;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,OAAO,IAAI;gBAC5C;gBACA;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,IAAI;gBAC3C;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,KAAK,UAAU;gBAChD;gBACA;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,MAAM,gBAAgB;gBACvD;gBACA;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,mBAAmB;gBACnC;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;gBACP;oBACE,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,QAAQ,IAAM,iBAAiB,SAAS,SAAS;gBACnD;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,QAAQ,IAAM,iBAAiB,WAAW,IAAI;oBAC9C,WAAW;gBACb;aACD;QACH;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,IAAI,MAAM,CAAC;QAC1B,iBAAiB,GAAG,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO;QACrD,mBAAmB;IACrB;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU,MAAM,WAAW,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,IAAM,GAAG,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;QACpF,MAAM,YAAY,MAAM,WAAW,IAAI,CAAC,OAAO,IAAI,CAAC;QACpD,MAAM,OAAO,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,WACnD,MAAM,WAAW,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAa,GAAG,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,GAAG,EAAE,IAAI,CAAC,QAChG,IAAI,CAAC;QAEP,MAAM,QAAQ,CAAC,EAAE,EAAE,QAAQ,MAAM,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,CAAC;QAC7D,iBAAiB,MAAM,MAAM;QAC7B,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,2BAC1B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;4BACZ,aAAa,mBACZ,6LAAC;gCAAI,WAAU;;;;;;4BAGhB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC1B,6LAAC;oCAAsB,WAAU;;sDAC/B,6LAAC;4CACC,SAAS,OAAO,MAAM;4CACtB,WAAW,CAAC,0EAA0E,EAAE,OAAO,SAAS,IAAI,IAAI;4CAChH,OAAO,OAAO,KAAK;sDAElB,OAAO,OAAO,IAAI,KAAK,yBACtB,6LAAC;gDAAK,WAAU;0DAAuB,OAAO,IAAI;;;;;uDAElD,OAAO,IAAI;;;;;;wCAKd,OAAO,WAAW,IAAI,iCACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAG;oDAAG;oDAAG;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAA,sBACtB,6LAAC;wDAEC,SAAS,IAAM,cAAc;wDAC7B,WAAU;;0EAEV,6LAAC;gEAAK,WAAU;0EAA2B,IAAI,MAAM,CAAC;;;;;;0EACtD,6LAAC;gEAAK,OAAO;oEAAE,UAAU,GAAG,KAAK,MAAM,EAAE,CAAC;gEAAC;gEAAG,WAAU;;oEAAgB;oEAC7D;;;;;;;;uDANN;;;;;;;;;;;;;;;;mCAnBP;;;;;;uBANO,MAAM,KAAK;;;;;;;;;;YA6CnC,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC1D,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC1D,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,CAAC,mBAAmB,eAAe,mBAClC,6LAAC;gBACC,WAAU;gBACV,SAAS;oBACP,mBAAmB;oBACnB,mBAAmB;gBACrB;;;;;;;;;;;;AAKV;GA1QgB;KAAA", "debugId": null}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Editor/EditorPane.tsx"], "sourcesContent": ["/**\n * Editor pane component with markdown editing capabilities\n */\n\n'use client';\n\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { debounce } from '@/utils/file';\nimport { EditorToolbar } from './EditorToolbar';\n\nexport function EditorPane() {\n  const { state, updateContent, dispatch } = useApp();\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const [isDragging, setIsDragging] = useState(false);\n\n  // Debounced content update to improve performance\n  const debouncedUpdateContent = useCallback(\n    debounce((content: string) => {\n      updateContent(content);\n    }, 300),\n    [updateContent]\n  );\n\n  // Handle content changes\n  const handleContentChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const content = event.target.value;\n    debouncedUpdateContent(content);\n    updateCursorPosition();\n  };\n\n  // Update cursor position\n  const updateCursorPosition = () => {\n    if (!textareaRef.current) return;\n\n    const textarea = textareaRef.current;\n    const content = textarea.value;\n    const cursorPos = textarea.selectionStart;\n    \n    const lines = content.substring(0, cursorPos).split('\\n');\n    const line = lines.length;\n    const column = lines[lines.length - 1].length + 1;\n\n    dispatch({\n      type: 'EDITOR_ACTION',\n      payload: {\n        type: 'SET_CURSOR_POSITION',\n        payload: { line, column }\n      }\n    });\n  };\n\n  // Handle keyboard shortcuts\n  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (event.ctrlKey || event.metaKey) {\n      switch (event.key) {\n        case 's':\n          event.preventDefault();\n          // Save functionality is handled by the header\n          break;\n        case 'b':\n          event.preventDefault();\n          insertMarkdown('**', '**', 'bold text');\n          break;\n        case 'i':\n          event.preventDefault();\n          insertMarkdown('*', '*', 'italic text');\n          break;\n        case 'k':\n          event.preventDefault();\n          insertMarkdown('[', '](url)', 'link text');\n          break;\n      }\n    }\n\n    // Tab handling for indentation\n    if (event.key === 'Tab') {\n      event.preventDefault();\n      const textarea = event.currentTarget;\n      const start = textarea.selectionStart;\n      const end = textarea.selectionEnd;\n      const value = textarea.value;\n\n      if (event.shiftKey) {\n        // Unindent\n        const lineStart = value.lastIndexOf('\\n', start - 1) + 1;\n        const lineText = value.substring(lineStart, start);\n        if (lineText.startsWith('  ')) {\n          const newValue = value.substring(0, lineStart) + \n                          lineText.substring(2) + \n                          value.substring(start);\n          textarea.value = newValue;\n          textarea.setSelectionRange(start - 2, end - 2);\n          debouncedUpdateContent(newValue);\n        }\n      } else {\n        // Indent\n        const newValue = value.substring(0, start) + '  ' + value.substring(end);\n        textarea.value = newValue;\n        textarea.setSelectionRange(start + 2, end + 2);\n        debouncedUpdateContent(newValue);\n      }\n    }\n  };\n\n  // Insert markdown formatting\n  const insertMarkdown = (before: string, after: string, placeholder: string) => {\n    if (!textareaRef.current) return;\n\n    const textarea = textareaRef.current;\n    const start = textarea.selectionStart;\n    const end = textarea.selectionEnd;\n    const selectedText = textarea.value.substring(start, end);\n    const replacement = selectedText || placeholder;\n    \n    const newValue = \n      textarea.value.substring(0, start) +\n      before + replacement + after +\n      textarea.value.substring(end);\n    \n    textarea.value = newValue;\n    \n    // Set cursor position\n    if (selectedText) {\n      textarea.setSelectionRange(start + before.length, start + before.length + replacement.length);\n    } else {\n      textarea.setSelectionRange(start + before.length, start + before.length + placeholder.length);\n    }\n    \n    textarea.focus();\n    debouncedUpdateContent(newValue);\n  };\n\n  // Handle drag and drop\n  const handleDragOver = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragging(false);\n\n    const files = Array.from(event.dataTransfer.files);\n    const imageFiles = files.filter(file => file.type.startsWith('image/'));\n    \n    if (imageFiles.length > 0) {\n      imageFiles.forEach(file => {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const dataUrl = e.target?.result as string;\n          const markdown = `![${file.name}](${dataUrl})`;\n          insertMarkdown('', '', markdown);\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n  };\n\n  // Focus textarea when file is opened\n  useEffect(() => {\n    if (state.editor.currentFile && textareaRef.current) {\n      textareaRef.current.focus();\n    }\n  }, [state.editor.currentFile]);\n\n  // Update cursor position on click\n  const handleClick = () => {\n    setTimeout(updateCursorPosition, 0);\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-white dark:bg-gray-900\">\n      {/* Toolbar */}\n      <EditorToolbar onInsertMarkdown={insertMarkdown} />\n\n      {/* Editor area */}\n      <div className=\"flex-1 relative\">\n        {state.editor.currentFile ? (\n          <div\n            className={`h-full relative ${isDragging ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <textarea\n              ref={textareaRef}\n              value={state.editor.content}\n              onChange={handleContentChange}\n              onKeyDown={handleKeyDown}\n              onClick={handleClick}\n              onKeyUp={updateCursorPosition}\n              className={`w-full h-full p-4 resize-none border-none outline-none bg-transparent text-gray-900 dark:text-white font-mono ${\n                state.settings.wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'\n              }`}\n              style={{\n                fontSize: `${state.settings.fontSize}px`,\n                lineHeight: '1.6',\n                tabSize: 2\n              }}\n              placeholder=\"Start writing your markdown here...\"\n              spellCheck={false}\n            />\n\n            {/* Line numbers */}\n            {state.settings.lineNumbers && (\n              <div className=\"absolute left-0 top-0 p-4 pointer-events-none select-none\">\n                <div\n                  className=\"text-gray-400 dark:text-gray-600 font-mono text-right\"\n                  style={{\n                    fontSize: `${state.settings.fontSize}px`,\n                    lineHeight: '1.6'\n                  }}\n                >\n                  {state.editor.content.split('\\n').map((_, index) => (\n                    <div key={index} className=\"pr-2\">\n                      {index + 1}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Drag overlay */}\n            {isDragging && (\n              <div className=\"absolute inset-0 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <svg className=\"w-12 h-12 text-blue-600 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                  </svg>\n                  <p className=\"text-blue-600 font-medium\">Drop images here to insert</p>\n                </div>\n              </div>\n            )}\n\n            {/* Loading overlay */}\n            {state.editor.isLoading && (\n              <div className=\"absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n                  <p className=\"text-gray-600 dark:text-gray-400\">Loading...</p>\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n            <div className=\"text-center\">\n              <svg className=\"w-16 h-16 mx-auto mb-4 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <h3 className=\"text-lg font-medium mb-2\">No file selected</h3>\n              <p className=\"text-sm\">Create a new file or select an existing one to start editing</p>\n            </div>\n          </div>\n        )}\n\n        {/* Error display */}\n        {state.editor.error && (\n          <div className=\"absolute bottom-4 right-4 bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700 rounded-md p-3 max-w-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm text-red-800 dark:text-red-200\">{state.editor.error}</span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kDAAkD;IAClD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACvC,CAAA,GAAA,uHAAA,CAAA,WAAQ,AAAD;0DAAE,CAAC;YACR,cAAc;QAChB;yDAAG,MACH;QAAC;KAAc;IAGjB,yBAAyB;IACzB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAAU,MAAM,MAAM,CAAC,KAAK;QAClC,uBAAuB;QACvB;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,UAAU,SAAS,KAAK;QAC9B,MAAM,YAAY,SAAS,cAAc;QAEzC,MAAM,QAAQ,QAAQ,SAAS,CAAC,GAAG,WAAW,KAAK,CAAC;QACpD,MAAM,OAAO,MAAM,MAAM;QACzB,MAAM,SAAS,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG;QAEhD,SAAS;YACP,MAAM;YACN,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE;oBAAM;gBAAO;YAC1B;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,EAAE;YAClC,OAAQ,MAAM,GAAG;gBACf,KAAK;oBACH,MAAM,cAAc;oBAEpB;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,MAAM,MAAM;oBAC3B;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,KAAK,KAAK;oBACzB;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB,eAAe,KAAK,UAAU;oBAC9B;YACJ;QACF;QAEA,+BAA+B;QAC/B,IAAI,MAAM,GAAG,KAAK,OAAO;YACvB,MAAM,cAAc;YACpB,MAAM,WAAW,MAAM,aAAa;YACpC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YACjC,MAAM,QAAQ,SAAS,KAAK;YAE5B,IAAI,MAAM,QAAQ,EAAE;gBAClB,WAAW;gBACX,MAAM,YAAY,MAAM,WAAW,CAAC,MAAM,QAAQ,KAAK;gBACvD,MAAM,WAAW,MAAM,SAAS,CAAC,WAAW;gBAC5C,IAAI,SAAS,UAAU,CAAC,OAAO;oBAC7B,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,aACpB,SAAS,SAAS,CAAC,KACnB,MAAM,SAAS,CAAC;oBAChC,SAAS,KAAK,GAAG;oBACjB,SAAS,iBAAiB,CAAC,QAAQ,GAAG,MAAM;oBAC5C,uBAAuB;gBACzB;YACF,OAAO;gBACL,SAAS;gBACT,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,SAAS,OAAO,MAAM,SAAS,CAAC;gBACpE,SAAS,KAAK,GAAG;gBACjB,SAAS,iBAAiB,CAAC,QAAQ,GAAG,MAAM;gBAC5C,uBAAuB;YACzB;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,CAAC,QAAgB,OAAe;QACrD,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,QAAQ,SAAS,cAAc;QACrC,MAAM,MAAM,SAAS,YAAY;QACjC,MAAM,eAAe,SAAS,KAAK,CAAC,SAAS,CAAC,OAAO;QACrD,MAAM,cAAc,gBAAgB;QAEpC,MAAM,WACJ,SAAS,KAAK,CAAC,SAAS,CAAC,GAAG,SAC5B,SAAS,cAAc,QACvB,SAAS,KAAK,CAAC,SAAS,CAAC;QAE3B,SAAS,KAAK,GAAG;QAEjB,sBAAsB;QACtB,IAAI,cAAc;YAChB,SAAS,iBAAiB,CAAC,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,YAAY,MAAM;QAC9F,OAAO;YACL,SAAS,iBAAiB,CAAC,QAAQ,OAAO,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,YAAY,MAAM;QAC9F;QAEA,SAAS,KAAK;QACd,uBAAuB;IACzB;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,YAAY,CAAC,KAAK;QACjD,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;QAE7D,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,MAAM,WAAW,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAC9C,eAAe,IAAI,IAAI;gBACzB;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,MAAM,MAAM,CAAC,WAAW,IAAI,YAAY,OAAO,EAAE;gBACnD,YAAY,OAAO,CAAC,KAAK;YAC3B;QACF;+BAAG;QAAC,MAAM,MAAM,CAAC,WAAW;KAAC;IAE7B,kCAAkC;IAClC,MAAM,cAAc;QAClB,WAAW,sBAAsB;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,gJAAA,CAAA,gBAAa;gBAAC,kBAAkB;;;;;;0BAGjC,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,CAAC,WAAW,iBACvB,6LAAC;wBACC,WAAW,CAAC,gBAAgB,EAAE,aAAa,mCAAmC,IAAI;wBAClF,YAAY;wBACZ,aAAa;wBACb,QAAQ;;0CAER,6LAAC;gCACC,KAAK;gCACL,OAAO,MAAM,MAAM,CAAC,OAAO;gCAC3B,UAAU;gCACV,WAAW;gCACX,SAAS;gCACT,SAAS;gCACT,WAAW,CAAC,8GAA8G,EACxH,MAAM,QAAQ,CAAC,QAAQ,GAAG,wBAAwB,kBAClD;gCACF,OAAO;oCACL,UAAU,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oCACxC,YAAY;oCACZ,SAAS;gCACX;gCACA,aAAY;gCACZ,YAAY;;;;;;4BAIb,MAAM,QAAQ,CAAC,WAAW,kBACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,UAAU,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;wCACxC,YAAY;oCACd;8CAEC,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,sBACxC,6LAAC;4CAAgB,WAAU;sDACxB,QAAQ;2CADD;;;;;;;;;;;;;;;4BASjB,4BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAuC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC9F,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,6LAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;4BAM9C,MAAM,MAAM,CAAC,SAAS,kBACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;6CAMxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAoC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC3F,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAM5B,MAAM,MAAM,CAAC,KAAK,kBACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAK,WAAU;8CAA0C,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1F;GAzQgB;;QAC6B,iIAAA,CAAA,SAAM;;;KADnC", "debugId": null}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Preview/PreviewPane.tsx"], "sourcesContent": ["/**\n * Preview pane component for rendered markdown\n */\n\n'use client';\n\nimport React, { useRef, useEffect, useState } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { extractHeadings } from '@/utils/markdown';\n\nexport function PreviewPane() {\n  const { state, dispatch } = useApp();\n  const previewRef = useRef<HTMLDivElement>(null);\n  const [showToc, setShowToc] = useState(false);\n  const [headings, setHeadings] = useState<Array<{ level: number; text: string; id: string }>>([]);\n\n  // Extract headings for table of contents\n  useEffect(() => {\n    if (state.editor.content) {\n      const extractedHeadings = extractHeadings(state.editor.content);\n      setHeadings(extractedHeadings);\n    } else {\n      setHeadings([]);\n    }\n  }, [state.editor.content]);\n\n  // Handle scroll synchronization\n  const handleScroll = () => {\n    if (!previewRef.current) return;\n    \n    const scrollPosition = previewRef.current.scrollTop;\n    dispatch({\n      type: 'PREVIEW_ACTION',\n      payload: { type: 'SET_SCROLL_POSITION', payload: scrollPosition }\n    });\n  };\n\n  // Handle anchor link clicks\n  const handleAnchorClick = (event: React.MouseEvent) => {\n    const target = event.target as HTMLElement;\n    if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {\n      event.preventDefault();\n      const id = target.getAttribute('href')?.substring(1);\n      if (id && previewRef.current) {\n        const element = previewRef.current.querySelector(`#${id}`);\n        if (element) {\n          element.scrollIntoView({ behavior: 'smooth' });\n        }\n      }\n    }\n  };\n\n  // Handle copy code button clicks\n  useEffect(() => {\n    const handleCopyCode = (event: Event) => {\n      const target = event.target as HTMLElement;\n      if (target.classList.contains('copy-code-btn')) {\n        const code = decodeURIComponent(target.getAttribute('data-code') || '');\n        navigator.clipboard.writeText(code).then(() => {\n          target.textContent = 'Copied!';\n          setTimeout(() => {\n            target.textContent = 'Copy';\n          }, 2000);\n        }).catch(() => {\n          target.textContent = 'Failed';\n          setTimeout(() => {\n            target.textContent = 'Copy';\n          }, 2000);\n        });\n      }\n    };\n\n    const previewElement = previewRef.current;\n    if (previewElement) {\n      previewElement.addEventListener('click', handleCopyCode);\n      return () => previewElement.removeEventListener('click', handleCopyCode);\n    }\n  }, [state.preview.htmlContent]);\n\n  const scrollToHeading = (id: string) => {\n    if (previewRef.current) {\n      const element = previewRef.current.querySelector(`#${id}`);\n      if (element) {\n        element.scrollIntoView({ behavior: 'smooth' });\n      }\n    }\n  };\n\n  return (\n    <div className=\"h-full flex flex-col bg-white dark:bg-gray-900\">\n      {/* Preview header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Preview</h2>\n        \n        <div className=\"flex items-center space-x-2\">\n          {/* Table of contents toggle */}\n          {headings.length > 0 && (\n            <button\n              onClick={() => setShowToc(!showToc)}\n              className={`p-2 rounded-md transition-colors ${\n                showToc \n                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600' \n                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n              title=\"Table of Contents\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 10h16M4 14h16M4 18h16\" />\n              </svg>\n            </button>\n          )}\n\n          {/* Print button */}\n          <button\n            onClick={() => window.print()}\n            className=\"p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            title=\"Print\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Main content area */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Table of contents sidebar */}\n        {showToc && headings.length > 0 && (\n          <div className=\"w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 overflow-y-auto\">\n            <div className=\"p-4\">\n              <h3 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-3\">\n                Table of Contents\n              </h3>\n              <nav className=\"space-y-1\">\n                {headings.map((heading, index) => (\n                  <button\n                    key={index}\n                    onClick={() => scrollToHeading(heading.id)}\n                    className={`block w-full text-left text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors ${\n                      heading.level > 1 ? `ml-${(heading.level - 1) * 3}` : ''\n                    }`}\n                    style={{ paddingLeft: `${(heading.level - 1) * 12}px` }}\n                  >\n                    {heading.text}\n                  </button>\n                ))}\n              </nav>\n            </div>\n          </div>\n        )}\n\n        {/* Preview content */}\n        <div className=\"flex-1 relative\">\n          {state.editor.currentFile ? (\n            <div\n              ref={previewRef}\n              className=\"h-full overflow-y-auto p-6 print-full-width\"\n              onScroll={handleScroll}\n              onClick={handleAnchorClick}\n            >\n              {state.preview.isLoading ? (\n                <div className=\"flex items-center justify-center h-32\">\n                  <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n                    <p className=\"text-gray-600 dark:text-gray-400\">Rendering preview...</p>\n                  </div>\n                </div>\n              ) : state.preview.error ? (\n                <div className=\"flex items-center justify-center h-32\">\n                  <div className=\"text-center\">\n                    <svg className=\"w-12 h-12 text-red-500 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <p className=\"text-red-600 dark:text-red-400\">{state.preview.error}</p>\n                  </div>\n                </div>\n              ) : state.preview.htmlContent ? (\n                <div\n                  className=\"prose prose-lg dark:prose-invert max-w-none\"\n                  dangerouslySetInnerHTML={{ __html: state.preview.htmlContent }}\n                />\n              ) : (\n                <div className=\"flex items-center justify-center h-32 text-gray-500 dark:text-gray-400\">\n                  <div className=\"text-center\">\n                    <svg className=\"w-12 h-12 mx-auto mb-2 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                    </svg>\n                    <p>Start typing to see the preview</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"h-full flex items-center justify-center text-gray-500 dark:text-gray-400\">\n              <div className=\"text-center\">\n                <svg className=\"w-16 h-16 mx-auto mb-4 opacity-50\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                </svg>\n                <h3 className=\"text-lg font-medium mb-2\">No file selected</h3>\n                <p className=\"text-sm\">Select a file to see the preview</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Custom styles for preview */}\n      <style jsx global>{`\n        .prose {\n          color: inherit;\n        }\n        \n        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {\n          position: relative;\n        }\n        \n        .prose .anchor-link {\n          position: absolute;\n          left: -1.5rem;\n          opacity: 0;\n          transition: opacity 0.2s ease;\n          text-decoration: none;\n          color: #6b7280;\n        }\n        \n        .prose h1:hover .anchor-link,\n        .prose h2:hover .anchor-link,\n        .prose h3:hover .anchor-link,\n        .prose h4:hover .anchor-link,\n        .prose h5:hover .anchor-link,\n        .prose h6:hover .anchor-link {\n          opacity: 1;\n        }\n        \n        .prose .code-block-wrapper {\n          position: relative;\n          margin: 1rem 0;\n        }\n        \n        .prose .code-block-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          background: #f3f4f6;\n          border: 1px solid #e5e7eb;\n          border-bottom: none;\n          border-radius: 0.375rem 0.375rem 0 0;\n          padding: 0.5rem 1rem;\n          font-size: 0.875rem;\n        }\n        \n        .dark .prose .code-block-header {\n          background: #374151;\n          border-color: #4b5563;\n        }\n        \n        .prose .code-language {\n          font-weight: 500;\n          color: #6b7280;\n        }\n        \n        .dark .prose .code-language {\n          color: #9ca3af;\n        }\n        \n        .prose .copy-code-btn {\n          background: #3b82f6;\n          color: white;\n          border: none;\n          border-radius: 0.25rem;\n          padding: 0.25rem 0.5rem;\n          font-size: 0.75rem;\n          cursor: pointer;\n          transition: background-color 0.2s ease;\n        }\n        \n        .prose .copy-code-btn:hover {\n          background: #2563eb;\n        }\n        \n        .prose .code-block-wrapper pre {\n          margin: 0;\n          border-radius: 0 0 0.375rem 0.375rem;\n          border: 1px solid #e5e7eb;\n          border-top: none;\n        }\n        \n        .dark .prose .code-block-wrapper pre {\n          border-color: #4b5563;\n        }\n        \n        .prose .table-wrapper {\n          overflow-x: auto;\n          margin: 1rem 0;\n        }\n        \n        .prose .markdown-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n        \n        .prose .markdown-table th,\n        .prose .markdown-table td {\n          border: 1px solid #e5e7eb;\n          padding: 0.5rem;\n          text-align: left;\n        }\n        \n        .dark .prose .markdown-table th,\n        .dark .prose .markdown-table td {\n          border-color: #4b5563;\n        }\n        \n        .prose .markdown-table th {\n          background: #f9fafb;\n          font-weight: 600;\n        }\n        \n        .dark .prose .markdown-table th {\n          background: #374151;\n        }\n        \n        /* Print styles */\n        @media print {\n          .prose {\n            max-width: none !important;\n          }\n          \n          .prose .anchor-link {\n            display: none;\n          }\n          \n          .prose .copy-code-btn {\n            display: none;\n          }\n          \n          .prose .code-block-header {\n            background: #f9fafb !important;\n            color: #000 !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID;AACA;AACA;;;AAJA;;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IACjC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD,EAAE;IAE/F,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;gBACxB,MAAM,oBAAoB,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;gBAC9D,YAAY;YACd,OAAO;gBACL,YAAY,EAAE;YAChB;QACF;gCAAG;QAAC,MAAM,MAAM,CAAC,OAAO;KAAC;IAEzB,gCAAgC;IAChC,MAAM,eAAe;QACnB,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,MAAM,iBAAiB,WAAW,OAAO,CAAC,SAAS;QACnD,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAuB,SAAS;YAAe;QAClE;IACF;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAI,OAAO,OAAO,KAAK,OAAO,OAAO,YAAY,CAAC,SAAS,WAAW,MAAM;YAC1E,MAAM,cAAc;YACpB,MAAM,KAAK,OAAO,YAAY,CAAC,SAAS,UAAU;YAClD,IAAI,MAAM,WAAW,OAAO,EAAE;gBAC5B,MAAM,UAAU,WAAW,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;gBACzD,IAAI,SAAS;oBACX,QAAQ,cAAc,CAAC;wBAAE,UAAU;oBAAS;gBAC9C;YACF;QACF;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;wDAAiB,CAAC;oBACtB,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,kBAAkB;wBAC9C,MAAM,OAAO,mBAAmB,OAAO,YAAY,CAAC,gBAAgB;wBACpE,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI;oEAAC;gCACvC,OAAO,WAAW,GAAG;gCACrB;4EAAW;wCACT,OAAO,WAAW,GAAG;oCACvB;2EAAG;4BACL;mEAAG,KAAK;oEAAC;gCACP,OAAO,WAAW,GAAG;gCACrB;4EAAW;wCACT,OAAO,WAAW,GAAG;oCACvB;2EAAG;4BACL;;oBACF;gBACF;;YAEA,MAAM,iBAAiB,WAAW,OAAO;YACzC,IAAI,gBAAgB;gBAClB,eAAe,gBAAgB,CAAC,SAAS;gBACzC;6CAAO,IAAM,eAAe,mBAAmB,CAAC,SAAS;;YAC3D;QACF;gCAAG;QAAC,MAAM,OAAO,CAAC,WAAW;KAAC;IAE9B,MAAM,kBAAkB,CAAC;QACvB,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,UAAU,WAAW,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;YACzD,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;IACF;IAEA,qBACE,6LAAC;kDAAc;;0BAEb,6LAAC;0DAAc;;kCACb,6LAAC;kEAAa;kCAAsD;;;;;;kCAEpE,6LAAC;kEAAc;;4BAEZ,SAAS,MAAM,GAAG,mBACjB,6LAAC;gCACC,SAAS,IAAM,WAAW,CAAC;gCAM3B,OAAM;0EALK,CAAC,iCAAiC,EAC3C,UACI,kDACA,4CACJ;0CAGF,cAAA,6LAAC;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8EAApD;8CACb,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;0CAM3E,6LAAC;gCACC,SAAS,IAAM,OAAO,KAAK;gCAE3B,OAAM;0EADI;0CAGV,cAAA,6LAAC;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8EAApD;8CACb,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;0DAAc;;oBAEZ,WAAW,SAAS,MAAM,GAAG,mBAC5B,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAa;8CAA2D;;;;;;8CAGzE,6LAAC;8EAAc;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAEC,SAAS,IAAM,gBAAgB,QAAQ,EAAE;4CAIzC,OAAO;gDAAE,aAAa,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC;4CAAC;sFAH3C,CAAC,4HAA4H,EACtI,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IACtD;sDAGD,QAAQ,IAAI;2CAPR;;;;;;;;;;;;;;;;;;;;;kCAgBjB,6LAAC;kEAAc;kCACZ,MAAM,MAAM,CAAC,WAAW,iBACvB,6LAAC;4BACC,KAAK;4BAEL,UAAU;4BACV,SAAS;sEAFC;sCAIT,MAAM,OAAO,CAAC,SAAS,iBACtB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAAmC;;;;;;;;;;;;;;;;uCAGlD,MAAM,OAAO,CAAC,KAAK,iBACrB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;4CAAoD,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sFAAhF;sDACb,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;sDAEvE,6LAAC;sFAAY;sDAAkC,MAAM,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;uCAGpE,MAAM,OAAO,CAAC,WAAW,iBAC3B,6LAAC;gCAEC,yBAAyB;oCAAE,QAAQ,MAAM,OAAO,CAAC,WAAW;gCAAC;0EADnD;;;;;qDAIZ,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;4CAAkD,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sFAA9E;;8DACb,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;8DACrE,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;sDAEvE,6LAAC;;sDAAE;;;;;;;;;;;;;;;;;;;;;iDAMX,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC;wCAAkD,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kFAA9E;;0DACb,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;0DACrE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;kDAEvE,6LAAC;kFAAa;kDAA2B;;;;;;kDACzC,6LAAC;kFAAY;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiJvC;GAjVgB;;QACc,iIAAA,CAAA,SAAM;;;KADpB", "debugId": null}}, {"offset": {"line": 2824, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/StatusBar.tsx"], "sourcesContent": ["/**\n * Status bar component showing file info and editor stats\n */\n\n'use client';\n\nimport React, { useMemo } from 'react';\nimport { useApp } from '@/contexts/AppContext';\nimport { getWordCount, validateMarkdown } from '@/utils/markdown';\n\nexport function StatusBar() {\n  const { state } = useApp();\n\n  // Calculate word count and validation\n  const stats = useMemo(() => {\n    if (!state.editor.content) {\n      return {\n        words: 0,\n        characters: 0,\n        charactersNoSpaces: 0,\n        paragraphs: 0,\n        readingTime: 0,\n        isValid: true,\n        errors: []\n      };\n    }\n\n    const wordCount = getWordCount(state.editor.content);\n    const validation = validateMarkdown(state.editor.content);\n\n    return {\n      ...wordCount,\n      ...validation\n    };\n  }, [state.editor.content]);\n\n  const formatTime = (minutes: number): string => {\n    if (minutes < 1) return '< 1 min read';\n    if (minutes === 1) return '1 min read';\n    return `${minutes} min read`;\n  };\n\n  return (\n    <div className=\"h-6 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 text-xs text-gray-600 dark:text-gray-400 no-print\">\n      {/* Left section - File info */}\n      <div className=\"flex items-center space-x-4\">\n        {state.editor.currentFile ? (\n          <>\n            <span className=\"flex items-center space-x-1\">\n              <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <span>{state.editor.currentFile.name}</span>\n            </span>\n\n            {state.editor.isModified && (\n              <span className=\"flex items-center space-x-1 text-orange-600 dark:text-orange-400\">\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                <span>Modified</span>\n              </span>\n            )}\n\n            {state.settings.autoSave && (\n              <span className=\"flex items-center space-x-1 text-green-600 dark:text-green-400\">\n                <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span>Auto-save</span>\n              </span>\n            )}\n          </>\n        ) : (\n          <span>No file open</span>\n        )}\n      </div>\n\n      {/* Center section - Document stats */}\n      <div className=\"flex items-center space-x-4\">\n        {state.editor.content && (\n          <>\n            <span>{stats.words} words</span>\n            <span>•</span>\n            <span>{stats.characters} characters</span>\n            <span>•</span>\n            <span>{stats.paragraphs} paragraphs</span>\n            <span>•</span>\n            <span>{formatTime(stats.readingTime)}</span>\n          </>\n        )}\n      </div>\n\n      {/* Right section - Editor info */}\n      <div className=\"flex items-center space-x-4\">\n        {/* Validation status */}\n        {state.editor.content && (\n          <div className=\"flex items-center space-x-1\">\n            {stats.isValid ? (\n              <>\n                <svg className=\"w-3 h-3 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-green-600 dark:text-green-400\">Valid</span>\n              </>\n            ) : (\n              <>\n                <svg className=\"w-3 h-3 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <span className=\"text-red-600 dark:text-red-400\">\n                  {stats.errors.filter(e => e.type === 'error').length} errors\n                </span>\n              </>\n            )}\n          </div>\n        )}\n\n        {/* Cursor position */}\n        <span>\n          Ln {state.editor.cursorPosition.line}, Col {state.editor.cursorPosition.column}\n        </span>\n\n        {/* Theme indicator */}\n        <span className=\"flex items-center space-x-1\">\n          {state.settings.theme === 'light' ? (\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n            </svg>\n          ) : (\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n            </svg>\n          )}\n          <span className=\"capitalize\">{state.settings.theme}</span>\n        </span>\n\n        {/* Preview mode */}\n        <span className=\"capitalize\">{state.settings.previewMode}</span>\n\n        {/* Loading indicators */}\n        {(state.editor.isLoading || state.preview.isLoading || state.fileManager.isLoading) && (\n          <div className=\"flex items-center space-x-1\">\n            <div className=\"w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin\"></div>\n            <span>Loading...</span>\n          </div>\n        )}\n\n        {/* Error indicators */}\n        {(state.editor.error || state.preview.error || state.fileManager.error) && (\n          <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span>Error</span>\n          </div>\n        )}\n      </div>\n\n      {/* Tooltip for validation errors */}\n      {!stats.isValid && stats.errors.length > 0 && (\n        <div className=\"fixed bottom-8 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg p-3 max-w-sm z-50\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">Validation Issues</h4>\n          <div className=\"space-y-1\">\n            {stats.errors.slice(0, 5).map((error, index) => (\n              <div key={index} className=\"text-xs\">\n                <span className={`font-medium ${error.type === 'error' ? 'text-red-600' : 'text-yellow-600'}`}>\n                  Line {error.line}:\n                </span>\n                <span className=\"ml-1 text-gray-600 dark:text-gray-400\">\n                  {error.message}\n                </span>\n              </div>\n            ))}\n            {stats.errors.length > 5 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                ... and {stats.errors.length - 5} more\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAID;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAEvB,sCAAsC;IACtC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE;YACpB,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,EAAE;gBACzB,OAAO;oBACL,OAAO;oBACP,YAAY;oBACZ,oBAAoB;oBACpB,YAAY;oBACZ,aAAa;oBACb,SAAS;oBACT,QAAQ,EAAE;gBACZ;YACF;YAEA,MAAM,YAAY,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;YACnD,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,MAAM,CAAC,OAAO;YAExD,OAAO;gBACL,GAAG,SAAS;gBACZ,GAAG,UAAU;YACf;QACF;mCAAG;QAAC,MAAM,MAAM,CAAC,OAAO;KAAC;IAEzB,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,YAAY,GAAG,OAAO;QAC1B,OAAO,GAAG,QAAQ,SAAS,CAAC;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,CAAC,WAAW,iBACvB;;sCACE,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;8CAAM,MAAM,MAAM,CAAC,WAAW,CAAC,IAAI;;;;;;;;;;;;wBAGrC,MAAM,MAAM,CAAC,UAAU,kBACtB,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;wBAIT,MAAM,QAAQ,CAAC,QAAQ,kBACtB,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;8CAAK;;;;;;;;;;;;;iDAKZ,6LAAC;8BAAK;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,CAAC,OAAO,kBACnB;;sCACE,6LAAC;;gCAAM,MAAM,KAAK;gCAAC;;;;;;;sCACnB,6LAAC;sCAAK;;;;;;sCACN,6LAAC;;gCAAM,MAAM,UAAU;gCAAC;;;;;;;sCACxB,6LAAC;sCAAK;;;;;;sCACN,6LAAC;;gCAAM,MAAM,UAAU;gCAAC;;;;;;;sCACxB,6LAAC;sCAAK;;;;;;sCACN,6LAAC;sCAAM,WAAW,MAAM,WAAW;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;;oBAEZ,MAAM,MAAM,CAAC,OAAO,kBACnB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,OAAO,iBACZ;;8CACE,6LAAC;oCAAI,WAAU;oCAAyB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAChF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;;yDAGvD;;8CACE,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;oCAAK,WAAU;;wCACb,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;kCAQ/D,6LAAC;;4BAAK;4BACA,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI;4BAAC;4BAAO,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM;;;;;;;kCAIhF,6LAAC;wBAAK,WAAU;;4BACb,MAAM,QAAQ,CAAC,KAAK,KAAK,wBACxB,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;qDAGvE,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAGzE,6LAAC;gCAAK,WAAU;0CAAc,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;kCAIpD,6LAAC;wBAAK,WAAU;kCAAc,MAAM,QAAQ,CAAC,WAAW;;;;;;oBAGvD,CAAC,MAAM,MAAM,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,WAAW,CAAC,SAAS,mBAChF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;oBAKT,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,MAAM,WAAW,CAAC,KAAK,mBACpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,CAAC,MAAM,OAAO,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,mBACvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAC/D,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACpC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,MAAM,IAAI,KAAK,UAAU,iBAAiB,mBAAmB;;gDAAE;gDACvF,MAAM,IAAI;gDAAC;;;;;;;sDAEnB,6LAAC;4CAAK,WAAU;sDACb,MAAM,OAAO;;;;;;;mCALR;;;;;4BASX,MAAM,MAAM,CAAC,MAAM,GAAG,mBACrB,6LAAC;gCAAI,WAAU;;oCAA2C;oCAC/C,MAAM,MAAM,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA5KgB;;QACI,iIAAA,CAAA,SAAM;;;KADV", "debugId": null}}, {"offset": {"line": 3363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["/**\n * Main layout component with resizable panes\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Allotment } from 'allotment';\nimport { useApp } from '@/contexts/AppContext';\nimport { Header } from './Header';\nimport { Sidebar } from './Sidebar';\nimport { EditorPane } from '../Editor/EditorPane';\nimport { PreviewPane } from '../Preview/PreviewPane';\nimport { StatusBar } from './StatusBar';\nimport 'allotment/dist/style.css';\n\ninterface MainLayoutProps {\n  children?: React.ReactNode;\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const { state } = useApp();\n  const [sidebarWidth, setSidebarWidth] = useState(300);\n  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize layout\n  useEffect(() => {\n    // Load saved layout preferences\n    const savedSidebarWidth = localStorage.getItem('mdeditor_sidebar_width');\n    const savedSidebarCollapsed = localStorage.getItem('mdeditor_sidebar_collapsed');\n\n    if (savedSidebarWidth) {\n      setSidebarWidth(parseInt(savedSidebarWidth, 10));\n    }\n\n    if (savedSidebarCollapsed) {\n      setIsSidebarCollapsed(savedSidebarCollapsed === 'true');\n    }\n\n    setIsLoading(false);\n  }, []);\n\n  // Save layout preferences\n  useEffect(() => {\n    if (!isLoading) {\n      localStorage.setItem('mdeditor_sidebar_width', sidebarWidth.toString());\n      localStorage.setItem('mdeditor_sidebar_collapsed', isSidebarCollapsed.toString());\n    }\n  }, [sidebarWidth, isSidebarCollapsed, isLoading]);\n\n  const handleSidebarToggle = () => {\n    setIsSidebarCollapsed(!isSidebarCollapsed);\n  };\n\n  const renderMainContent = () => {\n    const { previewMode } = state.settings;\n\n    switch (previewMode) {\n      case 'edit':\n        return <EditorPane />;\n      case 'preview':\n        return <PreviewPane />;\n      case 'side':\n      default:\n        return (\n          <Allotment\n            defaultSizes={[50, 50]}\n            minSize={200}\n            resizerStyle={{\n              width: '4px',\n              backgroundColor: 'var(--border-color)',\n              cursor: 'col-resize'\n            }}\n          >\n            <Allotment.Pane>\n              <EditorPane />\n            </Allotment.Pane>\n            <Allotment.Pane>\n              <PreviewPane />\n            </Allotment.Pane>\n          </Allotment>\n        );\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Loading Markdown Editor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`h-screen flex flex-col ${state.settings.theme === 'dark' ? 'dark' : ''}`}>\n      {/* Header */}\n      <Header onSidebarToggle={handleSidebarToggle} />\n\n      {/* Main content area */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {isSidebarCollapsed ? (\n          // When sidebar is collapsed, show only main content\n          <div className=\"w-full h-full bg-white dark:bg-gray-900\">\n            {renderMainContent()}\n          </div>\n        ) : (\n          // When sidebar is open, use Allotment for resizable layout\n          <Allotment\n            defaultSizes={[sidebarWidth, 1000 - sidebarWidth]}\n            minSize={200}\n            resizerStyle={{\n              width: '4px',\n              backgroundColor: 'var(--border-color)',\n              cursor: 'col-resize'\n            }}\n            onChangeSize={(sizes) => {\n              if (sizes[0]) {\n                setSidebarWidth(sizes[0]);\n              }\n            }}\n          >\n            {/* Sidebar */}\n            <Allotment.Pane>\n              <div className=\"h-full w-full\">\n                <Sidebar />\n              </div>\n            </Allotment.Pane>\n\n            {/* Main editor/preview area */}\n            <Allotment.Pane>\n              <div className=\"h-full bg-white dark:bg-gray-900\">\n                {renderMainContent()}\n              </div>\n            </Allotment.Pane>\n          </Allotment>\n        )}\n      </div>\n\n      {/* Status bar */}\n      <StatusBar />\n\n      {/* Custom styles */}\n      <style jsx global>{`\n        :root {\n          --border-color: #e5e7eb;\n          --sidebar-bg: #f9fafb;\n          --sidebar-border: #e5e7eb;\n        }\n\n        .dark {\n          --border-color: #374151;\n          --sidebar-bg: #1f2937;\n          --sidebar-border: #374151;\n        }\n\n        .allotment > .allotment-pane {\n          overflow: hidden;\n        }\n\n        .allotment-separator {\n          background-color: var(--border-color) !important;\n          transition: background-color 0.2s ease;\n        }\n\n        .allotment-separator:hover {\n          background-color: #3b82f6 !important;\n        }\n\n        /* Responsive design */\n        @media (max-width: 768px) {\n          .allotment-separator {\n            width: 2px !important;\n          }\n        }\n\n        /* Scrollbar styling */\n        ::-webkit-scrollbar {\n          width: 8px;\n          height: 8px;\n        }\n\n        ::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        ::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 4px;\n        }\n\n        ::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n\n        .dark ::-webkit-scrollbar-thumb {\n          background: #4b5563;\n        }\n\n        .dark ::-webkit-scrollbar-thumb:hover {\n          background: #6b7280;\n        }\n\n        /* Focus styles */\n        .allotment:focus-within .allotment-separator {\n          background-color: #3b82f6 !important;\n        }\n\n        /* Animation for smooth transitions */\n        .transition-layout {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        /* Print styles */\n        @media print {\n          .no-print {\n            display: none !important;\n          }\n\n          .print-full-width {\n            width: 100% !important;\n            max-width: none !important;\n          }\n        }\n\n        /* High contrast mode support */\n        @media (prefers-contrast: high) {\n          .allotment-separator {\n            background-color: #000 !important;\n          }\n\n          .dark .allotment-separator {\n            background-color: #fff !important;\n          }\n        }\n\n        /* Reduced motion support */\n        @media (prefers-reduced-motion: reduce) {\n          .transition-layout,\n          .allotment-separator {\n            transition: none !important;\n          }\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;;AAgBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,gCAAgC;YAChC,MAAM,oBAAoB,aAAa,OAAO,CAAC;YAC/C,MAAM,wBAAwB,aAAa,OAAO,CAAC;YAEnD,IAAI,mBAAmB;gBACrB,gBAAgB,SAAS,mBAAmB;YAC9C;YAEA,IAAI,uBAAuB;gBACzB,sBAAsB,0BAA0B;YAClD;YAEA,aAAa;QACf;+BAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,WAAW;gBACd,aAAa,OAAO,CAAC,0BAA0B,aAAa,QAAQ;gBACpE,aAAa,OAAO,CAAC,8BAA8B,mBAAmB,QAAQ;YAChF;QACF;+BAAG;QAAC;QAAc;QAAoB;KAAU;IAEhD,MAAM,sBAAsB;QAC1B,sBAAsB,CAAC;IACzB;IAEA,MAAM,oBAAoB;QACxB,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;QAEtC,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6IAAA,CAAA,aAAU;;;;;YACpB,KAAK;gBACH,qBAAO,6LAAC,+IAAA,CAAA,cAAW;;;;;YACrB,KAAK;YACL;gBACE,qBACE,6LAAC,+IAAA,CAAA,YAAS;oBACR,cAAc;wBAAC;wBAAI;qBAAG;oBACtB,SAAS;oBACT,cAAc;wBACZ,OAAO;wBACP,iBAAiB;wBACjB,QAAQ;oBACV;;sCAEA,6LAAC,+IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;sCAEb,6LAAC,+IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,6LAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;QAItB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,6LAAC;kDAAe,CAAC,uBAAuB,EAAE,MAAM,QAAQ,CAAC,KAAK,KAAK,SAAS,SAAS,IAAI;;0BAEvF,6LAAC,yIAAA,CAAA,SAAM;gBAAC,iBAAiB;;;;;;0BAGzB,6LAAC;0DAAc;0BACZ,qBACC,oDAAoD;8BACpD,6LAAC;8DAAc;8BACZ;;;;;2BAGH,2DAA2D;8BAC3D,6LAAC,+IAAA,CAAA,YAAS;oBACR,cAAc;wBAAC;wBAAc,OAAO;qBAAa;oBACjD,SAAS;oBACT,cAAc;wBACZ,OAAO;wBACP,iBAAiB;wBACjB,QAAQ;oBACV;oBACA,cAAc,CAAC;wBACb,IAAI,KAAK,CAAC,EAAE,EAAE;4BACZ,gBAAgB,KAAK,CAAC,EAAE;wBAC1B;oBACF;;sCAGA,6LAAC,+IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,6LAAC;0EAAc;0CACb,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;;;;;;sCAKZ,6LAAC,+IAAA,CAAA,YAAS,CAAC,IAAI;sCACb,cAAA,6LAAC;0EAAc;0CACZ;;;;;;;;;;;;;;;;;;;;;;0BAQX,6LAAC,4IAAA,CAAA,YAAS;;;;;;;;;;;;;;;AA0GhB;GArOgB;;QACI,iIAAA,CAAA,SAAM;;;KADV", "debugId": null}}]}