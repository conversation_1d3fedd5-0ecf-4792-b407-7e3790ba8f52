{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/types/index.ts"], "sourcesContent": ["/**\n * Core type definitions for the Markdown Editor application\n */\n\nexport interface MarkdownFile {\n  id: string;\n  name: string;\n  content: string;\n  path: string;\n  lastModified: Date;\n  size: number;\n  isDirectory: boolean;\n  parentId?: string;\n}\n\nexport interface EditorSettings {\n  theme: 'light' | 'dark';\n  fontSize: number;\n  lineNumbers: boolean;\n  wordWrap: boolean;\n  previewMode: 'side' | 'preview' | 'edit';\n  autoSave: boolean;\n  autoSaveInterval: number; // in milliseconds\n}\n\nexport interface ExportOptions {\n  format: 'pdf' | 'docx' | 'txt' | 'html';\n  includeMetadata: boolean;\n  pageSize?: 'A4' | 'Letter' | 'Legal';\n  margins?: {\n    top: number;\n    right: number;\n    bottom: number;\n    left: number;\n  };\n}\n\nexport interface FileManagerState {\n  currentDirectory: string;\n  selectedFiles: string[];\n  files: MarkdownFile[];\n  isLoading: boolean;\n  error: string | null;\n}\n\nexport interface EditorState {\n  currentFile: MarkdownFile | null;\n  content: string;\n  isModified: boolean;\n  isLoading: boolean;\n  error: string | null;\n  cursorPosition: {\n    line: number;\n    column: number;\n  };\n}\n\nexport interface PreviewState {\n  htmlContent: string;\n  isLoading: boolean;\n  error: string | null;\n  scrollPosition: number;\n}\n\nexport interface CloudProvider {\n  id: string;\n  name: string;\n  isConnected: boolean;\n  accessToken?: string;\n}\n\nexport interface CollaborationUser {\n  id: string;\n  name: string;\n  email: string;\n  avatar?: string;\n  isOnline: boolean;\n  cursorPosition?: {\n    line: number;\n    column: number;\n  };\n}\n\nexport interface CollaborationState {\n  isEnabled: boolean;\n  users: CollaborationUser[];\n  roomId: string | null;\n  isConnected: boolean;\n}\n\nexport interface AppState {\n  editor: EditorState;\n  preview: PreviewState;\n  fileManager: FileManagerState;\n  settings: EditorSettings;\n  collaboration: CollaborationState;\n  cloudProviders: CloudProvider[];\n}\n\n// Action types for state management\nexport type EditorAction =\n  | { type: 'SET_CONTENT'; payload: string }\n  | { type: 'SET_CURRENT_FILE'; payload: MarkdownFile | null }\n  | { type: 'SET_MODIFIED'; payload: boolean }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_CURSOR_POSITION'; payload: { line: number; column: number } };\n\nexport type FileManagerAction =\n  | { type: 'SET_FILES'; payload: MarkdownFile[] }\n  | { type: 'ADD_FILE'; payload: MarkdownFile }\n  | { type: 'UPDATE_FILE'; payload: MarkdownFile }\n  | { type: 'DELETE_FILE'; payload: string }\n  | { type: 'SET_CURRENT_DIRECTORY'; payload: string }\n  | { type: 'SET_SELECTED_FILES'; payload: string[] }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null };\n\nexport type PreviewAction =\n  | { type: 'SET_HTML_CONTENT'; payload: string }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null }\n  | { type: 'SET_SCROLL_POSITION'; payload: number };\n\nexport type SettingsAction =\n  | { type: 'SET_THEME'; payload: 'light' | 'dark' }\n  | { type: 'SET_FONT_SIZE'; payload: number }\n  | { type: 'SET_LINE_NUMBERS'; payload: boolean }\n  | { type: 'SET_WORD_WRAP'; payload: boolean }\n  | { type: 'SET_PREVIEW_MODE'; payload: 'side' | 'preview' | 'edit' }\n  | { type: 'SET_AUTO_SAVE'; payload: boolean }\n  | { type: 'SET_AUTO_SAVE_INTERVAL'; payload: number };\n\n// Utility types\nexport type DeepPartial<T> = {\n  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];\n};\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  pageSize: number;\n  hasNext: boolean;\n  hasPrevious: boolean;\n}\n\n// Event types\nexport interface FileEvent {\n  type: 'create' | 'update' | 'delete' | 'rename';\n  file: MarkdownFile;\n  timestamp: Date;\n}\n\nexport interface EditorEvent {\n  type: 'content-change' | 'cursor-move' | 'selection-change';\n  data: any;\n  timestamp: Date;\n}\n\n// Plugin system types\nexport interface EditorPlugin {\n  id: string;\n  name: string;\n  version: string;\n  description: string;\n  isEnabled: boolean;\n  initialize: (editor: any) => void;\n  destroy: () => void;\n}\n\n// Error types\nexport class EditorError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'EditorError';\n  }\n}\n\nexport class FileSystemError extends EditorError {\n  constructor(message: string, public operation: string, details?: any) {\n    super(message, 'FILESYSTEM_ERROR', details);\n    this.name = 'FileSystemError';\n  }\n}\n\nexport class ExportError extends EditorError {\n  constructor(message: string, public format: string, details?: any) {\n    super(message, 'EXPORT_ERROR', details);\n    this.name = 'ExportError';\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAiLM,MAAM,oBAAoB;;;IAC/B,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAHC,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,wBAAwB;;IACnC,YAAY,OAAe,EAAE,AAAO,SAAiB,EAAE,OAAa,CAAE;QACpE,KAAK,CAAC,SAAS,oBAAoB,eADD,YAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,oBAAoB;;IAC/B,YAAY,OAAe,EAAE,AAAO,MAAc,EAAE,OAAa,CAAE;QACjE,KAAK,CAAC,SAAS,gBAAgB,eADG,SAAA;QAElC,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/utils/file.ts"], "sourcesContent": ["/**\n * File system utilities\n */\n\nimport { MarkdownFile, FileSystemError } from '@/types';\n\n/**\n * Generate unique file ID\n */\nexport function generateFileId(): string {\n  return `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n}\n\n/**\n * Get file extension from filename\n */\nexport function getFileExtension(filename: string): string {\n  const lastDot = filename.lastIndexOf('.');\n  return lastDot === -1 ? '' : filename.slice(lastDot + 1).toLowerCase();\n}\n\n/**\n * Check if file is a markdown file\n */\nexport function isMarkdownFile(filename: string): boolean {\n  const extension = getFileExtension(filename);\n  return ['md', 'markdown', 'mdown', 'mkd', 'mdx'].includes(extension);\n}\n\n/**\n * Validate filename\n */\nexport function validateFilename(filename: string): {\n  isValid: boolean;\n  error?: string;\n} {\n  if (!filename.trim()) {\n    return { isValid: false, error: 'Filename cannot be empty' };\n  }\n\n  if (filename.length > 255) {\n    return { isValid: false, error: 'Filename is too long (max 255 characters)' };\n  }\n\n  // Check for invalid characters\n  const invalidChars = /[<>:\"/\\\\|?*\\x00-\\x1f]/;\n  if (invalidChars.test(filename)) {\n    return { isValid: false, error: 'Filename contains invalid characters' };\n  }\n\n  // Check for reserved names (Windows)\n  const reservedNames = [\n    'CON', 'PRN', 'AUX', 'NUL',\n    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',\n    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'\n  ];\n\n  const nameWithoutExt = filename.split('.')[0].toUpperCase();\n  if (reservedNames.includes(nameWithoutExt)) {\n    return { isValid: false, error: 'Filename is reserved by the system' };\n  }\n\n  return { isValid: true };\n}\n\n/**\n * Format file size for display\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 B';\n\n  const k = 1024;\n  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;\n}\n\n/**\n * Format date for display\n */\nexport function formatDate(date: Date): string {\n  const now = new Date();\n  const diffMs = now.getTime() - date.getTime();\n  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n  if (diffDays === 0) {\n    return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n  } else if (diffDays === 1) {\n    return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n  } else if (diffDays < 7) {\n    return `${diffDays} days ago`;\n  } else {\n    return date.toLocaleDateString();\n  }\n}\n\n/**\n * Create a new markdown file\n */\nexport function createMarkdownFile(\n  name: string,\n  content: string = '',\n  parentId?: string\n): MarkdownFile {\n  const validation = validateFilename(name);\n  if (!validation.isValid) {\n    throw new FileSystemError(validation.error!, 'create');\n  }\n\n  // Ensure .md extension\n  const filename = name.endsWith('.md') ? name : `${name}.md`;\n\n  return {\n    id: generateFileId(),\n    name: filename,\n    content,\n    path: parentId ? `${parentId}/${filename}` : filename,\n    lastModified: new Date(),\n    size: new Blob([content]).size,\n    isDirectory: false,\n    parentId\n  };\n}\n\n/**\n * Create a new directory\n */\nexport function createDirectory(name: string, parentId?: string): MarkdownFile {\n  const validation = validateFilename(name);\n  if (!validation.isValid) {\n    throw new FileSystemError(validation.error!, 'create');\n  }\n\n  return {\n    id: generateFileId(),\n    name,\n    content: '',\n    path: parentId ? `${parentId}/${name}` : name,\n    lastModified: new Date(),\n    size: 0,\n    isDirectory: true,\n    parentId\n  };\n}\n\n/**\n * Local storage utilities\n */\nexport const localStorage = {\n  /**\n   * Save files to local storage\n   */\n  saveFiles(files: MarkdownFile[]): void {\n    try {\n      const serialized = JSON.stringify(files, (key, value) => {\n        if (key === 'lastModified' && value instanceof Date) {\n          return value.toISOString();\n        }\n        return value;\n      });\n      window.localStorage.setItem('mdeditor_files', serialized);\n    } catch (error) {\n      console.error('Failed to save files to localStorage:', error);\n      throw new FileSystemError('Failed to save files', 'save');\n    }\n  },\n\n  /**\n   * Load files from local storage\n   */\n  loadFiles(): MarkdownFile[] {\n    try {\n      const stored = window.localStorage.getItem('mdeditor_files');\n      if (!stored) return [];\n\n      const parsed = JSON.parse(stored);\n      return parsed.map((file: any) => ({\n        ...file,\n        lastModified: new Date(file.lastModified)\n      }));\n    } catch (error) {\n      console.error('Failed to load files from localStorage:', error);\n      return [];\n    }\n  },\n\n  /**\n   * Save editor settings\n   */\n  saveSettings(settings: any): void {\n    try {\n      window.localStorage.setItem('mdeditor_settings', JSON.stringify(settings));\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n    }\n  },\n\n  /**\n   * Load editor settings\n   */\n  loadSettings(): any {\n    try {\n      const stored = window.localStorage.getItem('mdeditor_settings');\n      return stored ? JSON.parse(stored) : null;\n    } catch (error) {\n      console.error('Failed to load settings:', error);\n      return null;\n    }\n  },\n\n  /**\n   * Clear all stored data\n   */\n  clear(): void {\n    try {\n      window.localStorage.removeItem('mdeditor_files');\n      window.localStorage.removeItem('mdeditor_settings');\n    } catch (error) {\n      console.error('Failed to clear localStorage:', error);\n    }\n  }\n};\n\n/**\n * File download utilities\n */\nexport function downloadFile(content: string, filename: string, mimeType: string = 'text/plain'): void {\n  try {\n    const blob = new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.style.display = 'none';\n\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // Clean up the URL object\n    setTimeout(() => URL.revokeObjectURL(url), 100);\n  } catch (error) {\n    console.error('Failed to download file:', error);\n    throw new FileSystemError('Failed to download file', 'download');\n  }\n}\n\n/**\n * Read file from input element\n */\nexport function readFileFromInput(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    if (!isMarkdownFile(file.name)) {\n      reject(new FileSystemError('File is not a markdown file', 'read'));\n      return;\n    }\n\n    const reader = new FileReader();\n\n    reader.onload = (event) => {\n      const content = event.target?.result as string;\n      resolve(content);\n    };\n\n    reader.onerror = () => {\n      reject(new FileSystemError('Failed to read file', 'read'));\n    };\n\n    reader.readAsText(file);\n  });\n}\n\n/**\n * Debounce function for auto-save\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Throttle function for performance optimization\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;AAED;;AAKO,SAAS;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;AAC5E;AAKO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,UAAU,SAAS,WAAW,CAAC;IACrC,OAAO,YAAY,CAAC,IAAI,KAAK,SAAS,KAAK,CAAC,UAAU,GAAG,WAAW;AACtE;AAKO,SAAS,eAAe,QAAgB;IAC7C,MAAM,YAAY,iBAAiB;IACnC,OAAO;QAAC;QAAM;QAAY;QAAS;QAAO;KAAM,CAAC,QAAQ,CAAC;AAC5D;AAKO,SAAS,iBAAiB,QAAgB;IAI/C,IAAI,CAAC,SAAS,IAAI,IAAI;QACpB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,+BAA+B;IAC/B,MAAM,eAAe;IACrB,IAAI,aAAa,IAAI,CAAC,WAAW;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB;QAAO;QAAO;QAAO;QACrB;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAChE;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;QAAQ;KACjE;IAED,MAAM,iBAAiB,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;IACzD,IAAI,cAAc,QAAQ,CAAC,iBAAiB;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAK;QAAM;QAAM;QAAM;KAAK;IAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,GAAG,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;AACzE;AAKO,SAAS,WAAW,IAAU;IACnC,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;IAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;IAEzD,IAAI,aAAa,GAAG;QAClB,OAAO,CAAC,SAAS,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU,IAAI;IAC1F,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,CAAC,aAAa,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU,IAAI;IAC9F,OAAO,IAAI,WAAW,GAAG;QACvB,OAAO,GAAG,SAAS,SAAS,CAAC;IAC/B,OAAO;QACL,OAAO,KAAK,kBAAkB;IAChC;AACF;AAKO,SAAS,mBACd,IAAY,EACZ,UAAkB,EAAE,EACpB,QAAiB;IAEjB,MAAM,aAAa,iBAAiB;IACpC,IAAI,CAAC,WAAW,OAAO,EAAE;QACvB,MAAM,IAAI,qHAAA,CAAA,kBAAe,CAAC,WAAW,KAAK,EAAG;IAC/C;IAEA,uBAAuB;IACvB,MAAM,WAAW,KAAK,QAAQ,CAAC,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC;IAE3D,OAAO;QACL,IAAI;QACJ,MAAM;QACN;QACA,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,UAAU,GAAG;QAC7C,cAAc,IAAI;QAClB,MAAM,IAAI,KAAK;YAAC;SAAQ,EAAE,IAAI;QAC9B,aAAa;QACb;IACF;AACF;AAKO,SAAS,gBAAgB,IAAY,EAAE,QAAiB;IAC7D,MAAM,aAAa,iBAAiB;IACpC,IAAI,CAAC,WAAW,OAAO,EAAE;QACvB,MAAM,IAAI,qHAAA,CAAA,kBAAe,CAAC,WAAW,KAAK,EAAG;IAC/C;IAEA,OAAO;QACL,IAAI;QACJ;QACA,SAAS;QACT,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,MAAM,GAAG;QACzC,cAAc,IAAI;QAClB,MAAM;QACN,aAAa;QACb;IACF;AACF;AAKO,MAAM,eAAe;IAC1B;;GAEC,GACD,WAAU,KAAqB;QAC7B,IAAI;YACF,MAAM,aAAa,KAAK,SAAS,CAAC,OAAO,CAAC,KAAK;gBAC7C,IAAI,QAAQ,kBAAkB,iBAAiB,MAAM;oBACnD,OAAO,MAAM,WAAW;gBAC1B;gBACA,OAAO;YACT;YACA,OAAO,YAAY,CAAC,OAAO,CAAC,kBAAkB;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,qHAAA,CAAA,kBAAe,CAAC,wBAAwB;QACpD;IACF;IAEA;;GAEC,GACD;QACE,IAAI;YACF,MAAM,SAAS,OAAO,YAAY,CAAC,OAAO,CAAC;YAC3C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO,OAAO,GAAG,CAAC,CAAC,OAAc,CAAC;oBAChC,GAAG,IAAI;oBACP,cAAc,IAAI,KAAK,KAAK,YAAY;gBAC1C,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,cAAa,QAAa;QACxB,IAAI;YACF,OAAO,YAAY,CAAC,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA;;GAEC,GACD;QACE,IAAI;YACF,MAAM,SAAS,OAAO,YAAY,CAAC,OAAO,CAAC;YAC3C,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA;;GAEC,GACD;QACE,IAAI;YACF,OAAO,YAAY,CAAC,UAAU,CAAC;YAC/B,OAAO,YAAY,CAAC,UAAU,CAAC;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;AACF;AAKO,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,WAAmB,YAAY;IAC7F,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QAClD,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK,CAAC,OAAO,GAAG;QAErB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,0BAA0B;QAC1B,WAAW,IAAM,IAAI,eAAe,CAAC,MAAM;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,qHAAA,CAAA,kBAAe,CAAC,2BAA2B;IACvD;AACF;AAKO,SAAS,kBAAkB,IAAU;IAC1C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,eAAe,KAAK,IAAI,GAAG;YAC9B,OAAO,IAAI,qHAAA,CAAA,kBAAe,CAAC,+BAA+B;YAC1D;QACF;QAEA,MAAM,SAAS,IAAI;QAEnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,MAAM,MAAM,EAAE;YAC9B,QAAQ;QACV;QAEA,OAAO,OAAO,GAAG;YACf,OAAO,IAAI,qHAAA,CAAA,kBAAe,CAAC,uBAAuB;QACpD;QAEA,OAAO,UAAU,CAAC;IACpB;AACF;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/utils/markdown.ts"], "sourcesContent": ["/**\n * Markdown processing utilities\n */\n\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\n\n// Configure marked with custom renderer\nconst renderer = new marked.Renderer();\n\n// Custom heading renderer with anchor links\nrenderer.heading = function(text: string, level: number) {\n  // Ensure text is a string and handle edge cases\n  const textStr = typeof text === 'string' ? text : String(text || '');\n  // Create a valid CSS selector ID\n  const escapedText = textStr\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters except spaces and hyphens\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    || 'heading'; // Fallback if empty\n\n  return `\n    <h${level} id=\"${escapedText}\">\n      <a href=\"#${escapedText}\" class=\"anchor-link\">#</a>\n      ${textStr}\n    </h${level}>\n  `;\n};\n\n// Custom code block renderer with syntax highlighting support\nrenderer.code = function(code: string, language?: string) {\n  const codeStr = typeof code === 'string' ? code : String(code || '');\n  const langStr = typeof language === 'string' ? language : String(language || '');\n  const validLanguage = langStr && langStr.match(/^[a-zA-Z0-9_+-]*$/);\n  const langClass = validLanguage ? `language-${langStr}` : '';\n\n  return `\n    <div class=\"code-block-wrapper\">\n      <div class=\"code-block-header\">\n        <span class=\"code-language\">${langStr || 'text'}</span>\n        <button class=\"copy-code-btn\" data-code=\"${encodeURIComponent(codeStr)}\">\n          Copy\n        </button>\n      </div>\n      <pre><code class=\"${langClass}\">${codeStr}</code></pre>\n    </div>\n  `;\n};\n\n// Custom table renderer with responsive wrapper\nrenderer.table = function(header: string, body: string) {\n  const headerStr = typeof header === 'string' ? header : String(header || '');\n  const bodyStr = typeof body === 'string' ? body : String(body || '');\n\n  return `\n    <div class=\"table-wrapper\">\n      <table class=\"markdown-table\">\n        <thead>${headerStr}</thead>\n        <tbody>${bodyStr}</tbody>\n      </table>\n    </div>\n  `;\n};\n\n// Custom link renderer with external link handling\nrenderer.link = function(href: string, title: string | null, text: string) {\n  const hrefStr = typeof href === 'string' ? href : String(href || '');\n  const textStr = typeof text === 'string' ? text : String(text || '');\n  const titleStr = title ? String(title) : null;\n\n  const isExternal = hrefStr.startsWith('http') || hrefStr.startsWith('//');\n  const target = isExternal ? 'target=\"_blank\" rel=\"noopener noreferrer\"' : '';\n  const titleAttr = titleStr ? `title=\"${titleStr}\"` : '';\n\n  return `<a href=\"${hrefStr}\" ${target} ${titleAttr}>${textStr}</a>`;\n};\n\n// Configure marked options\nmarked.setOptions({\n  renderer,\n  gfm: true, // GitHub Flavored Markdown\n  breaks: true, // Convert \\n to <br>\n  pedantic: false,\n  sanitize: false, // We'll use DOMPurify instead\n  smartLists: true,\n  smartypants: true, // Use smart quotes\n});\n\n/**\n * Convert markdown to sanitized HTML\n */\nexport function markdownToHtml(markdown: string): string {\n  try {\n    // Convert markdown to HTML\n    const rawHtml = marked(markdown);\n\n    // Sanitize HTML to prevent XSS attacks\n    const cleanHtml = DOMPurify.sanitize(rawHtml, {\n      ALLOWED_TAGS: [\n        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',\n        'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins',\n        'a', 'img', 'video', 'audio',\n        'ul', 'ol', 'li',\n        'blockquote', 'pre', 'code',\n        'table', 'thead', 'tbody', 'tr', 'th', 'td',\n        'div', 'span',\n        'hr',\n        'details', 'summary'\n      ],\n      ALLOWED_ATTR: [\n        'href', 'title', 'alt', 'src', 'width', 'height',\n        'class', 'id', 'data-*',\n        'target', 'rel',\n        'controls', 'autoplay', 'loop', 'muted'\n      ],\n      ALLOW_DATA_ATTR: true,\n    });\n\n    return cleanHtml;\n  } catch (error) {\n    console.error('Error converting markdown to HTML:', error);\n    return '<p>Error rendering markdown content</p>';\n  }\n}\n\n/**\n * Extract headings from markdown for table of contents\n */\nexport function extractHeadings(markdown: string): Array<{\n  level: number;\n  text: string;\n  id: string;\n}> {\n  const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n  const headings: Array<{ level: number; text: string; id: string }> = [];\n\n  let match;\n  while ((match = headingRegex.exec(markdown)) !== null) {\n    const level = match[1].length;\n    const text = match[2].trim();\n    const id = text.toLowerCase().replace(/[^\\w]+/g, '-');\n\n    headings.push({ level, text, id });\n  }\n\n  return headings;\n}\n\n/**\n * Get word count from markdown\n */\nexport function getWordCount(markdown: string): {\n  words: number;\n  characters: number;\n  charactersNoSpaces: number;\n  paragraphs: number;\n  readingTime: number; // in minutes\n} {\n  // Remove markdown syntax for accurate word count\n  const plainText = markdown\n    .replace(/#{1,6}\\s+/g, '') // Remove headers\n    .replace(/\\*\\*(.*?)\\*\\*/g, '$1') // Remove bold\n    .replace(/\\*(.*?)\\*/g, '$1') // Remove italic\n    .replace(/`(.*?)`/g, '$1') // Remove inline code\n    .replace(/```[\\s\\S]*?```/g, '') // Remove code blocks\n    .replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1') // Remove links, keep text\n    .replace(/!\\[([^\\]]*)\\]\\([^)]+\\)/g, '') // Remove images\n    .replace(/^\\s*[-*+]\\s+/gm, '') // Remove list markers\n    .replace(/^\\s*\\d+\\.\\s+/gm, '') // Remove numbered list markers\n    .replace(/^\\s*>\\s+/gm, '') // Remove blockquotes\n    .replace(/^\\s*\\|.*\\|$/gm, '') // Remove tables\n    .replace(/^\\s*[-=]{3,}$/gm, '') // Remove horizontal rules\n    .trim();\n\n  const words = plainText.split(/\\s+/).filter(word => word.length > 0).length;\n  const characters = plainText.length;\n  const charactersNoSpaces = plainText.replace(/\\s/g, '').length;\n  const paragraphs = plainText.split(/\\n\\s*\\n/).filter(p => p.trim().length > 0).length;\n\n  // Average reading speed: 200 words per minute\n  const readingTime = Math.ceil(words / 200);\n\n  return {\n    words,\n    characters,\n    charactersNoSpaces,\n    paragraphs,\n    readingTime\n  };\n}\n\n/**\n * Validate markdown syntax\n */\nexport function validateMarkdown(markdown: string): {\n  isValid: boolean;\n  errors: Array<{\n    line: number;\n    message: string;\n    type: 'warning' | 'error';\n  }>;\n} {\n  const errors: Array<{ line: number; message: string; type: 'warning' | 'error' }> = [];\n  const lines = markdown.split('\\n');\n\n  lines.forEach((line, index) => {\n    const lineNumber = index + 1;\n\n    // Check for unmatched brackets\n    const openBrackets = (line.match(/\\[/g) || []).length;\n    const closeBrackets = (line.match(/\\]/g) || []).length;\n    if (openBrackets !== closeBrackets) {\n      errors.push({\n        line: lineNumber,\n        message: 'Unmatched square brackets',\n        type: 'warning'\n      });\n    }\n\n    // Check for unmatched parentheses in links\n    const openParens = (line.match(/\\(/g) || []).length;\n    const closeParens = (line.match(/\\)/g) || []).length;\n    if (openParens !== closeParens) {\n      errors.push({\n        line: lineNumber,\n        message: 'Unmatched parentheses',\n        type: 'warning'\n      });\n    }\n\n    // Check for malformed links\n    const linkRegex = /\\[([^\\]]*)\\]\\(([^)]*)\\)/g;\n    let linkMatch;\n    while ((linkMatch = linkRegex.exec(line)) !== null) {\n      const linkText = linkMatch[1];\n      const linkUrl = linkMatch[2];\n\n      if (!linkText.trim()) {\n        errors.push({\n          line: lineNumber,\n          message: 'Link has empty text',\n          type: 'warning'\n        });\n      }\n\n      if (!linkUrl.trim()) {\n        errors.push({\n          line: lineNumber,\n          message: 'Link has empty URL',\n          type: 'error'\n        });\n      }\n    }\n\n    // Check for malformed images\n    const imageRegex = /!\\[([^\\]]*)\\]\\(([^)]*)\\)/g;\n    let imageMatch;\n    while ((imageMatch = imageRegex.exec(line)) !== null) {\n      const altText = imageMatch[1];\n      const imageUrl = imageMatch[2];\n\n      if (!imageUrl.trim()) {\n        errors.push({\n          line: lineNumber,\n          message: 'Image has empty URL',\n          type: 'error'\n        });\n      }\n    }\n  });\n\n  return {\n    isValid: errors.filter(e => e.type === 'error').length === 0,\n    errors\n  };\n}\n\n/**\n * Format markdown content\n */\nexport function formatMarkdown(markdown: string): string {\n  return markdown\n    .replace(/\\n{3,}/g, '\\n\\n') // Remove excessive line breaks\n    .replace(/[ \\t]+$/gm, '') // Remove trailing whitespace\n    .replace(/^[ \\t]+/gm, (match) => match.replace(/\\t/g, '  ')) // Convert tabs to spaces\n    .trim();\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAED;AACA;;;AAEA,wCAAwC;AACxC,MAAM,WAAW,IAAI,8IAAA,CAAA,SAAM,CAAC,QAAQ;AAEpC,4CAA4C;AAC5C,SAAS,OAAO,GAAG,SAAS,IAAY,EAAE,KAAa;IACrD,gDAAgD;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,QAAQ;IACjE,iCAAiC;IACjC,MAAM,cAAc,QACjB,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,sDAAsD;KAC/E,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,OAAO,CAAC,OAAO,KAAK,8CAA8C;QAChE,WAAW,oBAAoB;IAEpC,OAAO,CAAC;MACJ,EAAE,MAAM,KAAK,EAAE,YAAY;gBACjB,EAAE,YAAY;MACxB,EAAE,QAAQ;OACT,EAAE,MAAM;EACb,CAAC;AACH;AAEA,8DAA8D;AAC9D,SAAS,IAAI,GAAG,SAAS,IAAY,EAAE,QAAiB;IACtD,MAAM,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,QAAQ;IACjE,MAAM,UAAU,OAAO,aAAa,WAAW,WAAW,OAAO,YAAY;IAC7E,MAAM,gBAAgB,WAAW,QAAQ,KAAK,CAAC;IAC/C,MAAM,YAAY,gBAAgB,CAAC,SAAS,EAAE,SAAS,GAAG;IAE1D,OAAO,CAAC;;;oCAG0B,EAAE,WAAW,OAAO;iDACP,EAAE,mBAAmB,SAAS;;;;wBAIvD,EAAE,UAAU,EAAE,EAAE,QAAQ;;EAE9C,CAAC;AACH;AAEA,gDAAgD;AAChD,SAAS,KAAK,GAAG,SAAS,MAAc,EAAE,IAAY;IACpD,MAAM,YAAY,OAAO,WAAW,WAAW,SAAS,OAAO,UAAU;IACzE,MAAM,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,QAAQ;IAEjE,OAAO,CAAC;;;eAGK,EAAE,UAAU;eACZ,EAAE,QAAQ;;;EAGvB,CAAC;AACH;AAEA,mDAAmD;AACnD,SAAS,IAAI,GAAG,SAAS,IAAY,EAAE,KAAoB,EAAE,IAAY;IACvE,MAAM,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,QAAQ;IACjE,MAAM,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,QAAQ;IACjE,MAAM,WAAW,QAAQ,OAAO,SAAS;IAEzC,MAAM,aAAa,QAAQ,UAAU,CAAC,WAAW,QAAQ,UAAU,CAAC;IACpE,MAAM,SAAS,aAAa,8CAA8C;IAC1E,MAAM,YAAY,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,GAAG;IAErD,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI,CAAC;AACrE;AAEA,2BAA2B;AAC3B,8IAAA,CAAA,SAAM,CAAC,UAAU,CAAC;IAChB;IACA,KAAK;IACL,QAAQ;IACR,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;AACf;AAKO,SAAS,eAAe,QAAgB;IAC7C,IAAI;QACF,2BAA2B;QAC3B,MAAM,UAAU,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;QAEvB,uCAAuC;QACvC,MAAM,YAAY,kJAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,SAAS;YAC5C,cAAc;gBACZ;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC9B;gBAAK;gBAAM;gBAAU;gBAAM;gBAAK;gBAAK;gBAAO;gBAC5C;gBAAK;gBAAO;gBAAS;gBACrB;gBAAM;gBAAM;gBACZ;gBAAc;gBAAO;gBACrB;gBAAS;gBAAS;gBAAS;gBAAM;gBAAM;gBACvC;gBAAO;gBACP;gBACA;gBAAW;aACZ;YACD,cAAc;gBACZ;gBAAQ;gBAAS;gBAAO;gBAAO;gBAAS;gBACxC;gBAAS;gBAAM;gBACf;gBAAU;gBACV;gBAAY;gBAAY;gBAAQ;aACjC;YACD,iBAAiB;QACnB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,QAAgB;IAK9C,MAAM,eAAe;IACrB,MAAM,WAA+D,EAAE;IAEvE,IAAI;IACJ,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,SAAS,MAAM,KAAM;QACrD,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;QAC7B,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1B,MAAM,KAAK,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW;QAEjD,SAAS,IAAI,CAAC;YAAE;YAAO;YAAM;QAAG;IAClC;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,QAAgB;IAO3C,iDAAiD;IACjD,MAAM,YAAY,SACf,OAAO,CAAC,cAAc,IAAI,iBAAiB;KAC3C,OAAO,CAAC,kBAAkB,MAAM,cAAc;KAC9C,OAAO,CAAC,cAAc,MAAM,gBAAgB;KAC5C,OAAO,CAAC,YAAY,MAAM,qBAAqB;KAC/C,OAAO,CAAC,mBAAmB,IAAI,qBAAqB;KACpD,OAAO,CAAC,0BAA0B,MAAM,0BAA0B;KAClE,OAAO,CAAC,2BAA2B,IAAI,gBAAgB;KACvD,OAAO,CAAC,kBAAkB,IAAI,sBAAsB;KACpD,OAAO,CAAC,kBAAkB,IAAI,+BAA+B;KAC7D,OAAO,CAAC,cAAc,IAAI,qBAAqB;KAC/C,OAAO,CAAC,iBAAiB,IAAI,gBAAgB;KAC7C,OAAO,CAAC,mBAAmB,IAAI,0BAA0B;KACzD,IAAI;IAEP,MAAM,QAAQ,UAAU,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC3E,MAAM,aAAa,UAAU,MAAM;IACnC,MAAM,qBAAqB,UAAU,OAAO,CAAC,OAAO,IAAI,MAAM;IAC9D,MAAM,aAAa,UAAU,KAAK,CAAC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM;IAErF,8CAA8C;IAC9C,MAAM,cAAc,KAAK,IAAI,CAAC,QAAQ;IAEtC,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,iBAAiB,QAAgB;IAQ/C,MAAM,SAA8E,EAAE;IACtF,MAAM,QAAQ,SAAS,KAAK,CAAC;IAE7B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,QAAQ;QAE3B,+BAA+B;QAC/B,MAAM,eAAe,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;QACrD,MAAM,gBAAgB,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;QACtD,IAAI,iBAAiB,eAAe;YAClC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,MAAM;YACR;QACF;QAEA,2CAA2C;QAC3C,MAAM,aAAa,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;QACnD,MAAM,cAAc,CAAC,KAAK,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;QACpD,IAAI,eAAe,aAAa;YAC9B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,SAAS;gBACT,MAAM;YACR;QACF;QAEA,4BAA4B;QAC5B,MAAM,YAAY;QAClB,IAAI;QACJ,MAAO,CAAC,YAAY,UAAU,IAAI,CAAC,KAAK,MAAM,KAAM;YAClD,MAAM,WAAW,SAAS,CAAC,EAAE;YAC7B,MAAM,UAAU,SAAS,CAAC,EAAE;YAE5B,IAAI,CAAC,SAAS,IAAI,IAAI;gBACpB,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,SAAS;oBACT,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,QAAQ,IAAI,IAAI;gBACnB,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,SAAS;oBACT,MAAM;gBACR;YACF;QACF;QAEA,6BAA6B;QAC7B,MAAM,aAAa;QACnB,IAAI;QACJ,MAAO,CAAC,aAAa,WAAW,IAAI,CAAC,KAAK,MAAM,KAAM;YACpD,MAAM,UAAU,UAAU,CAAC,EAAE;YAC7B,MAAM,WAAW,UAAU,CAAC,EAAE;YAE9B,IAAI,CAAC,SAAS,IAAI,IAAI;gBACpB,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,SAAS;oBACT,MAAM;gBACR;YACF;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM,KAAK;QAC3D;IACF;AACF;AAKO,SAAS,eAAe,QAAgB;IAC7C,OAAO,SACJ,OAAO,CAAC,WAAW,QAAQ,+BAA+B;KAC1D,OAAO,CAAC,aAAa,IAAI,6BAA6B;KACtD,OAAO,CAAC,aAAa,CAAC,QAAU,MAAM,OAAO,CAAC,OAAO,OAAO,yBAAyB;KACrF,IAAI;AACT", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/src/contexts/AppContext.tsx"], "sourcesContent": ["/**\n * Main application context for state management\n */\n\n'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport {\n  AppState,\n  EditorAction,\n  FileManagerAction,\n  PreviewAction,\n  SettingsAction,\n  EditorSettings,\n  MarkdownFile\n} from '@/types';\nimport { localStorage } from '@/utils/file';\nimport { markdownToHtml } from '@/utils/markdown';\n\n\n// Initial state\nconst initialState: AppState = {\n  editor: {\n    currentFile: null,\n    content: '',\n    isModified: false,\n    isLoading: false,\n    error: null,\n    cursorPosition: { line: 1, column: 1 }\n  },\n  preview: {\n    htmlContent: '',\n    isLoading: false,\n    error: null,\n    scrollPosition: 0\n  },\n  fileManager: {\n    currentDirectory: '/',\n    selectedFiles: [],\n    files: [],\n    isLoading: false,\n    error: null\n  },\n  settings: {\n    theme: 'light',\n    fontSize: 14,\n    lineNumbers: true,\n    wordWrap: true,\n    previewMode: 'side',\n    autoSave: true,\n    autoSaveInterval: 30000 // 30 seconds\n  },\n  collaboration: {\n    isEnabled: false,\n    users: [],\n    roomId: null,\n    isConnected: false\n  },\n  cloudProviders: []\n};\n\n// Action types\ntype AppAction =\n  | { type: 'EDITOR_ACTION'; payload: EditorAction }\n  | { type: 'FILE_MANAGER_ACTION'; payload: FileManagerAction }\n  | { type: 'PREVIEW_ACTION'; payload: PreviewAction }\n  | { type: 'SETTINGS_ACTION'; payload: SettingsAction }\n  | { type: 'LOAD_INITIAL_DATA' }\n  | { type: 'RESET_STATE' };\n\n// Reducers\nfunction editorReducer(state: AppState['editor'], action: EditorAction): AppState['editor'] {\n  switch (action.type) {\n    case 'SET_CONTENT':\n      return { ...state, content: action.payload, isModified: true };\n    case 'SET_CURRENT_FILE':\n      return {\n        ...state,\n        currentFile: action.payload,\n        content: action.payload?.content || '',\n        isModified: false\n      };\n    case 'SET_MODIFIED':\n      return { ...state, isModified: action.payload };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'SET_ERROR':\n      return { ...state, error: action.payload };\n    case 'SET_CURSOR_POSITION':\n      return { ...state, cursorPosition: action.payload };\n    default:\n      return state;\n  }\n}\n\nfunction fileManagerReducer(\n  state: AppState['fileManager'],\n  action: FileManagerAction\n): AppState['fileManager'] {\n  switch (action.type) {\n    case 'SET_FILES':\n      return { ...state, files: action.payload };\n    case 'ADD_FILE':\n      return { ...state, files: [...state.files, action.payload] };\n    case 'UPDATE_FILE':\n      return {\n        ...state,\n        files: state.files.map(file =>\n          file.id === action.payload.id ? action.payload : file\n        )\n      };\n    case 'DELETE_FILE':\n      return {\n        ...state,\n        files: state.files.filter(file => file.id !== action.payload)\n      };\n    case 'SET_CURRENT_DIRECTORY':\n      return { ...state, currentDirectory: action.payload };\n    case 'SET_SELECTED_FILES':\n      return { ...state, selectedFiles: action.payload };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'SET_ERROR':\n      return { ...state, error: action.payload };\n    default:\n      return state;\n  }\n}\n\nfunction previewReducer(state: AppState['preview'], action: PreviewAction): AppState['preview'] {\n  switch (action.type) {\n    case 'SET_HTML_CONTENT':\n      return { ...state, htmlContent: action.payload };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'SET_ERROR':\n      return { ...state, error: action.payload };\n    case 'SET_SCROLL_POSITION':\n      return { ...state, scrollPosition: action.payload };\n    default:\n      return state;\n  }\n}\n\nfunction settingsReducer(state: AppState['settings'], action: SettingsAction): AppState['settings'] {\n  switch (action.type) {\n    case 'SET_THEME':\n      return { ...state, theme: action.payload };\n    case 'SET_FONT_SIZE':\n      return { ...state, fontSize: action.payload };\n    case 'SET_LINE_NUMBERS':\n      return { ...state, lineNumbers: action.payload };\n    case 'SET_WORD_WRAP':\n      return { ...state, wordWrap: action.payload };\n    case 'SET_PREVIEW_MODE':\n      return { ...state, previewMode: action.payload };\n    case 'SET_AUTO_SAVE':\n      return { ...state, autoSave: action.payload };\n    case 'SET_AUTO_SAVE_INTERVAL':\n      return { ...state, autoSaveInterval: action.payload };\n    default:\n      return state;\n  }\n}\n\n// Main reducer\nfunction appReducer(state: AppState, action: AppAction): AppState {\n  switch (action.type) {\n    case 'EDITOR_ACTION':\n      return { ...state, editor: editorReducer(state.editor, action.payload) };\n    case 'FILE_MANAGER_ACTION':\n      return { ...state, fileManager: fileManagerReducer(state.fileManager, action.payload) };\n    case 'PREVIEW_ACTION':\n      return { ...state, preview: previewReducer(state.preview, action.payload) };\n    case 'SETTINGS_ACTION':\n      const newSettings = settingsReducer(state.settings, action.payload);\n      // Save settings to localStorage\n      localStorage.saveSettings(newSettings);\n      return { ...state, settings: newSettings };\n    case 'LOAD_INITIAL_DATA':\n      const savedFiles = localStorage.loadFiles();\n      const savedSettings = localStorage.loadSettings();\n      return {\n        ...state,\n        fileManager: { ...state.fileManager, files: savedFiles },\n        settings: savedSettings ? { ...state.settings, ...savedSettings } : state.settings\n      };\n    case 'RESET_STATE':\n      localStorage.clear();\n      return initialState;\n    default:\n      return state;\n  }\n}\n\n// Context\ninterface AppContextType {\n  state: AppState;\n  dispatch: React.Dispatch<AppAction>;\n\n  // Helper functions\n  updateContent: (content: string) => void;\n  openFile: (file: MarkdownFile) => void;\n  saveCurrentFile: () => void;\n  createNewFile: (name: string, content?: string) => void;\n  deleteFile: (fileId: string) => void;\n  updatePreview: (content: string) => void;\n  updateSettings: (settings: Partial<EditorSettings>) => void;\n}\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\n// Provider component\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\nexport function AppProvider({ children }: AppProviderProps) {\n  const [state, dispatch] = useReducer(appReducer, initialState);\n\n  // Load initial data on mount\n  useEffect(() => {\n    dispatch({ type: 'LOAD_INITIAL_DATA' });\n  }, []);\n\n  // Handle theme changes\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const root = document.documentElement;\n      const body = document.body;\n\n      if (state.settings.theme === 'dark') {\n        root.classList.add('dark');\n        body.classList.add('dark');\n      } else {\n        root.classList.remove('dark');\n        body.classList.remove('dark');\n      }\n\n      // Force a repaint\n      root.style.colorScheme = state.settings.theme;\n    }\n  }, [state.settings.theme]);\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (!state.settings.autoSave || !state.editor.isModified || !state.editor.currentFile) {\n      return;\n    }\n\n    const autoSaveTimer = setTimeout(() => {\n      saveCurrentFile();\n    }, state.settings.autoSaveInterval);\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [state.editor.content, state.editor.isModified, state.settings.autoSave, state.settings.autoSaveInterval]);\n\n  // Update preview when content changes\n  useEffect(() => {\n    if (state.editor.content) {\n      updatePreview(state.editor.content);\n    }\n  }, [state.editor.content]);\n\n  // Helper functions\n  const updateContent = (content: string) => {\n    dispatch({\n      type: 'EDITOR_ACTION',\n      payload: { type: 'SET_CONTENT', payload: content }\n    });\n  };\n\n  const openFile = (file: MarkdownFile) => {\n    dispatch({\n      type: 'EDITOR_ACTION',\n      payload: { type: 'SET_CURRENT_FILE', payload: file }\n    });\n  };\n\n  const saveCurrentFile = () => {\n    if (!state.editor.currentFile) return;\n\n    const updatedFile: MarkdownFile = {\n      ...state.editor.currentFile,\n      content: state.editor.content,\n      lastModified: new Date(),\n      size: new Blob([state.editor.content]).size\n    };\n\n    dispatch({\n      type: 'FILE_MANAGER_ACTION',\n      payload: { type: 'UPDATE_FILE', payload: updatedFile }\n    });\n\n    dispatch({\n      type: 'EDITOR_ACTION',\n      payload: { type: 'SET_MODIFIED', payload: false }\n    });\n\n    // Save to localStorage\n    const updatedFiles = state.fileManager.files.map(file =>\n      file.id === updatedFile.id ? updatedFile : file\n    );\n    localStorage.saveFiles(updatedFiles);\n  };\n\n  const createNewFile = (name: string, content: string = '') => {\n    const newFile: MarkdownFile = {\n      id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,\n      name: name.endsWith('.md') ? name : `${name}.md`,\n      content,\n      path: name,\n      lastModified: new Date(),\n      size: new Blob([content]).size,\n      isDirectory: false\n    };\n\n    dispatch({\n      type: 'FILE_MANAGER_ACTION',\n      payload: { type: 'ADD_FILE', payload: newFile }\n    });\n\n    // Save to localStorage\n    localStorage.saveFiles([...state.fileManager.files, newFile]);\n\n    // Open the new file\n    openFile(newFile);\n  };\n\n  const deleteFile = (fileId: string) => {\n    dispatch({\n      type: 'FILE_MANAGER_ACTION',\n      payload: { type: 'DELETE_FILE', payload: fileId }\n    });\n\n    // If the deleted file is currently open, close it\n    if (state.editor.currentFile?.id === fileId) {\n      dispatch({\n        type: 'EDITOR_ACTION',\n        payload: { type: 'SET_CURRENT_FILE', payload: null }\n      });\n    }\n\n    // Save to localStorage\n    const updatedFiles = state.fileManager.files.filter(file => file.id !== fileId);\n    localStorage.saveFiles(updatedFiles);\n  };\n\n  const updatePreview = (content: string) => {\n    try {\n      dispatch({\n        type: 'PREVIEW_ACTION',\n        payload: { type: 'SET_LOADING', payload: true }\n      });\n\n      const htmlContent = markdownToHtml(content);\n\n      dispatch({\n        type: 'PREVIEW_ACTION',\n        payload: { type: 'SET_HTML_CONTENT', payload: htmlContent }\n      });\n\n      dispatch({\n        type: 'PREVIEW_ACTION',\n        payload: { type: 'SET_ERROR', payload: null }\n      });\n    } catch (error) {\n      dispatch({\n        type: 'PREVIEW_ACTION',\n        payload: { type: 'SET_ERROR', payload: 'Failed to render preview' }\n      });\n    } finally {\n      dispatch({\n        type: 'PREVIEW_ACTION',\n        payload: { type: 'SET_LOADING', payload: false }\n      });\n    }\n  };\n\n  const updateSettings = (settings: Partial<EditorSettings>) => {\n    Object.entries(settings).forEach(([key, value]) => {\n      switch (key) {\n        case 'theme':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_THEME', payload: value as 'light' | 'dark' }\n          });\n          break;\n        case 'fontSize':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_FONT_SIZE', payload: value as number }\n          });\n          break;\n        case 'lineNumbers':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_LINE_NUMBERS', payload: value as boolean }\n          });\n          break;\n        case 'wordWrap':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_WORD_WRAP', payload: value as boolean }\n          });\n          break;\n        case 'previewMode':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_PREVIEW_MODE', payload: value as 'side' | 'preview' | 'edit' }\n          });\n          break;\n        case 'autoSave':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_AUTO_SAVE', payload: value as boolean }\n          });\n          break;\n        case 'autoSaveInterval':\n          dispatch({\n            type: 'SETTINGS_ACTION',\n            payload: { type: 'SET_AUTO_SAVE_INTERVAL', payload: value as number }\n          });\n          break;\n      }\n    });\n  };\n\n  const contextValue: AppContextType = {\n    state,\n    dispatch,\n    updateContent,\n    openFile,\n    saveCurrentFile,\n    createNewFile,\n    deleteFile,\n    updatePreview,\n    updateSettings\n  };\n\n  return (\n    <AppContext.Provider value={contextValue}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n\n\n// Hook to use the context\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAID;AAUA;AACA;AAbA;;;;;AAgBA,gBAAgB;AAChB,MAAM,eAAyB;IAC7B,QAAQ;QACN,aAAa;QACb,SAAS;QACT,YAAY;QACZ,WAAW;QACX,OAAO;QACP,gBAAgB;YAAE,MAAM;YAAG,QAAQ;QAAE;IACvC;IACA,SAAS;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,gBAAgB;IAClB;IACA,aAAa;QACX,kBAAkB;QAClB,eAAe,EAAE;QACjB,OAAO,EAAE;QACT,WAAW;QACX,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,aAAa;QACb,UAAU;QACV,kBAAkB,MAAM,aAAa;IACvC;IACA,eAAe;QACb,WAAW;QACX,OAAO,EAAE;QACT,QAAQ;QACR,aAAa;IACf;IACA,gBAAgB,EAAE;AACpB;AAWA,WAAW;AACX,SAAS,cAAc,KAAyB,EAAE,MAAoB;IACpE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,OAAO,OAAO;gBAAE,YAAY;YAAK;QAC/D,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa,OAAO,OAAO;gBAC3B,SAAS,OAAO,OAAO,EAAE,WAAW;gBACpC,YAAY;YACd;QACF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,YAAY,OAAO,OAAO;YAAC;QAChD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QACpD;YACE,OAAO;IACX;AACF;AAEA,SAAS,mBACP,KAA8B,EAC9B,MAAyB;IAEzB,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO;uBAAI,MAAM,KAAK;oBAAE,OAAO,OAAO;iBAAC;YAAC;QAC7D,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,GAAG;YAErD;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO;YAC9D;QACF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,kBAAkB,OAAO,OAAO;YAAC;QACtD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,eAAe,OAAO,OAAO;YAAC;QACnD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C;YACE,OAAO;IACX;AACF;AAEA,SAAS,eAAe,KAA0B,EAAE,MAAqB;IACvE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QACjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QACpD;YACE,OAAO;IACX;AACF;AAEA,SAAS,gBAAgB,KAA2B,EAAE,MAAsB;IAC1E,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,OAAO,OAAO;YAAC;QAC9C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QACjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,OAAO,OAAO;YAAC;QAC9C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QACjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU,OAAO,OAAO;YAAC;QAC9C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,kBAAkB,OAAO,OAAO;YAAC;QACtD;YACE,OAAO;IACX;AACF;AAEA,eAAe;AACf,SAAS,WAAW,KAAe,EAAE,MAAiB;IACpD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,QAAQ,cAAc,MAAM,MAAM,EAAE,OAAO,OAAO;YAAE;QACzE,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,mBAAmB,MAAM,WAAW,EAAE,OAAO,OAAO;YAAE;QACxF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS,eAAe,MAAM,OAAO,EAAE,OAAO,OAAO;YAAE;QAC5E,KAAK;YACH,MAAM,cAAc,gBAAgB,MAAM,QAAQ,EAAE,OAAO,OAAO;YAClE,gCAAgC;YAChC,oHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YAC1B,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU;YAAY;QAC3C,KAAK;YACH,MAAM,aAAa,oHAAA,CAAA,eAAY,CAAC,SAAS;YACzC,MAAM,gBAAgB,oHAAA,CAAA,eAAY,CAAC,YAAY;YAC/C,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa;oBAAE,GAAG,MAAM,WAAW;oBAAE,OAAO;gBAAW;gBACvD,UAAU,gBAAgB;oBAAE,GAAG,MAAM,QAAQ;oBAAE,GAAG,aAAa;gBAAC,IAAI,MAAM,QAAQ;YACpF;QACF,KAAK;YACH,oHAAA,CAAA,eAAY,CAAC,KAAK;YAClB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAiBA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA8B;AAOtD,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,YAAY;IAEjD,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YAAE,MAAM;QAAoB;IACvC,GAAG,EAAE;IAEL,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAcnC;IACF,GAAG;QAAC,MAAM,QAAQ,CAAC,KAAK;KAAC;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,IAAI,CAAC,MAAM,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,EAAE;YACrF;QACF;QAEA,MAAM,gBAAgB,WAAW;YAC/B;QACF,GAAG,MAAM,QAAQ,CAAC,gBAAgB;QAElC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,MAAM,MAAM,CAAC,OAAO;QAAE,MAAM,MAAM,CAAC,UAAU;QAAE,MAAM,QAAQ,CAAC,QAAQ;QAAE,MAAM,QAAQ,CAAC,gBAAgB;KAAC;IAE5G,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;YACxB,cAAc,MAAM,MAAM,CAAC,OAAO;QACpC;IACF,GAAG;QAAC,MAAM,MAAM,CAAC,OAAO;KAAC;IAEzB,mBAAmB;IACnB,MAAM,gBAAgB,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAQ;QACnD;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAoB,SAAS;YAAK;QACrD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,MAAM,CAAC,WAAW,EAAE;QAE/B,MAAM,cAA4B;YAChC,GAAG,MAAM,MAAM,CAAC,WAAW;YAC3B,SAAS,MAAM,MAAM,CAAC,OAAO;YAC7B,cAAc,IAAI;YAClB,MAAM,IAAI,KAAK;gBAAC,MAAM,MAAM,CAAC,OAAO;aAAC,EAAE,IAAI;QAC7C;QAEA,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAY;QACvD;QAEA,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAgB,SAAS;YAAM;QAClD;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAC/C,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;QAE7C,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;IACzB;IAEA,MAAM,gBAAgB,CAAC,MAAc,UAAkB,EAAE;QACvD,MAAM,UAAwB;YAC5B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;YACvE,MAAM,KAAK,QAAQ,CAAC,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC;YAChD;YACA,MAAM;YACN,cAAc,IAAI;YAClB,MAAM,IAAI,KAAK;gBAAC;aAAQ,EAAE,IAAI;YAC9B,aAAa;QACf;QAEA,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAQ;QAChD;QAEA,uBAAuB;QACvB,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;eAAI,MAAM,WAAW,CAAC,KAAK;YAAE;SAAQ;QAE5D,oBAAoB;QACpB,SAAS;IACX;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YACP,MAAM;YACN,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAO;QAClD;QAEA,kDAAkD;QAClD,IAAI,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,QAAQ;YAC3C,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAoB,SAAS;gBAAK;YACrD;QACF;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACxE,oHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;IACzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI;YACF,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAK;YAChD;YAEA,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;YAEnC,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAoB,SAAS;gBAAY;YAC5D;YAEA,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAAK;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAa,SAAS;gBAA2B;YACpE;QACF,SAAU;YACR,SAAS;gBACP,MAAM;gBACN,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAM;YACjD;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC5C,OAAQ;gBACN,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAa,SAAS;wBAA0B;oBACnE;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAiB,SAAS;wBAAgB;oBAC7D;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAoB,SAAS;wBAAiB;oBACjE;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAiB,SAAS;wBAAiB;oBAC9D;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAoB,SAAS;wBAAqC;oBACrF;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAAiB,SAAS;wBAAiB;oBAC9D;oBACA;gBACF,KAAK;oBACH,SAAS;wBACP,MAAM;wBACN,SAAS;4BAAE,MAAM;4BAA0B,SAAS;wBAAgB;oBACtE;oBACA;YACJ;QACF;IACF;IAEA,MAAM,eAA+B;QACnC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,WAAW,QAAQ;QAAC,OAAO;kBACzB;;;;;;AAGP;AAKO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}