{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/mdeditor/node_modules/marked/src/defaults.ts", "file:///D:/mdeditor/node_modules/marked/src/rules.ts", "file:///D:/mdeditor/node_modules/marked/src/helpers.ts", "file:///D:/mdeditor/node_modules/marked/src/Tokenizer.ts", "file:///D:/mdeditor/node_modules/marked/src/Lexer.ts", "file:///D:/mdeditor/node_modules/marked/src/Renderer.ts", "file:///D:/mdeditor/node_modules/marked/src/TextRenderer.ts", "file:///D:/mdeditor/node_modules/marked/src/Parser.ts", "file:///D:/mdeditor/node_modules/marked/src/Hooks.ts", "file:///D:/mdeditor/node_modules/marked/src/Instance.ts", "file:///D:/mdeditor/node_modules/marked/src/marked.ts"], "sourcesContent": ["import type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Gets the original marked default options.\n */\nexport function _getDefaults(): MarkedOptions {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null,\n  };\n}\n\nexport let _defaults = _getDefaults();\n\nexport function changeDefaults(newDefaults: MarkedOptions) {\n  _defaults = newDefaults;\n}\n", "const noopTest = { exec: () => null } as unknown as RegExp;\n\nfunction edit(regex: string | RegExp, opt = '') {\n  let source = typeof regex === 'string' ? regex : regex.source;\n  const obj = {\n    replace: (name: string | RegExp, val: string | RegExp) => {\n      let valSource = typeof val === 'string' ? val : val.source;\n      valSource = valSource.replace(other.caret, '$1');\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    },\n  };\n  return obj;\n}\n\nexport const other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull: string) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent: number) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n\n/**\n * Block-Level Grammar\n */\n\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/\\|table/g, '') // table not in commonmark\n  .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n  .replace(/bull/g, bullet) // lists can interrupt\n  .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n  .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n  .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n  .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n  .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n  .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n  .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n  .replace('label', _blockLabel)\n  .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n  .getRegex();\n\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n  .replace(/bull/g, bullet)\n  .getRegex();\n\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n  + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n  + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n  + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n  + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n  + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit(\n  '^ {0,3}(?:' // optional indentation\n+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n+ ')', 'i')\n  .replace('comment', _comment)\n  .replace('tag', _tag)\n  .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst paragraph = edit(_paragraph)\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n  .replace('|table', '')\n  .replace('blockquote', ' {0,3}>')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n  .replace('paragraph', paragraph)\n  .getRegex();\n\n/**\n * Normal Block Grammar\n */\n\nconst blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText,\n};\n\ntype BlockKeys = keyof typeof blockNormal;\n\n/**\n * GFM Block Grammar\n */\n\nconst gfmTable = edit(\n  '^ *([^\\\\n ].*)\\\\n' // Header\n+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n  .replace('hr', hr)\n  .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n  .replace('blockquote', ' {0,3}>')\n  .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n  .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n  .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n  .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n  .getRegex();\n\nconst blockGfm: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('table', gfmTable) // interrupt paragraphs with table\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex(),\n};\n\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\n\nconst blockPedantic: Record<BlockKeys, RegExp> = {\n  ...blockNormal,\n  html: edit(\n    '^ *(?:comment *(?:\\\\n|\\\\s*$)'\n    + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n    + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n    .replace('comment', _comment)\n    .replace(/tag/g, '(?!(?:'\n      + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n      + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n      + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n    .getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest, // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' *#{1,6} *[^\\n]')\n    .replace('lheading', lheading)\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('|fences', '')\n    .replace('|list', '')\n    .replace('|html', '')\n    .replace('|tag', '')\n    .getRegex(),\n};\n\n/**\n * Inline-Level Grammar\n */\n\nconst escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n  .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\n\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\n\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\nconst emStrongRDelimAstCore =\n  '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n+ '|[^*]+(?=[^*])' // Consume to delim\n+ '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n+ '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n+ '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\n\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n  .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n  .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n  .replace(/punct/g, _punctuationGfmStrongEm)\n  .getRegex();\n\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit(\n  '^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n+ '|[^_]+(?=[^_])' // Consume to delim\n+ '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n+ '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n+ '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n+ '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n+ '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n  .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n  .replace(/punctSpace/g, _punctuationOrSpace)\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n  .replace(/punct/g, _punctuation)\n  .getRegex();\n\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n  .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n  .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n  .getRegex();\n\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit(\n  '^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n  .replace('comment', _inlineComment)\n  .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n  .getRegex();\n\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\n\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/)\n  .replace('label', _inlineLabel)\n  .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/)\n  .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n  .getRegex();\n\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n  .replace('label', _inlineLabel)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n  .replace('ref', _blockLabel)\n  .getRegex();\n\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n  .replace('reflink', reflink)\n  .replace('nolink', nolink)\n  .getRegex();\n\n/**\n * Normal Inline Grammar\n */\n\nconst inlineNormal = {\n  _backpedal: noopTest, // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest,\n};\n\ntype InlineKeys = keyof typeof inlineNormal;\n\n/**\n * Pedantic Inline Grammar\n */\n\nconst inlinePedantic: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n    .replace('label', _inlineLabel)\n    .getRegex(),\n};\n\n/**\n * GFM Inline Grammar\n */\n\nconst inlineGfm: Record<InlineKeys, RegExp> = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n    .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n    .getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n\n/**\n * GFM + Line Breaks Inline Grammar\n */\n\nconst inlineBreaks: Record<InlineKeys, RegExp> = {\n  ...inlineGfm,\n  br: edit(br).replace('{2,}', '*').getRegex(),\n  text: edit(inlineGfm.text)\n    .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n    .replace(/\\{2,\\}/g, '*')\n    .getRegex(),\n};\n\n/**\n * exports\n */\n\nexport const block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic,\n};\n\nexport const inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic,\n};\n\nexport interface Rules {\n  other: typeof other\n  block: Record<BlockKeys, RegExp>\n  inline: Record<InlineKeys, RegExp>\n}\n", "import { other } from './rules.ts';\n\n/**\n * Helpers\n */\nconst escapeReplacements: { [index: string]: string } = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch: string) => escapeReplacements[ch];\n\nexport function escape(html: string, encode?: boolean) {\n  if (encode) {\n    if (other.escapeTest.test(html)) {\n      return html.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html)) {\n      return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n\n  return html;\n}\n\nexport function unescape(html: string) {\n  // explicitly match decimal, hex, and named HTML entities\n  return html.replace(other.unescapeTest, (_, n) => {\n    n = n.toLowerCase();\n    if (n === 'colon') return ':';\n    if (n.charAt(0) === '#') {\n      return n.charAt(1) === 'x'\n        ? String.fromCharCode(parseInt(n.substring(2), 16))\n        : String.fromCharCode(+n.substring(1));\n    }\n    return '';\n  });\n}\n\nexport function cleanUrl(href: string) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, '%');\n  } catch {\n    return null;\n  }\n  return href;\n}\n\nexport function splitCells(tableRow: string, count?: number) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push('');\n    }\n  }\n\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n  }\n  return cells;\n}\n\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nexport function rtrim(str: string, c: string, invert?: boolean) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n\n  return str.slice(0, l - suffLen);\n}\n\nexport function findClosingBracket(str: string, b: string) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n\n  return -1;\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  rtrim,\n  splitCells,\n  findClosingBracket,\n} from './helpers.ts';\nimport type { Rules } from './rules.ts';\nimport type { _Lexer } from './Lexer.ts';\nimport type { Links, Tokens, Token } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\nfunction outputLink(cap: string[], link: Pick<Tokens.Link, 'href' | 'title'>, raw: string, lexer: _Lexer, rules: Rules): Tokens.Link | Tokens.Image {\n  const href = link.href;\n  const title = link.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n\n  lexer.state.inLink = true;\n  const token: Tokens.Link | Tokens.Image = {\n    type: cap[0].charAt(0) === '!' ? 'image' : 'link',\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer.inlineTokens(text),\n  };\n  lexer.state.inLink = false;\n  return token;\n}\n\nfunction indentCodeCompensation(raw: string, text: string, rules: Rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n\n  if (matchIndentToCode === null) {\n    return text;\n  }\n\n  const indentToCode = matchIndentToCode[1];\n\n  return text\n    .split('\\n')\n    .map(node => {\n      const matchIndentInNode = node.match(rules.other.beginningSpace);\n      if (matchIndentInNode === null) {\n        return node;\n      }\n\n      const [indentInNode] = matchIndentInNode;\n\n      if (indentInNode.length >= indentToCode.length) {\n        return node.slice(indentToCode.length);\n      }\n\n      return node;\n    })\n    .join('\\n');\n}\n\n/**\n * Tokenizer\n */\nexport class _Tokenizer {\n  options: MarkedOptions;\n  rules!: Rules; // set by the lexer\n  lexer!: _Lexer; // set by the lexer\n\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  space(src: string): Tokens.Space | undefined {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0],\n      };\n    }\n  }\n\n  code(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic\n          ? rtrim(text, '\\n')\n          : text,\n      };\n    }\n  }\n\n  fences(src: string): Tokens.Code | undefined {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n        text,\n      };\n    }\n  }\n\n  heading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n\n      // remove trailing #s\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  hr(src: string): Tokens.Hr | undefined {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: rtrim(cap[0], '\\n'),\n      };\n    }\n  }\n\n  blockquote(src: string): Tokens.Blockquote | undefined {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], '\\n').split('\\n');\n      let raw = '';\n      let text = '';\n      const tokens: Token[] = [];\n\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          // get lines up to a continuation\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n\n        const currentRaw = currentLines.join('\\n');\n        const currentText = currentRaw\n          // precede setext continuation with 4 spaces so it isn't a setext\n          .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n          .replace(this.rules.other.blockquoteSetextReplace2, '');\n        raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\\n${currentText}` : currentText;\n\n        // parse blockquote lines as top level tokens\n        // merge paragraphs if this is a continuation\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n\n        // if there is no continuation then we are done\n        if (lines.length === 0) {\n          break;\n        }\n\n        const lastToken = tokens.at(-1);\n\n        if (lastToken?.type === 'code') {\n          // blockquote continuation cannot be preceded by a code block\n          break;\n        } else if (lastToken?.type === 'blockquote') {\n          // include continuation in nested blockquote\n          const oldToken = lastToken as Tokens.Blockquote;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.blockquote(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === 'list') {\n          // include continuation in nested list\n          const oldToken = lastToken as Tokens.List;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.list(newText)!;\n          tokens[tokens.length - 1] = newToken;\n\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1)!.raw.length).split('\\n');\n          continue;\n        }\n      }\n\n      return {\n        type: 'blockquote',\n        raw,\n        tokens,\n        text,\n      };\n    }\n  }\n\n  list(src: string): Tokens.List | undefined {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n\n      const list: Tokens.List = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: [],\n      };\n\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n\n      // Get next list item\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        let endEarly = false;\n        let raw = '';\n        let itemContents = '';\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n\n        if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n\n        raw = cap[0];\n        src = src.substring(raw.length);\n\n        let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t: string) => ' '.repeat(3 * t.length));\n        let nextLine = src.split('\\n', 1)[0];\n        let blankLine = !line.trim();\n\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n\n          // Check if following lines should be included in List Item\n          while (src) {\n            const rawLine = src.split('\\n', 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n            }\n\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of html block\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n\n            // Horizontal rule found\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n              itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n\n              // paragraph continuation unless last line was a different block level element\n              if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n\n              itemContents += '\\n' + nextLine;\n            }\n\n            if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n              blankLine = true;\n            }\n\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n\n        let istask: RegExpExecArray | null = null;\n        let ischecked: boolean | undefined;\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n          }\n        }\n\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: [],\n        });\n\n        list.raw += raw;\n      }\n\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      const lastItem = list.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        // not a list since there were no items\n        return;\n      }\n      list.raw = list.raw.trimEnd();\n\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (let i = 0; i < list.items.length; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (let i = 0; i < list.items.length; i++) {\n          list.items[i].loose = true;\n        }\n      }\n\n      return list;\n    }\n  }\n\n  html(src: string): Tokens.HTML | undefined {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token: Tokens.HTML = {\n        type: 'html',\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n        text: cap[0],\n      };\n      return token;\n    }\n  }\n\n  def(src: string): Tokens.Def | undefined {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title,\n      };\n    }\n  }\n\n  table(src: string): Tokens.Table | undefined {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n      return;\n    }\n\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n\n    const item: Tokens.Table = {\n      type: 'table',\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: [],\n    };\n\n    if (headers.length !== aligns.length) {\n      // header and align columns must be equal, rows can be different.\n      return;\n    }\n\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push('right');\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push('center');\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push('left');\n      } else {\n        item.align.push(null);\n      }\n    }\n\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i],\n      });\n    }\n\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i],\n        };\n      }));\n    }\n\n    return item;\n  }\n\n  lheading(src: string): Tokens.Heading | undefined {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1]),\n      };\n    }\n  }\n\n  paragraph(src: string): Tokens.Paragraph | undefined {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n        ? cap[1].slice(0, -1)\n        : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text),\n      };\n    }\n  }\n\n  text(src: string): Tokens.Text | undefined {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0]),\n      };\n    }\n  }\n\n  escape(src: string): Tokens.Escape | undefined {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: cap[1],\n      };\n    }\n  }\n\n  tag(src: string): Tokens.Tag | undefined {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n\n      return {\n        type: 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0],\n      };\n    }\n  }\n\n  link(src: string): Tokens.Link | Tokens.Image | undefined {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          return;\n        }\n\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex === -2) {\n          // more open parens than closed\n          return;\n        }\n\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = this.rules.other.pedanticHrefTitle.exec(href);\n\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  reflink(src: string, links: Links): Tokens.Link | Tokens.Image | Tokens.Text | undefined {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src))\n      || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const link = links[linkString.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text,\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer, this.rules);\n    }\n  }\n\n  emStrong(src: string, maskedSrc: string, prevChar = ''): Tokens.Em | Tokens.Strong | undefined {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n\n    const nextChar = match[1] || match[2] || '';\n\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n\n        if (!rDelim) continue; // skip single * in __abc*abc__\n\n        rLength = [...rDelim].length;\n\n        if (match[3] || match[4]) { // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) { // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n\n        delimTotal -= rLength;\n\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        // char length can be >1 for unicode characters;\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text),\n          };\n        }\n\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text),\n        };\n      }\n    }\n  }\n\n  codespan(src: string): Tokens.Codespan | undefined {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text,\n      };\n    }\n  }\n\n  br(src: string): Tokens.Br | undefined {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0],\n      };\n    }\n  }\n\n  del(src: string): Tokens.Del | undefined {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2]),\n      };\n    }\n  }\n\n  autolink(src: string): Tokens.Link | undefined {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[1];\n        href = 'mailto:' + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  url(src: string): Tokens.Link | undefined {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[0];\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: 'text',\n            raw: text,\n            text,\n          },\n        ],\n      };\n    }\n  }\n\n  inlineText(src: string): Tokens.Text | undefined {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        escaped,\n      };\n    }\n  }\n}\n", "import { _Tokenizer } from './Tokenizer.ts';\nimport { _defaults } from './defaults.ts';\nimport { other, block, inline } from './rules.ts';\nimport type { Token, TokensList, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Block Lexer\n */\nexport class _Lexer {\n  tokens: TokensList;\n  options: MarkedOptions;\n  state: {\n    inLink: boolean;\n    inRawBlock: boolean;\n    top: boolean;\n  };\n\n  private tokenizer: _Tokenizer;\n  private inlineQueue: { src: string, tokens: Token[] }[];\n\n  constructor(options?: MarkedOptions) {\n    // TokenList cannot be created in one go\n    this.tokens = [] as unknown as TokensList;\n    this.tokens.links = Object.create(null);\n    this.options = options || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true,\n    };\n\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal,\n    };\n\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline,\n    };\n  }\n\n  /**\n   * Static Lex Method\n   */\n  static lex(src: string, options?: MarkedOptions) {\n    const lexer = new _Lexer(options);\n    return lexer.lex(src);\n  }\n\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src: string, options?: MarkedOptions) {\n    const lexer = new _Lexer(options);\n    return lexer.inlineTokens(src);\n  }\n\n  /**\n   * Preprocessing\n   */\n  lex(src: string) {\n    src = src.replace(other.carriageReturn, '\\n');\n\n    this.blockTokens(src, this.tokens);\n\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n\n    return this.tokens;\n  }\n\n  /**\n   * Lexing\n   */\n  blockTokens(src: string, tokens?: Token[], lastParagraphClipped?: boolean): Token[];\n  blockTokens(src: string, tokens?: TokensList, lastParagraphClipped?: boolean): TokensList;\n  blockTokens(src: string, tokens: Token[] = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n    }\n\n    while (src) {\n      let token: Tokens.Generic | undefined;\n\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== undefined) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unnecessary paragraph tags\n          lastToken.raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title,\n          };\n        }\n        continue;\n      }\n\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1)!.src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    this.state.top = true;\n    return tokens;\n  }\n\n  inline(src: string, tokens: Token[] = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src: string, tokens: Token[] = []): Token[] {\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match: RegExpExecArray | null = null;\n\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index)\n              + '[' + 'a'.repeat(match[0].length - 2) + ']'\n              + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n\n    // Mask out escaped characters\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n\n    let keepPrevChar = false;\n    let prevChar = '';\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n\n      let token: Tokens.Generic | undefined;\n\n      // extensions\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === 'text' && lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // autolink\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n\n    return tokens;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport {\n  cleanUrl,\n  escape,\n} from './helpers.ts';\nimport { other } from './rules.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Tokens } from './Tokens.ts';\nimport type { _Parser } from './Parser.ts';\n\n/**\n * Renderer\n */\nexport class _Renderer {\n  options: MarkedOptions;\n  parser!: _Parser; // set by the parser\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  space(token: Tokens.Space): string {\n    return '';\n  }\n\n  code({ text, lang, escaped }: Tokens.Code): string {\n    const langString = (lang || '').match(other.notSpaceStart)?.[0];\n\n    const code = text.replace(other.endingNewline, '') + '\\n';\n\n    if (!langString) {\n      return '<pre><code>'\n        + (escaped ? code : escape(code, true))\n        + '</code></pre>\\n';\n    }\n\n    return '<pre><code class=\"language-'\n      + escape(langString)\n      + '\">'\n      + (escaped ? code : escape(code, true))\n      + '</code></pre>\\n';\n  }\n\n  blockquote({ tokens }: Tokens.Blockquote): string {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\\n${body}</blockquote>\\n`;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag) : string {\n    return text;\n  }\n\n  heading({ tokens, depth }: Tokens.Heading): string {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n  }\n\n  hr(token: Tokens.Hr): string {\n    return '<hr>\\n';\n  }\n\n  list(token: Tokens.List): string {\n    const ordered = token.ordered;\n    const start = token.start;\n\n    let body = '';\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n\n    const type = ordered ? 'ol' : 'ul';\n    const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n    return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n  }\n\n  listitem(item: Tokens.ListItem): string {\n    let itemBody = '';\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === 'paragraph') {\n          item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n            item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: 'text',\n            raw: checkbox + ' ',\n            text: checkbox + ' ',\n            escaped: true,\n          });\n        }\n      } else {\n        itemBody += checkbox + ' ';\n      }\n    }\n\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n\n    return `<li>${itemBody}</li>\\n`;\n  }\n\n  checkbox({ checked }: Tokens.Checkbox): string {\n    return '<input '\n      + (checked ? 'checked=\"\" ' : '')\n      + 'disabled=\"\" type=\"checkbox\">';\n  }\n\n  paragraph({ tokens }: Tokens.Paragraph): string {\n    return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n  }\n\n  table(token: Tokens.Table): string {\n    let header = '';\n\n    // header\n    let cell = '';\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell });\n\n    let body = '';\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n\n      cell = '';\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n\n      body += this.tablerow({ text: cell });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n\n    return '<table>\\n'\n      + '<thead>\\n'\n      + header\n      + '</thead>\\n'\n      + body\n      + '</table>\\n';\n  }\n\n  tablerow({ text }: Tokens.TableRow): string {\n    return `<tr>\\n${text}</tr>\\n`;\n  }\n\n  tablecell(token: Tokens.TableCell): string {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? 'th' : 'td';\n    const tag = token.align\n      ? `<${type} align=\"${token.align}\">`\n      : `<${type}>`;\n    return tag + content + `</${type}>\\n`;\n  }\n\n  /**\n   * span level renderer\n   */\n  strong({ tokens }: Tokens.Strong): string {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n\n  em({ tokens }: Tokens.Em): string {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n\n  codespan({ text }: Tokens.Codespan): string {\n    return `<code>${escape(text, true)}</code>`;\n  }\n\n  br(token: Tokens.Br): string {\n    return '<br>';\n  }\n\n  del({ tokens }: Tokens.Del): string {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n\n  link({ href, title, tokens }: Tokens.Link): string {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + (escape(title)) + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out;\n  }\n\n  image({ href, title, text, tokens }: Tokens.Image): string {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape(text);\n    }\n    href = cleanHref;\n\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape(title)}\"`;\n    }\n    out += '>';\n    return out;\n  }\n\n  text(token: Tokens.Text | Tokens.Escape) : string {\n    return 'tokens' in token && token.tokens\n      ? this.parser.parseInline(token.tokens)\n      : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n  }\n}\n", "import type { Tokens } from './Tokens.ts';\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nexport class _TextRenderer {\n  // no need for block level renderers\n  strong({ text }: Tokens.Strong) {\n    return text;\n  }\n\n  em({ text }: Tokens.Em) {\n    return text;\n  }\n\n  codespan({ text }: Tokens.Codespan) {\n    return text;\n  }\n\n  del({ text }: Tokens.Del) {\n    return text;\n  }\n\n  html({ text }: Tokens.HTML | Tokens.Tag) {\n    return text;\n  }\n\n  text({ text }: Tokens.Text | Tokens.Escape | Tokens.Tag) {\n    return text;\n  }\n\n  link({ text }: Tokens.Link) {\n    return '' + text;\n  }\n\n  image({ text }: Tokens.Image) {\n    return '' + text;\n  }\n\n  br() {\n    return '';\n  }\n}\n", "import { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _defaults } from './defaults.ts';\nimport type { MarkedToken, Token, Tokens } from './Tokens.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\n\n/**\n * Parsing & Compiling\n */\nexport class _Parser {\n  options: MarkedOptions;\n  renderer: _Renderer;\n  textRenderer: _TextRenderer;\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens: Token[], options?: MarkedOptions) {\n    const parser = new _Parser(options);\n    return parser.parse(tokens);\n  }\n\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens: Token[], options?: MarkedOptions) {\n    const parser = new _Parser(options);\n    return parser.parseInline(tokens);\n  }\n\n  /**\n   * Parse Loop\n   */\n  parse(tokens: Token[], top = true): string {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken as Tokens.Generic;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'space': {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case 'hr': {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case 'heading': {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case 'code': {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case 'table': {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case 'blockquote': {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case 'list': {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case 'html': {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case 'paragraph': {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case 'text': {\n          let textToken = token;\n          let body = this.renderer.text(textToken);\n          while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n            textToken = tokens[++i] as Tokens.Text;\n            body += '\\n' + this.renderer.text(textToken);\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: 'paragraph',\n              raw: body,\n              text: body,\n              tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '';\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n\n    return out;\n  }\n\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens: Token[], renderer: _Renderer | _TextRenderer = this.renderer): string {\n    let out = '';\n\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n\n      const token = anyToken as MarkedToken;\n\n      switch (token.type) {\n        case 'escape': {\n          out += renderer.text(token);\n          break;\n        }\n        case 'html': {\n          out += renderer.html(token);\n          break;\n        }\n        case 'link': {\n          out += renderer.link(token);\n          break;\n        }\n        case 'image': {\n          out += renderer.image(token);\n          break;\n        }\n        case 'strong': {\n          out += renderer.strong(token);\n          break;\n        }\n        case 'em': {\n          out += renderer.em(token);\n          break;\n        }\n        case 'codespan': {\n          out += renderer.codespan(token);\n          break;\n        }\n        case 'br': {\n          out += renderer.br(token);\n          break;\n        }\n        case 'del': {\n          out += renderer.del(token);\n          break;\n        }\n        case 'text': {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return '';\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n}\n", "import { _defaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport type { MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\n\nexport class _Hooks {\n  options: MarkedOptions;\n  block?: boolean;\n\n  constructor(options?: MarkedOptions) {\n    this.options = options || _defaults;\n  }\n\n  static passThroughHooks = new Set([\n    'preprocess',\n    'postprocess',\n    'processAllTokens',\n  ]);\n\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown: string) {\n    return markdown;\n  }\n\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html: string) {\n    return html;\n  }\n\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens: Token[] | TokensList) {\n    return tokens;\n  }\n\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n}\n", "import { _getDefaults } from './defaults.ts';\nimport { _Lexer } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { escape } from './helpers.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, Tokens, TokensList } from './Tokens.ts';\n\nexport type MaybePromise = void | Promise<void>;\n\ntype UnknownFunction = (...args: unknown[]) => unknown;\ntype GenericRendererFunction = (...args: unknown[]) => string | false;\n\nexport class Marked {\n  defaults = _getDefaults();\n  options = this.setOptions;\n\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n\n  constructor(...args: MarkedExtension[]) {\n    this.use(...args);\n  }\n\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n    let values: MaybePromise[] = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case 'table': {\n          const tableToken = token as Tokens.Table;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case 'list': {\n          const listToken = token as Tokens.List;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token as Tokens.Generic;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens = genericToken[childTokens].flat(Infinity) as Token[] | TokensList;\n              values = values.concat(this.walkTokens(tokens, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n\n  use(...args: MarkedExtension[]) {\n    const extensions: MarkedOptions['extensions'] = this.defaults.extensions || { renderers: {}, childTokens: {} };\n\n    args.forEach((pack) => {\n      // copy options to new object\n      const opts = { ...pack } as MarkedOptions;\n\n      // set async to true if it was set to true before\n      opts.async = this.defaults.async || opts.async || false;\n\n      // ==-- Parse \"addon\" extensions --== //\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error('extension name required');\n          }\n          if ('renderer' in ext) { // Renderer extensions\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              // Replace extension with func to run new extension but fall back if false\n              extensions.renderers[ext.name] = function(...args) {\n                let ret = ext.renderer.apply(this, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if ('tokenizer' in ext) { // Tokenizer Extensions\n            if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) { // Function to check for start of token\n              if (ext.level === 'block') {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === 'inline') {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n\n      // ==-- Parse \"overwrite\" extensions --== //\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if (['options', 'parser'].includes(prop)) {\n            // ignore options property\n            continue;\n          }\n          const rendererProp = prop as Exclude<keyof _Renderer, 'options' | 'parser'>;\n          const rendererFunc = pack.renderer[rendererProp] as GenericRendererFunction;\n          const prevRenderer = renderer[rendererProp] as GenericRendererFunction;\n          // Replace renderer with func to run extension, but fall back if false\n          renderer[rendererProp] = (...args: unknown[]) => {\n            let ret = rendererFunc.apply(renderer, args);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args);\n            }\n            return ret || '';\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if (['options', 'rules', 'lexer'].includes(prop)) {\n            // ignore options, rules, and lexer properties\n            continue;\n          }\n          const tokenizerProp = prop as Exclude<keyof _Tokenizer, 'options' | 'rules' | 'lexer'>;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp] as UnknownFunction;\n          const prevTokenizer = tokenizer[tokenizerProp] as UnknownFunction;\n          // Replace tokenizer with func to run extension, but fall back if false\n          // @ts-expect-error cannot type tokenizer function dynamically\n          tokenizer[tokenizerProp] = (...args: unknown[]) => {\n            let ret = tokenizerFunc.apply(tokenizer, args);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n\n      // ==-- Parse Hooks extensions --== //\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if (['options', 'block'].includes(prop)) {\n            // ignore options and block properties\n            continue;\n          }\n          const hooksProp = prop as Exclude<keyof _Hooks, 'options' | 'block'>;\n          const hooksFunc = pack.hooks[hooksProp] as UnknownFunction;\n          const prevHook = hooks[hooksProp] as UnknownFunction;\n          if (_Hooks.passThroughHooks.has(prop)) {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (arg: unknown) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                  return prevHook.call(hooks, ret);\n                });\n              }\n\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (...args: unknown[]) => {\n              let ret = hooksFunc.apply(hooks, args);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n\n      // ==-- Parse WalkTokens extensions --== //\n      if (pack.walkTokens) {\n        const walkTokens = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values: MaybePromise[] = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens) {\n            values = values.concat(walkTokens.call(this, token));\n          }\n          return values;\n        };\n      }\n\n      this.defaults = { ...this.defaults, ...opts };\n    });\n\n    return this;\n  }\n\n  setOptions(opt: MarkedOptions) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n\n  lexer(src: string, options?: MarkedOptions) {\n    return _Lexer.lex(src, options ?? this.defaults);\n  }\n\n  parser(tokens: Token[], options?: MarkedOptions) {\n    return _Parser.parse(tokens, options ?? this.defaults);\n  }\n\n  private parseMarkdown(blockType: boolean) {\n    type overloadedParse = {\n      (src: string, options: MarkedOptions & { async: true }): Promise<string>;\n      (src: string, options: MarkedOptions & { async: false }): string;\n      (src: string, options?: MarkedOptions | null): string | Promise<string>;\n    };\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const parse: overloadedParse = (src: string, options?: MarkedOptions | null): any => {\n      const origOpt = { ...options };\n      const opt = { ...this.defaults, ...origOpt };\n\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n\n      // throw error if an extension set async to true but parse was called with async: false\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n      }\n\n      // throw error in case of non string input\n      if (typeof src === 'undefined' || src === null) {\n        return throwError(new Error('marked(): input parameter is undefined or null'));\n      }\n      if (typeof src !== 'string') {\n        return throwError(new Error('marked(): input parameter is of type '\n          + Object.prototype.toString.call(src) + ', string expected'));\n      }\n\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n\n      const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n      const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n          .then(src => lexer(src, opt))\n          .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n          .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n          .then(tokens => parser(tokens, opt))\n          .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n          .catch(throwError);\n      }\n\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src) as string;\n        }\n        let tokens = lexer(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html = parser(tokens, opt);\n        if (opt.hooks) {\n          html = opt.hooks.postprocess(html) as string;\n        }\n        return html;\n      } catch (e) {\n        return throwError(e as Error);\n      }\n    };\n\n    return parse;\n  }\n\n  private onError(silent: boolean, async: boolean) {\n    return (e: Error): string | Promise<string> => {\n      e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n\n      if (silent) {\n        const msg = '<p>An error occurred:</p><pre>'\n          + escape(e.message + '', true)\n          + '</pre>';\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n}\n", "import { _<PERSON>er } from './Lexer.ts';\nimport { _Parser } from './Parser.ts';\nimport { _Tokenizer } from './Tokenizer.ts';\nimport { _Renderer } from './Renderer.ts';\nimport { _TextRenderer } from './TextRenderer.ts';\nimport { _Hooks } from './Hooks.ts';\nimport { Marked } from './Instance.ts';\nimport {\n  _getDefaults,\n  changeDefaults,\n  _defaults,\n} from './defaults.ts';\nimport type { MarkedExtension, MarkedOptions } from './MarkedOptions.ts';\nimport type { Token, TokensList } from './Tokens.ts';\nimport type { MaybePromise } from './Instance.ts';\n\nconst markedInstance = new Marked();\n\n/**\n * Compiles markdown to HTML asynchronously.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options, having async: true\n * @return Promise of string of compiled HTML\n */\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\n\n/**\n * Compiles markdown to HTML.\n *\n * @param src String of markdown source to be compiled\n * @param options Optional hash of options\n * @return String of compiled HTML. Will be a Promise of string if async is set to true by any extensions.\n */\nexport function marked(src: string, options: MarkedOptions & { async: false }): string;\nexport function marked(src: string, options: MarkedOptions & { async: true }): Promise<string>;\nexport function marked(src: string, options?: MarkedOptions | null): string | Promise<string>;\nexport function marked(src: string, opt?: MarkedOptions | null): string | Promise<string> {\n  return markedInstance.parse(src, opt);\n}\n\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\nmarked.setOptions = function(options: MarkedOptions) {\n  markedInstance.setOptions(options);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\n\nmarked.defaults = _defaults;\n\n/**\n * Use Extension\n */\n\nmarked.use = function(...args: MarkedExtension[]) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n\n/**\n * Run callback for every token\n */\n\nmarked.walkTokens = function(tokens: Token[] | TokensList, callback: (token: Token) => MaybePromise | MaybePromise[]) {\n  return markedInstance.walkTokens(tokens, callback);\n};\n\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\n\nexport const options = marked.options;\nexport const setOptions = marked.setOptions;\nexport const use = marked.use;\nexport const walkTokens = marked.walkTokens;\nexport const parseInline = marked.parseInline;\nexport const parse = marked;\nexport const parser = _Parser.parse;\nexport const lexer = _Lexer.lex;\nexport { _defaults as defaults, _getDefaults as getDefaults } from './defaults.ts';\nexport { _Lexer as Lexer } from './Lexer.ts';\nexport { _Parser as Parser } from './Parser.ts';\nexport { _Tokenizer as Tokenizer } from './Tokenizer.ts';\nexport { _Renderer as Renderer } from './Renderer.ts';\nexport { _TextRenderer as TextRenderer } from './TextRenderer.ts';\nexport { _Hooks as Hooks } from './Hooks.ts';\nexport { Marked } from './Instance.ts';\nexport type * from './MarkedOptions.ts';\nexport type * from './Tokens.ts';\n"], "names": ["escape", "html", "link", "lexer", "options", "list", "tag", "text", "options", "lexer", "options", "escape", "tag", "options", "parser", "options", "html", "tokens", "args", "ret", "walkTokens", "options", "parse", "lexer", "parser", "src", "html", "escape", "options"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKO,SAAS,eAA8B;IAC5C,OAAO;QACL,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,KAAK;QACL,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,YAAY;IACd;AACF;AAEO,IAAI,YAAY,aAAa;AAE7B,SAAS,eAAe,WAAA,EAA4B;IACzD,YAAY;AACd;;ACxBA,IAAM,WAAW;IAAE,MAAM,IAAM;AAAK;AAEpC,SAAS,KAAK,KAAA,EAAwB,MAAM,EAAA,EAAI;IAC9C,IAAI,SAAS,OAAO,UAAU,WAAW,QAAQ,MAAM,MAAA;IACvD,MAAM,MAAM;QACV,SAAS,CAAC,MAAuB,QAAyB;YACxD,IAAI,YAAY,OAAO,QAAQ,WAAW,MAAM,IAAI,MAAA;YACpD,YAAY,UAAU,OAAA,CAAQ,MAAM,KAAA,EAAO,IAAI;YAC/C,SAAS,OAAO,OAAA,CAAQ,MAAM,SAAS;YACvC,OAAO;QACT;QACA,UAAU,MAAM;YACd,OAAO,IAAI,OAAO,QAAQ,GAAG;QAC/B;IACF;IACA,OAAO;AACT;AAEO,IAAM,QAAQ;IACnB,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;IACxB,gBAAgB;IAChB,YAAY;IACZ,mBAAmB;IACnB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,yBAAyB;IACzB,0BAA0B;IAC1B,iBAAiB;IACjB,oBAAoB;IACpB,YAAY;IACZ,iBAAiB;IACjB,SAAS;IACT,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;IAChB,WAAW;IACX,SAAS;IACT,mBAAmB;IACnB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,mBAAmB;IACnB,qBAAqB;IACrB,YAAY;IACZ,eAAe;IACf,oBAAoB;IACpB,uBAAuB;IACvB,cAAc;IACd,OAAO;IACP,eAAe;IACf,UAAU;IACV,WAAW;IACX,WAAW;IACX,gBAAgB;IAChB,WAAW;IACX,eAAe;IACf,eAAe;IACf,eAAe,CAAC,OAAiB,IAAI,OAAO,CAAA,QAAA,EAAW,IAAI,CAAA,4BAAA,CAA+B;IAC1F,iBAAiB,CAAC,SAAmB,IAAI,OAAO,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAI,GAAG,SAAS,CAAC,CAAC,CAAA,kDAAA,CAAqD;IACpI,SAAS,CAAC,SAAmB,IAAI,OAAO,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAI,GAAG,SAAS,CAAC,CAAC,CAAA,kDAAA,CAAoD;IAC3H,kBAAkB,CAAC,SAAmB,IAAI,OAAO,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAI,GAAG,SAAS,CAAC,CAAC,CAAA,eAAA,CAAiB;IACjG,mBAAmB,CAAC,SAAmB,IAAI,OAAO,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAI,GAAG,SAAS,CAAC,CAAC,CAAA,EAAA,CAAI;IACrF,gBAAgB,CAAC,SAAmB,IAAI,OAAO,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAI,GAAG,SAAS,CAAC,CAAC,CAAA,kBAAA,CAAA,EAAsB,GAAG;AACzG;AAMA,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,SAAS;AACf,IAAM,KAAK;AACX,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,WAAW,KAAK,YAAY,EAC/B,OAAA,CAAQ,SAAS,MAAM,EACvB,OAAA,CAAQ,cAAc,mBAAmB,EACzC,OAAA,CAAQ,WAAW,uBAAuB,EAC1C,OAAA,CAAQ,eAAe,SAAS,EAChC,OAAA,CAAQ,YAAY,cAAc,EAClC,OAAA,CAAQ,SAAS,mBAAmB,EACpC,OAAA,CAAQ,YAAY,EAAE,EACtB,QAAA,CAAS;AACZ,IAAM,cAAc,KAAK,YAAY,EAClC,OAAA,CAAQ,SAAS,MAAM,EACvB,OAAA,CAAQ,cAAc,mBAAmB,EACzC,OAAA,CAAQ,WAAW,uBAAuB,EAC1C,OAAA,CAAQ,eAAe,SAAS,EAChC,OAAA,CAAQ,YAAY,cAAc,EAClC,OAAA,CAAQ,SAAS,mBAAmB,EACpC,OAAA,CAAQ,UAAU,mCAAmC,EACrD,QAAA,CAAS;AACZ,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,MAAM,KAAK,6GAA6G,EAC3H,OAAA,CAAQ,SAAS,WAAW,EAC5B,OAAA,CAAQ,SAAS,8DAA8D,EAC/E,QAAA,CAAS;AAEZ,IAAM,OAAO,KAAK,sCAAsC,EACrD,OAAA,CAAQ,SAAS,MAAM,EACvB,QAAA,CAAS;AAEZ,IAAM,OAAO;AAMb,IAAM,WAAW;AACjB,IAAM,OAAO,KACX,6dASK,KACJ,OAAA,CAAQ,WAAW,QAAQ,EAC3B,OAAA,CAAQ,OAAO,IAAI,EACnB,OAAA,CAAQ,aAAa,0EAA0E,EAC/F,QAAA,CAAS;AAEZ,IAAM,YAAY,KAAK,UAAU,EAC9B,OAAA,CAAQ,MAAM,EAAE,EAChB,OAAA,CAAQ,WAAW,uBAAuB,EAC1C,OAAA,CAAQ,aAAa,EAAE,EACvB,OAAA,CAAQ,UAAU,EAAE,EACpB,OAAA,CAAQ,cAAc,SAAS,EAC/B,OAAA,CAAQ,UAAU,gDAAgD,EAClE,OAAA,CAAQ,QAAQ,wBAAwB,EACxC,OAAA,CAAQ,QAAQ,6DAA6D,EAC7E,OAAA,CAAQ,OAAO,IAAI,EACnB,QAAA,CAAS;AAEZ,IAAM,aAAa,KAAK,yCAAyC,EAC9D,OAAA,CAAQ,aAAa,SAAS,EAC9B,QAAA,CAAS;AAMZ,IAAM,cAAc;IAClB;IACA,MAAM;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO;IACP,MAAM;AACR;AAQA,IAAM,WAAW,KACf,+JAGC,OAAA,CAAQ,MAAM,EAAE,EAChB,OAAA,CAAQ,WAAW,uBAAuB,EAC1C,OAAA,CAAQ,cAAc,SAAS,EAC/B,OAAA,CAAQ,QAAQ,wBAAyB,EACzC,OAAA,CAAQ,UAAU,gDAAgD,EAClE,OAAA,CAAQ,QAAQ,wBAAwB,EACxC,OAAA,CAAQ,QAAQ,6DAA6D,EAC7E,OAAA,CAAQ,OAAO,IAAI,EACnB,QAAA,CAAS;AAEZ,IAAM,WAAsC;IAC1C,GAAG,WAAA;IACH,UAAU;IACV,OAAO;IACP,WAAW,KAAK,UAAU,EACvB,OAAA,CAAQ,MAAM,EAAE,EAChB,OAAA,CAAQ,WAAW,uBAAuB,EAC1C,OAAA,CAAQ,aAAa,EAAE,EACvB,OAAA,CAAQ,SAAS,QAAQ,EACzB,OAAA,CAAQ,cAAc,SAAS,EAC/B,OAAA,CAAQ,UAAU,gDAAgD,EAClE,OAAA,CAAQ,QAAQ,wBAAwB,EACxC,OAAA,CAAQ,QAAQ,6DAA6D,EAC7E,OAAA,CAAQ,OAAO,IAAI,EACnB,QAAA,CAAS;AACd;AAMA,IAAM,gBAA2C;IAC/C,GAAG,WAAA;IACH,MAAM,KACJ,CAAA,sIAAA,CAAA,EAGC,OAAA,CAAQ,WAAW,QAAQ,EAC3B,OAAA,CAAQ,QAAQ,mKAGkB,EAClC,QAAA,CAAS;IACZ,KAAK;IACL,SAAS;IACT,QAAQ;IAAA,uBAAA;IACR,UAAU;IACV,WAAW,KAAK,UAAU,EACvB,OAAA,CAAQ,MAAM,EAAE,EAChB,OAAA,CAAQ,WAAW,iBAAiB,EACpC,OAAA,CAAQ,YAAY,QAAQ,EAC5B,OAAA,CAAQ,UAAU,EAAE,EACpB,OAAA,CAAQ,cAAc,SAAS,EAC/B,OAAA,CAAQ,WAAW,EAAE,EACrB,OAAA,CAAQ,SAAS,EAAE,EACnB,OAAA,CAAQ,SAAS,EAAE,EACnB,OAAA,CAAQ,QAAQ,EAAE,EAClB,QAAA,CAAS;AACd;AAMA,IAAM,SAAS;AACf,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,aAAa;AAGnB,IAAM,eAAe;AACrB,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,cAAc,KAAK,yBAAyB,GAAG,EAClD,OAAA,CAAQ,eAAe,mBAAmB,EAAE,QAAA,CAAS;AAGxD,IAAM,0BAA0B;AAChC,IAAM,iCAAiC;AACvC,IAAM,oCAAoC;AAG1C,IAAM,YAAY;AAElB,IAAM,qBAAqB;AAE3B,IAAM,iBAAiB,KAAK,oBAAoB,GAAG,EAChD,OAAA,CAAQ,UAAU,YAAY,EAC9B,QAAA,CAAS;AAEZ,IAAM,oBAAoB,KAAK,oBAAoB,GAAG,EACnD,OAAA,CAAQ,UAAU,uBAAuB,EACzC,QAAA,CAAS;AAEZ,IAAM,wBACJ;AASF,IAAM,oBAAoB,KAAK,uBAAuB,IAAI,EACvD,OAAA,CAAQ,kBAAkB,sBAAsB,EAChD,OAAA,CAAQ,eAAe,mBAAmB,EAC1C,OAAA,CAAQ,UAAU,YAAY,EAC9B,QAAA,CAAS;AAEZ,IAAM,uBAAuB,KAAK,uBAAuB,IAAI,EAC1D,OAAA,CAAQ,kBAAkB,iCAAiC,EAC3D,OAAA,CAAQ,eAAe,8BAA8B,EACrD,OAAA,CAAQ,UAAU,uBAAuB,EACzC,QAAA,CAAS;AAGZ,IAAM,oBAAoB,KACxB,oNAMiC,MAChC,OAAA,CAAQ,kBAAkB,sBAAsB,EAChD,OAAA,CAAQ,eAAe,mBAAmB,EAC1C,OAAA,CAAQ,UAAU,YAAY,EAC9B,QAAA,CAAS;AAEZ,IAAM,iBAAiB,KAAK,aAAa,IAAI,EAC1C,OAAA,CAAQ,UAAU,YAAY,EAC9B,QAAA,CAAS;AAEZ,IAAM,WAAW,KAAK,qCAAqC,EACxD,OAAA,CAAQ,UAAU,8BAA8B,EAChD,OAAA,CAAQ,SAAS,8IAA8I,EAC/J,QAAA,CAAS;AAEZ,IAAM,iBAAiB,KAAK,QAAQ,EAAE,OAAA,CAAQ,aAAa,KAAK,EAAE,QAAA,CAAS;AAC3E,IAAM,MAAM,KACV,4JAMC,OAAA,CAAQ,WAAW,cAAc,EACjC,OAAA,CAAQ,aAAa,6EAA6E,EAClG,QAAA,CAAS;AAEZ,IAAM,eAAe;AAErB,IAAM,OAAO,KAAK,mEAAmE,EAClF,OAAA,CAAQ,SAAS,YAAY,EAC7B,OAAA,CAAQ,QAAQ,yCAAyC,EACzD,OAAA,CAAQ,SAAS,6DAA6D,EAC9E,QAAA,CAAS;AAEZ,IAAM,UAAU,KAAK,yBAAyB,EAC3C,OAAA,CAAQ,SAAS,YAAY,EAC7B,OAAA,CAAQ,OAAO,WAAW,EAC1B,QAAA,CAAS;AAEZ,IAAM,SAAS,KAAK,uBAAuB,EACxC,OAAA,CAAQ,OAAO,WAAW,EAC1B,QAAA,CAAS;AAEZ,IAAM,gBAAgB,KAAK,yBAAyB,GAAG,EACpD,OAAA,CAAQ,WAAW,OAAO,EAC1B,OAAA,CAAQ,UAAU,MAAM,EACxB,QAAA,CAAS;AAMZ,IAAM,eAAe;IACnB,YAAY;IAAA,wBAAA;IACZ;IACA;IACA;IACA;IACA,MAAM;IACN,KAAK;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM;IACN,KAAK;AACP;AAQA,IAAM,iBAA6C;IACjD,GAAG,YAAA;IACH,MAAM,KAAK,yBAAyB,EACjC,OAAA,CAAQ,SAAS,YAAY,EAC7B,QAAA,CAAS;IACZ,SAAS,KAAK,+BAA+B,EAC1C,OAAA,CAAQ,SAAS,YAAY,EAC7B,QAAA,CAAS;AACd;AAMA,IAAM,YAAwC;IAC5C,GAAG,YAAA;IACH,mBAAmB;IACnB,gBAAgB;IAChB,KAAK,KAAK,oEAAoE,GAAG,EAC9E,OAAA,CAAQ,SAAS,2EAA2E,EAC5F,QAAA,CAAS;IACZ,YAAY;IACZ,KAAK;IACL,MAAM;AACR;AAMA,IAAM,eAA2C;IAC/C,GAAG,SAAA;IACH,IAAI,KAAK,EAAE,EAAE,OAAA,CAAQ,QAAQ,GAAG,EAAE,QAAA,CAAS;IAC3C,MAAM,KAAK,UAAU,IAAI,EACtB,OAAA,CAAQ,QAAQ,eAAe,EAC/B,OAAA,CAAQ,WAAW,GAAG,EACtB,QAAA,CAAS;AACd;AAMO,IAAM,QAAQ;IACnB,QAAQ;IACR,KAAK;IACL,UAAU;AACZ;AAEO,IAAM,SAAS;IACpB,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,UAAU;AACZ;;ACzbA,IAAM,qBAAkD;IACtD,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AACA,IAAM,uBAAuB,CAAC,KAAe,kBAAA,CAAmB,EAAE,CAAA;AAE3D,SAASA,QAAOC,KAAAA,EAAc,MAAA,EAAkB;IACrD,IAAI,QAAQ;QACV,IAAI,MAAM,UAAA,CAAW,IAAA,CAAKA,KAAI,GAAG;YAC/B,OAAOA,MAAK,OAAA,CAAQ,MAAM,aAAA,EAAe,oBAAoB;QAC/D;IACF,OAAO;QACL,IAAI,MAAM,kBAAA,CAAmB,IAAA,CAAKA,KAAI,GAAG;YACvC,OAAOA,MAAK,OAAA,CAAQ,MAAM,qBAAA,EAAuB,oBAAoB;QACvE;IACF;IAEA,OAAOA;AACT;AAgBO,SAAS,SAAS,IAAA,EAAc;IACrC,IAAI;QACF,OAAO,UAAU,IAAI,EAAE,OAAA,CAAQ,MAAM,aAAA,EAAe,GAAG;IACzD,EAAA,OAAQ;QACN,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAAS,WAAW,QAAA,EAAkB,KAAA,EAAgB;IAG3D,MAAM,MAAM,SAAS,OAAA,CAAQ,MAAM,QAAA,EAAU,CAAC,OAAO,QAAQ,QAAQ;QACjE,IAAI,UAAU;QACd,IAAI,OAAO;QACX,MAAO,EAAE,QAAQ,KAAK,GAAA,CAAI,IAAI,CAAA,KAAM,KAAM,UAAU,CAAC;QACrD,IAAI,SAAS;YAGX,OAAO;QACT,OAAO;YAEL,OAAO;QACT;IACF,CAAC,GACD,QAAQ,IAAI,KAAA,CAAM,MAAM,SAAS;IACnC,IAAI,IAAI;IAGR,IAAI,CAAC,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,CAAK,GAAG;QACpB,MAAM,KAAA,CAAM;IACd;IACA,IAAI,MAAM,MAAA,GAAS,KAAK,CAAC,MAAM,EAAA,CAAG,CAAA,CAAE,GAAG,KAAK,GAAG;QAC7C,MAAM,GAAA,CAAI;IACZ;IAEA,IAAI,OAAO;QACT,IAAI,MAAM,MAAA,GAAS,OAAO;YACxB,MAAM,MAAA,CAAO,KAAK;QACpB,OAAO;YACL,MAAO,MAAM,MAAA,GAAS,MAAO,MAAM,IAAA,CAAK,EAAE;QAC5C;IACF;IAEA,MAAO,IAAI,MAAM,MAAA,EAAQ,IAAK;QAE5B,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,CAAK,EAAE,OAAA,CAAQ,MAAM,SAAA,EAAW,GAAG;IACzD;IACA,OAAO;AACT;AAUO,SAAS,MAAM,GAAA,EAAa,CAAA,EAAW,MAAA,EAAkB;IAC9D,MAAM,IAAI,IAAI,MAAA;IACd,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAGA,IAAI,UAAU;IAGd,MAAO,UAAU,EAAG;QAClB,MAAM,WAAW,IAAI,MAAA,CAAO,IAAI,UAAU,CAAC;QAC3C,IAAI,aAAa,KAAK,CAAC,QAAQ;YAC7B;QACF,OAAA,IAAW,aAAa,KAAK,QAAQ;YACnC;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO,IAAI,KAAA,CAAM,GAAG,IAAI,OAAO;AACjC;AAEO,SAAS,mBAAmB,GAAA,EAAa,CAAA,EAAW;IACzD,IAAI,IAAI,OAAA,CAAQ,CAAA,CAAE,CAAC,CAAC,MAAM,CAAA,GAAI;QAC5B,OAAO,CAAA;IACT;IAEA,IAAI,QAAQ;IACZ,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,MAAA,EAAQ,IAAK;QACnC,IAAI,GAAA,CAAI,CAAC,CAAA,KAAM,MAAM;YACnB;QACF,OAAA,IAAW,GAAA,CAAI,CAAC,CAAA,KAAM,CAAA,CAAE,CAAC,CAAA,EAAG;YAC1B;QACF,OAAA,IAAW,GAAA,CAAI,CAAC,CAAA,KAAM,CAAA,CAAE,CAAC,CAAA,EAAG;YAC1B;YACA,IAAI,QAAQ,GAAG;gBACb,OAAO;YACT;QACF;IACF;IACA,IAAI,QAAQ,GAAG;QACb,OAAO,CAAA;IACT;IAEA,OAAO,CAAA;AACT;;ACzIA,SAAS,WAAW,GAAA,EAAeC,KAAAA,EAA2C,GAAA,EAAaC,MAAAA,EAAe,KAAA,EAA0C;IAClJ,MAAM,OAAOD,MAAK,IAAA;IAClB,MAAM,QAAQA,MAAK,KAAA,IAAS;IAC5B,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,MAAM,KAAA,CAAM,iBAAA,EAAmB,IAAI;IAE/DC,OAAM,KAAA,CAAM,MAAA,GAAS;IACrB,MAAM,QAAoC;QACxC,MAAM,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,MAAM,MAAM,UAAU;QAC3C;QACA;QACA;QACA;QACA,QAAQA,OAAM,YAAA,CAAa,IAAI;IACjC;IACAA,OAAM,KAAA,CAAM,MAAA,GAAS;IACrB,OAAO;AACT;AAEA,SAAS,uBAAuB,GAAA,EAAa,IAAA,EAAc,KAAA,EAAc;IACvE,MAAM,oBAAoB,IAAI,KAAA,CAAM,MAAM,KAAA,CAAM,sBAAsB;IAEtE,IAAI,sBAAsB,MAAM;QAC9B,OAAO;IACT;IAEA,MAAM,eAAe,iBAAA,CAAkB,CAAC,CAAA;IAExC,OAAO,KACJ,KAAA,CAAM,IAAI,EACV,GAAA,CAAI,CAAA,SAAQ;QACX,MAAM,oBAAoB,KAAK,KAAA,CAAM,MAAM,KAAA,CAAM,cAAc;QAC/D,IAAI,sBAAsB,MAAM;YAC9B,OAAO;QACT;QAEA,MAAM,CAAC,YAAY,CAAA,GAAI;QAEvB,IAAI,aAAa,MAAA,IAAU,aAAa,MAAA,EAAQ;YAC9C,OAAO,KAAK,KAAA,CAAM,aAAa,MAAM;QACvC;QAEA,OAAO;IACT,CAAC,EACA,IAAA,CAAK,IAAI;AACd;AAKO,IAAM,aAAN,MAAiB;IACtB,QAAA;IACA,MAAA;IAAA,mBAAA;IACA,MAAA;IAAA,mBAAA;IAEA,YAAYC,QAAAA,CAAyB;QACnC,IAAA,CAAK,OAAA,GAAUA,YAAW;IAC5B;IAEA,MAAM,GAAA,EAAuC;QAC3C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,GAAG;QAC7C,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,GAAS,GAAG;YAC5B,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;YACZ;QACF;IACF;IAEA,KAAK,GAAA,EAAsC;QACzC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,GAAG;QAC1C,IAAI,KAAK;YACP,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,gBAAA,EAAkB,EAAE;YACjE,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,gBAAgB;gBAChB,MAAM,CAAC,IAAA,CAAK,OAAA,CAAQ,QAAA,GAChB,MAAM,MAAM,IAAI,IAChB;YACN;QACF;IACF;IAEA,OAAO,GAAA,EAAsC;QAC3C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,GAAG;QAC5C,IAAI,KAAK;YACP,MAAM,MAAM,GAAA,CAAI,CAAC,CAAA;YACjB,MAAM,OAAO,uBAAuB,KAAK,GAAA,CAAI,CAAC,CAAA,IAAK,IAAI,IAAA,CAAK,KAAK;YAEjE,OAAO;gBACL,MAAM;gBACN;gBACA,MAAM,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,IAAA,CAAK,EAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,EAAgB,IAAI,IAAI,GAAA,CAAI,CAAC,CAAA;gBACpF;YACF;QACF;IACF;IAEA,QAAQ,GAAA,EAAyC;QAC/C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,GAAG;QAC7C,IAAI,KAAK;YACP,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,IAAA,CAAK;YAGvB,IAAI,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,IAAI,GAAG;gBAC1C,MAAM,UAAU,MAAM,MAAM,GAAG;gBAC/B,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;oBACzB,OAAO,QAAQ,IAAA,CAAK;gBACtB,OAAA,IAAW,CAAC,WAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,OAAO,GAAG;oBAErE,OAAO,QAAQ,IAAA,CAAK;gBACtB;YACF;YAEA,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA;gBACd;gBACA,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAI;YAChC;QACF;IACF;IAEA,GAAG,GAAA,EAAoC;QACrC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,EAAA,CAAG,IAAA,CAAK,GAAG;QACxC,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,MAAM,GAAA,CAAI,CAAC,CAAA,EAAG,IAAI;YACzB;QACF;IACF;IAEA,WAAW,GAAA,EAA4C;QACrD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,GAAG;QAChD,IAAI,KAAK;YACP,IAAI,QAAQ,MAAM,GAAA,CAAI,CAAC,CAAA,EAAG,IAAI,EAAE,KAAA,CAAM,IAAI;YAC1C,IAAI,MAAM;YACV,IAAI,OAAO;YACX,MAAM,SAAkB,CAAC,CAAA;YAEzB,MAAO,MAAM,MAAA,GAAS,EAAG;gBACvB,IAAI,eAAe;gBACnB,MAAM,eAAe,CAAC,CAAA;gBAEtB,IAAI;gBACJ,IAAK,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;oBAEjC,IAAI,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,KAAA,CAAM,CAAC,CAAC,GAAG;wBACnD,aAAa,IAAA,CAAK,KAAA,CAAM,CAAC,CAAC;wBAC1B,eAAe;oBACjB,OAAA,IAAW,CAAC,cAAc;wBACxB,aAAa,IAAA,CAAK,KAAA,CAAM,CAAC,CAAC;oBAC5B,OAAO;wBACL;oBACF;gBACF;gBACA,QAAQ,MAAM,KAAA,CAAM,CAAC;gBAErB,MAAM,aAAa,aAAa,IAAA,CAAK,IAAI;gBACzC,MAAM,cAAc,WAEjB,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,uBAAA,EAAyB,UAAU,EAC5D,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,wBAAA,EAA0B,EAAE;gBACxD,MAAM,MAAM,GAAG,GAAG,CAAA;AAAA,EAAK,UAAU,EAAA,GAAK;gBACtC,OAAO,OAAO,GAAG,IAAI,CAAA;AAAA,EAAK,WAAW,EAAA,GAAK;gBAI1C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,GAAA;gBAC7B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,GAAA,GAAM;gBACvB,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,aAAa,QAAQ,IAAI;gBAChD,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,GAAA,GAAM;gBAGvB,IAAI,MAAM,MAAA,KAAW,GAAG;oBACtB;gBACF;gBAEA,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAE9B,IAAI,WAAW,SAAS,QAAQ;oBAE9B;gBACF,OAAA,IAAW,WAAW,SAAS,cAAc;oBAE3C,MAAM,WAAW;oBACjB,MAAM,UAAU,SAAS,GAAA,GAAM,OAAO,MAAM,IAAA,CAAK,IAAI;oBACrD,MAAM,WAAW,IAAA,CAAK,UAAA,CAAW,OAAO;oBACxC,MAAA,CAAO,OAAO,MAAA,GAAS,CAAC,CAAA,GAAI;oBAE5B,MAAM,IAAI,SAAA,CAAU,GAAG,IAAI,MAAA,GAAS,SAAS,GAAA,CAAI,MAAM,IAAI,SAAS,GAAA;oBACpE,OAAO,KAAK,SAAA,CAAU,GAAG,KAAK,MAAA,GAAS,SAAS,IAAA,CAAK,MAAM,IAAI,SAAS,IAAA;oBACxE;gBACF,OAAA,IAAW,WAAW,SAAS,QAAQ;oBAErC,MAAM,WAAW;oBACjB,MAAM,UAAU,SAAS,GAAA,GAAM,OAAO,MAAM,IAAA,CAAK,IAAI;oBACrD,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,OAAO;oBAClC,MAAA,CAAO,OAAO,MAAA,GAAS,CAAC,CAAA,GAAI;oBAE5B,MAAM,IAAI,SAAA,CAAU,GAAG,IAAI,MAAA,GAAS,UAAU,GAAA,CAAI,MAAM,IAAI,SAAS,GAAA;oBACrE,OAAO,KAAK,SAAA,CAAU,GAAG,KAAK,MAAA,GAAS,SAAS,GAAA,CAAI,MAAM,IAAI,SAAS,GAAA;oBACvE,QAAQ,QAAQ,SAAA,CAAU,OAAO,EAAA,CAAG,CAAA,CAAE,EAAG,GAAA,CAAI,MAAM,EAAE,KAAA,CAAM,IAAI;oBAC/D;gBACF;YACF;YAEA,OAAO;gBACL,MAAM;gBACN;gBACA;gBACA;YACF;QACF;IACF;IAEA,KAAK,GAAA,EAAsC;QACzC,IAAI,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,GAAG;QACxC,IAAI,KAAK;YACP,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,IAAA,CAAK;YACvB,MAAM,YAAY,KAAK,MAAA,GAAS;YAEhC,MAAMC,QAAoB;gBACxB,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,OAAO,YAAY,CAAC,KAAK,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI;gBACxC,OAAO;gBACP,OAAO,CAAC,CAAA;YACV;YAEA,OAAO,YAAY,CAAA,UAAA,EAAa,KAAK,KAAA,CAAM,CAAA,CAAE,CAAC,EAAA,GAAK,CAAA,EAAA,EAAK,IAAI,EAAA;YAE5D,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;gBACzB,OAAO,YAAY,OAAO;YAC5B;YAGA,MAAM,YAAY,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,aAAA,CAAc,IAAI;YACrD,IAAI,oBAAoB;YAExB,MAAO,IAAK;gBACV,IAAI,WAAW;gBACf,IAAI,MAAM;gBACV,IAAI,eAAe;gBACnB,IAAI,CAAA,CAAE,MAAM,UAAU,IAAA,CAAK,GAAG,CAAA,GAAI;oBAChC;gBACF;gBAEA,IAAI,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,EAAA,CAAG,IAAA,CAAK,GAAG,GAAG;oBACjC;gBACF;gBAEA,MAAM,GAAA,CAAI,CAAC,CAAA;gBACX,MAAM,IAAI,SAAA,CAAU,IAAI,MAAM;gBAE9B,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,KAAA,CAAM,MAAM,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,EAAiB,CAAC,IAAc,IAAI,MAAA,CAAO,IAAI,EAAE,MAAM,CAAC;gBACrH,IAAI,WAAW,IAAI,KAAA,CAAM,MAAM,CAAC,CAAA,CAAE,CAAC,CAAA;gBACnC,IAAI,YAAY,CAAC,KAAK,IAAA,CAAK;gBAE3B,IAAI,SAAS;gBACb,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;oBACzB,SAAS;oBACT,eAAe,KAAK,SAAA,CAAU;gBAChC,OAAA,IAAW,WAAW;oBACpB,SAAS,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,GAAS;gBAC3B,OAAO;oBACL,SAAS,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,CAAO,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,YAAY;oBACpD,SAAS,SAAS,IAAI,IAAI;oBAC1B,eAAe,KAAK,KAAA,CAAM,MAAM;oBAChC,UAAU,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA;gBACnB;gBAEA,IAAI,aAAa,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,SAAA,CAAU,IAAA,CAAK,QAAQ,GAAG;oBAC1D,OAAO,WAAW;oBAClB,MAAM,IAAI,SAAA,CAAU,SAAS,MAAA,GAAS,CAAC;oBACvC,WAAW;gBACb;gBAEA,IAAI,CAAC,UAAU;oBACb,MAAM,kBAAkB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,MAAM;oBAC/D,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,OAAA,CAAQ,MAAM;oBAC/C,MAAM,mBAAmB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,gBAAA,CAAiB,MAAM;oBACjE,MAAM,oBAAoB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,MAAM;oBACnE,MAAM,iBAAiB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,cAAA,CAAe,MAAM;oBAG7D,MAAO,IAAK;wBACV,MAAM,UAAU,IAAI,KAAA,CAAM,MAAM,CAAC,CAAA,CAAE,CAAC,CAAA;wBACpC,IAAI;wBACJ,WAAW;wBAGX,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;4BACzB,WAAW,SAAS,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,kBAAA,EAAoB,IAAI;4BACrE,sBAAsB;wBACxB,OAAO;4BACL,sBAAsB,SAAS,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,aAAA,EAAe,MAAM;wBAC/E;wBAGA,IAAI,iBAAiB,IAAA,CAAK,QAAQ,GAAG;4BACnC;wBACF;wBAGA,IAAI,kBAAkB,IAAA,CAAK,QAAQ,GAAG;4BACpC;wBACF;wBAGA,IAAI,eAAe,IAAA,CAAK,QAAQ,GAAG;4BACjC;wBACF;wBAGA,IAAI,gBAAgB,IAAA,CAAK,QAAQ,GAAG;4BAClC;wBACF;wBAGA,IAAI,QAAQ,IAAA,CAAK,QAAQ,GAAG;4BAC1B;wBACF;wBAEA,IAAI,oBAAoB,MAAA,CAAO,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,YAAY,KAAK,UAAU,CAAC,SAAS,IAAA,CAAK,GAAG;4BAC3F,gBAAgB,OAAO,oBAAoB,KAAA,CAAM,MAAM;wBACzD,OAAO;4BAEL,IAAI,WAAW;gCACb;4BACF;4BAGA,IAAI,KAAK,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,aAAA,EAAe,MAAM,EAAE,MAAA,CAAO,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,YAAY,KAAK,GAAG;gCACnG;4BACF;4BACA,IAAI,iBAAiB,IAAA,CAAK,IAAI,GAAG;gCAC/B;4BACF;4BACA,IAAI,kBAAkB,IAAA,CAAK,IAAI,GAAG;gCAChC;4BACF;4BACA,IAAI,QAAQ,IAAA,CAAK,IAAI,GAAG;gCACtB;4BACF;4BAEA,gBAAgB,OAAO;wBACzB;wBAEA,IAAI,CAAC,aAAa,CAAC,SAAS,IAAA,CAAK,GAAG;4BAClC,YAAY;wBACd;wBAEA,OAAO,UAAU;wBACjB,MAAM,IAAI,SAAA,CAAU,QAAQ,MAAA,GAAS,CAAC;wBACtC,OAAO,oBAAoB,KAAA,CAAM,MAAM;oBACzC;gBACF;gBAEA,IAAI,CAACA,MAAK,KAAA,EAAO;oBAEf,IAAI,mBAAmB;wBACrBA,MAAK,KAAA,GAAQ;oBACf,OAAA,IAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,GAAG,GAAG;wBACrD,oBAAoB;oBACtB;gBACF;gBAEA,IAAI,SAAiC;gBACrC,IAAI;gBAEJ,IAAI,IAAA,CAAK,OAAA,CAAQ,GAAA,EAAK;oBACpB,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,YAAY;oBACtD,IAAI,QAAQ;wBACV,YAAY,MAAA,CAAO,CAAC,CAAA,KAAM;wBAC1B,eAAe,aAAa,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,EAAiB,EAAE;oBAC1E;gBACF;gBAEAA,MAAK,KAAA,CAAM,IAAA,CAAK;oBACd,MAAM;oBACN;oBACA,MAAM,CAAC,CAAC;oBACR,SAAS;oBACT,OAAO;oBACP,MAAM;oBACN,QAAQ,CAAC,CAAA;gBACX,CAAC;gBAEDA,MAAK,GAAA,IAAO;YACd;YAGA,MAAM,WAAWA,MAAK,KAAA,CAAM,EAAA,CAAG,CAAA,CAAE;YACjC,IAAI,UAAU;gBACZ,SAAS,GAAA,GAAM,SAAS,GAAA,CAAI,OAAA,CAAQ;gBACpC,SAAS,IAAA,GAAO,SAAS,IAAA,CAAK,OAAA,CAAQ;YACxC,OAAO;gBAEL;YACF;YACAA,MAAK,GAAA,GAAMA,MAAK,GAAA,CAAI,OAAA,CAAQ;YAG5B,IAAA,IAAS,IAAI,GAAG,IAAIA,MAAK,KAAA,CAAM,MAAA,EAAQ,IAAK;gBAC1C,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,GAAA,GAAM;gBACvBA,MAAK,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,WAAA,CAAYA,MAAK,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,EAAM,CAAC,CAAC;gBAEpE,IAAI,CAACA,MAAK,KAAA,EAAO;oBAEf,MAAM,UAAUA,MAAK,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,CAAO,MAAA,CAAO,CAAA,IAAK,EAAE,IAAA,KAAS,OAAO;oBACnE,MAAM,wBAAwB,QAAQ,MAAA,GAAS,KAAK,QAAQ,IAAA,CAAK,CAAA,IAAK,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,EAAE,GAAG,CAAC;oBAE1GA,MAAK,KAAA,GAAQ;gBACf;YACF;YAGA,IAAIA,MAAK,KAAA,EAAO;gBACd,IAAA,IAAS,IAAI,GAAG,IAAIA,MAAK,KAAA,CAAM,MAAA,EAAQ,IAAK;oBAC1CA,MAAK,KAAA,CAAM,CAAC,CAAA,CAAE,KAAA,GAAQ;gBACxB;YACF;YAEA,OAAOA;QACT;IACF;IAEA,KAAK,GAAA,EAAsC;QACzC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,GAAG;QAC1C,IAAI,KAAK;YACP,MAAM,QAAqB;gBACzB,MAAM;gBACN,OAAO;gBACP,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,KAAK,GAAA,CAAI,CAAC,CAAA,KAAM,SAAS,GAAA,CAAI,CAAC,CAAA,KAAM,YAAY,GAAA,CAAI,CAAC,CAAA,KAAM;gBAC3D,MAAM,GAAA,CAAI,CAAC,CAAA;YACb;YACA,OAAO;QACT;IACF;IAEA,IAAI,GAAA,EAAqC;QACvC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,GAAG;QACzC,IAAI,KAAK;YACP,MAAMC,OAAM,GAAA,CAAI,CAAC,CAAA,CAAE,WAAA,CAAY,EAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,mBAAA,EAAqB,GAAG;YAClF,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,YAAA,EAAc,IAAI,EAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,EAAgB,IAAI,IAAI;YAC5H,MAAM,QAAQ,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,SAAA,CAAU,GAAG,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,GAAS,CAAC,EAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,EAAgB,IAAI,IAAI,GAAA,CAAI,CAAC,CAAA;YACrH,OAAO;gBACL,MAAM;gBACN,KAAAA;gBACA,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV;gBACA;YACF;QACF;IACF;IAEA,MAAM,GAAA,EAAuC;QAC3C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,GAAG;QAC3C,IAAI,CAAC,KAAK;YACR;QACF;QAEA,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,cAAA,CAAe,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAG;YAEjD;QACF;QAEA,MAAM,UAAU,WAAW,GAAA,CAAI,CAAC,CAAC;QACjC,MAAM,SAAS,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,EAAiB,EAAE,EAAE,KAAA,CAAM,GAAG;QAC7E,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,EAAG,KAAK,IAAI,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,EAAmB,EAAE,EAAE,KAAA,CAAM,IAAI,IAAI,CAAC,CAAA;QAEpG,MAAM,OAAqB;YACzB,MAAM;YACN,KAAK,GAAA,CAAI,CAAC,CAAA;YACV,QAAQ,CAAC,CAAA;YACT,OAAO,CAAC,CAAA;YACR,MAAM,CAAC,CAAA;QACT;QAEA,IAAI,QAAQ,MAAA,KAAW,OAAO,MAAA,EAAQ;YAEpC;QACF;QAEA,KAAA,MAAW,SAAS,OAAQ;YAC1B,IAAI,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,KAAK,GAAG;gBAChD,KAAK,KAAA,CAAM,IAAA,CAAK,OAAO;YACzB,OAAA,IAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,gBAAA,CAAiB,IAAA,CAAK,KAAK,GAAG;gBACxD,KAAK,KAAA,CAAM,IAAA,CAAK,QAAQ;YAC1B,OAAA,IAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,cAAA,CAAe,IAAA,CAAK,KAAK,GAAG;gBACtD,KAAK,KAAA,CAAM,IAAA,CAAK,MAAM;YACxB,OAAO;gBACL,KAAK,KAAA,CAAM,IAAA,CAAK,IAAI;YACtB;QACF;QAEA,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAK;YACvC,KAAK,MAAA,CAAO,IAAA,CAAK;gBACf,MAAM,OAAA,CAAQ,CAAC,CAAA;gBACf,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAC;gBACpC,QAAQ;gBACR,OAAO,KAAK,KAAA,CAAM,CAAC,CAAA;YACrB,CAAC;QACH;QAEA,KAAA,MAAW,OAAO,KAAM;YACtB,KAAK,IAAA,CAAK,IAAA,CAAK,WAAW,KAAK,KAAK,MAAA,CAAO,MAAM,EAAE,GAAA,CAAI,CAAC,MAAM,MAAM;gBAClE,OAAO;oBACL,MAAM;oBACN,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAI;oBAC9B,QAAQ;oBACR,OAAO,KAAK,KAAA,CAAM,CAAC,CAAA;gBACrB;YACF,CAAC,CAAC;QACJ;QAEA,OAAO;IACT;IAEA,SAAS,GAAA,EAAyC;QAChD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,GAAG;QAC9C,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,MAAM,MAAM,IAAI;gBACtC,MAAM,GAAA,CAAI,CAAC,CAAA;gBACX,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,CAAC,CAAC;YAClC;QACF;IACF;IAEA,UAAU,GAAA,EAA2C;QACnD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,SAAA,CAAU,IAAA,CAAK,GAAG;QAC/C,IAAI,KAAK;YACP,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,CAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,GAAS,CAAC,MAAM,OAC9C,GAAA,CAAI,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,IAClB,GAAA,CAAI,CAAC,CAAA;YACT,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV;gBACA,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAI;YAChC;QACF;IACF;IAEA,KAAK,GAAA,EAAsC;QACzC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,GAAG;QAC1C,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,MAAM,GAAA,CAAI,CAAC,CAAA;gBACX,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,CAAC,CAAC;YAClC;QACF;IACF;IAEA,OAAO,GAAA,EAAwC;QAC7C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,GAAG;QAC7C,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,MAAM,GAAA,CAAI,CAAC,CAAA;YACb;QACF;IACF;IAEA,IAAI,GAAA,EAAqC;QACvC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,GAAG;QAC1C,IAAI,KAAK;YACP,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA,IAAU,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,SAAA,CAAU,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAG;gBACvE,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA,GAAS;YAC5B,OAAA,IAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA,IAAU,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,OAAA,CAAQ,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAG;gBAC3E,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA,GAAS;YAC5B;YACA,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,IAAc,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAG;gBACnF,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,GAAa;YAChC,OAAA,IAAW,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,IAAc,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAG;gBACvF,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA,GAAa;YAChC;YAEA,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,QAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,MAAA;gBACzB,YAAY,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA;gBAC7B,OAAO;gBACP,MAAM,GAAA,CAAI,CAAC,CAAA;YACb;QACF;IACF;IAEA,KAAK,GAAA,EAAqD;QACxD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,GAAG;QAC3C,IAAI,KAAK;YACP,MAAM,aAAa,GAAA,CAAI,CAAC,CAAA,CAAE,IAAA,CAAK;YAC/B,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,QAAA,IAAY,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAK,UAAU,GAAG;gBAEjF,IAAI,CAAE,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,UAAU,GAAI;oBACxD;gBACF;gBAGA,MAAM,aAAa,MAAM,WAAW,KAAA,CAAM,GAAG,CAAA,CAAE,GAAG,IAAI;gBACtD,IAAA,CAAK,WAAW,MAAA,GAAS,WAAW,MAAA,IAAU,MAAM,GAAG;oBACrD;gBACF;YACF,OAAO;gBAEL,MAAM,iBAAiB,mBAAmB,GAAA,CAAI,CAAC,CAAA,EAAG,IAAI;gBACtD,IAAI,mBAAmB,CAAA,GAAI;oBAEzB;gBACF;gBAEA,IAAI,iBAAiB,CAAA,GAAI;oBACvB,MAAM,QAAQ,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,GAAG,MAAM,IAAI,IAAI;oBAC9C,MAAM,UAAU,QAAQ,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,GAAS;oBACxC,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,SAAA,CAAU,GAAG,cAAc;oBAC3C,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,SAAA,CAAU,GAAG,OAAO,EAAE,IAAA,CAAK;oBAC3C,GAAA,CAAI,CAAC,CAAA,GAAI;gBACX;YACF;YACA,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA;YAChB,IAAI,QAAQ;YACZ,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;gBAEzB,MAAMJ,QAAO,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAK,IAAI;gBAEzD,IAAIA,OAAM;oBACR,OAAOA,KAAAA,CAAK,CAAC,CAAA;oBACb,QAAQA,KAAAA,CAAK,CAAC,CAAA;gBAChB;YACF,OAAO;gBACL,QAAQ,GAAA,CAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI;YACzC;YAEA,OAAO,KAAK,IAAA,CAAK;YACjB,IAAI,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAK,IAAI,GAAG;gBACjD,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,IAAY,CAAE,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,UAAU,GAAI;oBAEjF,OAAO,KAAK,KAAA,CAAM,CAAC;gBACrB,OAAO;oBACL,OAAO,KAAK,KAAA,CAAM,GAAG,CAAA,CAAE;gBACzB;YACF;YACA,OAAO,WAAW,KAAK;gBACrB,MAAM,OAAO,KAAK,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,EAAgB,IAAI,IAAI;gBACpE,OAAO,QAAQ,MAAM,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,EAAgB,IAAI,IAAI;YACzE,GAAG,GAAA,CAAI,CAAC,CAAA,EAAG,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;QACnC;IACF;IAEA,QAAQ,GAAA,EAAa,KAAA,EAAoE;QACvF,IAAI;QACJ,IAAA,CAAK,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,OAAA,CAAQ,IAAA,CAAK,GAAG,CAAA,KAAA,CACvC,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,GAAG,CAAA,GAAI;YAC/C,MAAM,aAAA,CAAc,GAAA,CAAI,CAAC,CAAA,IAAK,GAAA,CAAI,CAAC,CAAA,EAAG,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,mBAAA,EAAqB,GAAG;YACvF,MAAMA,QAAO,KAAA,CAAM,WAAW,WAAA,CAAY,CAAC,CAAA;YAC3C,IAAI,CAACA,OAAM;gBACT,MAAM,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC;gBAC5B,OAAO;oBACL,MAAM;oBACN,KAAK;oBACL;gBACF;YACF;YACA,OAAO,WAAW,KAAKA,OAAM,GAAA,CAAI,CAAC,CAAA,EAAG,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;QAC7D;IACF;IAEA,SAAS,GAAA,EAAa,SAAA,EAAmB,WAAW,EAAA,EAA2C;QAC7F,IAAI,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,cAAA,CAAe,IAAA,CAAK,GAAG;QACrD,IAAI,CAAC,MAAO,CAAA;QAGZ,IAAI,KAAA,CAAM,CAAC,CAAA,IAAK,SAAS,KAAA,CAAM,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,mBAAmB,EAAG,CAAA;QAEtE,MAAM,WAAW,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,IAAK;QAEzC,IAAI,CAAC,YAAY,CAAC,YAAY,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,WAAA,CAAY,IAAA,CAAK,QAAQ,GAAG;YAE1E,MAAM,UAAU,CAAC;mBAAG,KAAA,CAAM,CAAC,CAAC;aAAA,CAAE,MAAA,GAAS;YACvC,IAAI,QAAQ,SAAS,aAAa,SAAS,gBAAgB;YAE3D,MAAM,SAAS,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAA,KAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,iBAAA,GAAoB,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,iBAAA;YAC7F,OAAO,SAAA,GAAY;YAGnB,YAAY,UAAU,KAAA,CAAM,CAAA,IAAK,IAAI,MAAA,GAAS,OAAO;YAErD,MAAA,CAAQ,QAAQ,OAAO,IAAA,CAAK,SAAS,CAAA,KAAM,KAAM;gBAC/C,SAAS,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA;gBAE5E,IAAI,CAAC,OAAQ,CAAA;gBAEb,UAAU,CAAC;uBAAG,MAAM;iBAAA,CAAE,MAAA;gBAEtB,IAAI,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,EAAG;oBACxB,cAAc;oBACd;gBACF,OAAA,IAAW,KAAA,CAAM,CAAC,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,EAAG;oBAC/B,IAAI,UAAU,KAAK,CAAA,CAAA,CAAG,UAAU,OAAA,IAAW,CAAA,GAAI;wBAC7C,iBAAiB;wBACjB;oBACF;gBACF;gBAEA,cAAc;gBAEd,IAAI,aAAa,EAAG,CAAA;gBAGpB,UAAU,KAAK,GAAA,CAAI,SAAS,UAAU,aAAa,aAAa;gBAEhE,MAAM,iBAAiB,CAAC;uBAAG,KAAA,CAAM,CAAC,CAAC;iBAAA,CAAE,CAAC,CAAA,CAAE,MAAA;gBACxC,MAAM,MAAM,IAAI,KAAA,CAAM,GAAG,UAAU,MAAM,KAAA,GAAQ,iBAAiB,OAAO;gBAGzE,IAAI,KAAK,GAAA,CAAI,SAAS,OAAO,IAAI,GAAG;oBAClC,MAAMK,QAAO,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE;oBAC5B,OAAO;wBACL,MAAM;wBACN;wBACA,MAAAA;wBACA,QAAQ,IAAA,CAAK,KAAA,CAAM,YAAA,CAAaA,KAAI;oBACtC;gBACF;gBAGA,MAAM,OAAO,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE;gBAC5B,OAAO;oBACL,MAAM;oBACN;oBACA;oBACA,QAAQ,IAAA,CAAK,KAAA,CAAM,YAAA,CAAa,IAAI;gBACtC;YACF;QACF;IACF;IAEA,SAAS,GAAA,EAA0C;QACjD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,GAAG;QAC3C,IAAI,KAAK;YACP,IAAI,OAAO,GAAA,CAAI,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,EAAmB,GAAG;YACjE,MAAM,mBAAmB,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,YAAA,CAAa,IAAA,CAAK,IAAI;YAChE,MAAM,0BAA0B,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,iBAAA,CAAkB,IAAA,CAAK,IAAI,KAAK,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,IAAI;YAC3H,IAAI,oBAAoB,yBAAyB;gBAC/C,OAAO,KAAK,SAAA,CAAU,GAAG,KAAK,MAAA,GAAS,CAAC;YAC1C;YACA,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV;YACF;QACF;IACF;IAEA,GAAG,GAAA,EAAoC;QACrC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,GAAG;QACzC,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;YACZ;QACF;IACF;IAEA,IAAI,GAAA,EAAqC;QACvC,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,GAAG;QAC1C,IAAI,KAAK;YACP,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,MAAM,GAAA,CAAI,CAAC,CAAA;gBACX,QAAQ,IAAA,CAAK,KAAA,CAAM,YAAA,CAAa,GAAA,CAAI,CAAC,CAAC;YACxC;QACF;IACF;IAEA,SAAS,GAAA,EAAsC;QAC7C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,GAAG;QAC/C,IAAI,KAAK;YACP,IAAI,MAAM;YACV,IAAI,GAAA,CAAI,CAAC,CAAA,KAAM,KAAK;gBAClB,OAAO,GAAA,CAAI,CAAC,CAAA;gBACZ,OAAO,YAAY;YACrB,OAAO;gBACL,OAAO,GAAA,CAAI,CAAC,CAAA;gBACZ,OAAO;YACT;YAEA,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV;gBACA;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,KAAK;wBACL;oBACF;iBACF;YACF;QACF;IACF;IAEA,IAAI,GAAA,EAAsC;QACxC,IAAI;QACJ,IAAI,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,GAAG,GAAG;YACzC,IAAI,MAAM;YACV,IAAI,GAAA,CAAI,CAAC,CAAA,KAAM,KAAK;gBAClB,OAAO,GAAA,CAAI,CAAC,CAAA;gBACZ,OAAO,YAAY;YACrB,OAAO;gBAEL,IAAI;gBACJ,GAAG;oBACD,cAAc,GAAA,CAAI,CAAC,CAAA;oBACnB,GAAA,CAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,UAAA,CAAW,IAAA,CAAK,GAAA,CAAI,CAAC,CAAC,GAAA,CAAI,CAAC,CAAA,IAAK;gBAC7D,QAAS,gBAAgB,GAAA,CAAI,CAAC,CAAA,CAAA;gBAC9B,OAAO,GAAA,CAAI,CAAC,CAAA;gBACZ,IAAI,GAAA,CAAI,CAAC,CAAA,KAAM,QAAQ;oBACrB,OAAO,YAAY,GAAA,CAAI,CAAC,CAAA;gBAC1B,OAAO;oBACL,OAAO,GAAA,CAAI,CAAC,CAAA;gBACd;YACF;YACA,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV;gBACA;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,KAAK;wBACL;oBACF;iBACF;YACF;QACF;IACF;IAEA,WAAW,GAAA,EAAsC;QAC/C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,GAAG;QAC3C,IAAI,KAAK;YACP,MAAM,UAAU,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM,UAAA;YACjC,OAAO;gBACL,MAAM;gBACN,KAAK,GAAA,CAAI,CAAC,CAAA;gBACV,MAAM,GAAA,CAAI,CAAC,CAAA;gBACX;YACF;QACF;IACF;AACF;;ACn2BO,IAAM,SAAN,MAAM,QAAO;IAClB,OAAA;IACA,QAAA;IACA,MAAA;IAMQ,UAAA;IACA,YAAA;IAER,YAAYC,QAAAA,CAAyB;QAEnC,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA;QACf,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI;QACtC,IAAA,CAAK,OAAA,GAAUA,YAAW;QAC1B,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,IAAA,CAAK,OAAA,CAAQ,SAAA,IAAa,IAAI,WAAW;QAClE,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,OAAA,CAAQ,SAAA;QAC9B,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU,IAAA,CAAK,OAAA;QAC9B,IAAA,CAAK,SAAA,CAAU,KAAA,GAAQ,IAAA;QACvB,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA;QACpB,IAAA,CAAK,KAAA,GAAQ;YACX,QAAQ;YACR,YAAY;YACZ,KAAK;QACP;QAEA,MAAM,QAAQ;YACZ;YACA,OAAO,MAAM,MAAA;YACb,QAAQ,OAAO,MAAA;QACjB;QAEA,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;YACzB,MAAM,KAAA,GAAQ,MAAM,QAAA;YACpB,MAAM,MAAA,GAAS,OAAO,QAAA;QACxB,OAAA,IAAW,IAAA,CAAK,OAAA,CAAQ,GAAA,EAAK;YAC3B,MAAM,KAAA,GAAQ,MAAM,GAAA;YACpB,IAAI,IAAA,CAAK,OAAA,CAAQ,MAAA,EAAQ;gBACvB,MAAM,MAAA,GAAS,OAAO,MAAA;YACxB,OAAO;gBACL,MAAM,MAAA,GAAS,OAAO,GAAA;YACxB;QACF;QACA,IAAA,CAAK,SAAA,CAAU,KAAA,GAAQ;IACzB;IAAA;;GAAA,GAKA,WAAW,QAAQ;QACjB,OAAO;YACL;YACA;QACF;IACF;IAAA;;GAAA,GAKA,OAAO,IAAI,GAAA,EAAaA,QAAAA,EAAyB;QAC/C,MAAMC,SAAQ,IAAI,QAAOD,QAAO;QAChC,OAAOC,OAAM,GAAA,CAAI,GAAG;IACtB;IAAA;;GAAA,GAKA,OAAO,UAAU,GAAA,EAAaD,QAAAA,EAAyB;QACrD,MAAMC,SAAQ,IAAI,QAAOD,QAAO;QAChC,OAAOC,OAAM,YAAA,CAAa,GAAG;IAC/B;IAAA;;GAAA,GAKA,IAAI,GAAA,EAAa;QACf,MAAM,IAAI,OAAA,CAAQ,MAAM,cAAA,EAAgB,IAAI;QAE5C,IAAA,CAAK,WAAA,CAAY,KAAK,IAAA,CAAK,MAAM;QAEjC,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,WAAA,CAAY,MAAA,EAAQ,IAAK;YAChD,MAAM,OAAO,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA;YAC/B,IAAA,CAAK,YAAA,CAAa,KAAK,GAAA,EAAK,KAAK,MAAM;QACzC;QACA,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA;QAEpB,OAAO,IAAA,CAAK,MAAA;IACd;IAOA,YAAY,GAAA,EAAa,SAAkB,CAAC,CAAA,EAAG,uBAAuB,KAAA,EAAO;QAC3E,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;YACzB,MAAM,IAAI,OAAA,CAAQ,MAAM,aAAA,EAAe,MAAM,EAAE,OAAA,CAAQ,MAAM,SAAA,EAAW,EAAE;QAC5E;QAEA,MAAO,IAAK;YACV,IAAI;YAEJ,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,OAAO,KAAK,CAAC,iBAAiB;gBACzD,IAAI,QAAQ,aAAa,IAAA,CAAK;oBAAE,OAAO,IAAA;gBAAK,GAAG,KAAK,MAAM,GAAG;oBAC3D,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;oBACpC,OAAO,IAAA,CAAK,KAAK;oBACjB,OAAO;gBACT;gBACA,OAAO;YACT,CAAC,GAAG;gBACF;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,GAAG,GAAG;gBACrC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,MAAM,GAAA,CAAI,MAAA,KAAW,KAAK,cAAc,KAAA,GAAW;oBAGrD,UAAU,GAAA,IAAO;gBACnB,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG;gBACpC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAE9B,IAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;oBACjE,UAAU,GAAA,IAAO,OAAO,MAAM,GAAA;oBAC9B,UAAU,IAAA,IAAQ,OAAO,MAAM,IAAA;oBAC/B,IAAA,CAAK,WAAA,CAAY,EAAA,CAAG,CAAA,CAAE,EAAG,GAAA,GAAM,UAAU,IAAA;gBAC3C,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,GAAG,GAAG;gBACtC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,GAAG,GAAG;gBACvC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,EAAA,CAAG,GAAG,GAAG;gBAClC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,GAAG,GAAG;gBAC1C,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG;gBACpC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG;gBACpC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG;gBACnC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,WAAW,SAAS,eAAe,WAAW,SAAS,QAAQ;oBACjE,UAAU,GAAA,IAAO,OAAO,MAAM,GAAA;oBAC9B,UAAU,IAAA,IAAQ,OAAO,MAAM,GAAA;oBAC/B,IAAA,CAAK,WAAA,CAAY,EAAA,CAAG,CAAA,CAAE,EAAG,GAAA,GAAM,UAAU,IAAA;gBAC3C,OAAA,IAAW,CAAC,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,MAAM,GAAG,CAAA,EAAG;oBACxC,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,MAAM,GAAG,CAAA,GAAI;wBAC7B,MAAM,MAAM,IAAA;wBACZ,OAAO,MAAM,KAAA;oBACf;gBACF;gBACA;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,GAAG,GAAG;gBACrC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,GAAG,GAAG;gBACxC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAIA,IAAI,SAAS;YACb,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,YAAY;gBACvC,IAAI,aAAa;gBACjB,MAAM,UAAU,IAAI,KAAA,CAAM,CAAC;gBAC3B,IAAI;gBACJ,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,UAAA,CAAW,OAAA,CAAQ,CAAC,kBAAkB;oBAC5D,YAAY,cAAc,IAAA,CAAK;wBAAE,OAAO,IAAA;oBAAK,GAAG,OAAO;oBACvD,IAAI,OAAO,cAAc,YAAY,aAAa,GAAG;wBACnD,aAAa,KAAK,GAAA,CAAI,YAAY,SAAS;oBAC7C;gBACF,CAAC;gBACD,IAAI,aAAa,YAAY,cAAc,GAAG;oBAC5C,SAAS,IAAI,SAAA,CAAU,GAAG,aAAa,CAAC;gBAC1C;YACF;YACA,IAAI,IAAA,CAAK,KAAA,CAAM,GAAA,IAAA,CAAQ,QAAQ,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,MAAM,CAAA,GAAI;gBAChE,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,wBAAwB,WAAW,SAAS,aAAa;oBAC3D,UAAU,GAAA,IAAO,OAAO,MAAM,GAAA;oBAC9B,UAAU,IAAA,IAAQ,OAAO,MAAM,IAAA;oBAC/B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI;oBACrB,IAAA,CAAK,WAAA,CAAY,EAAA,CAAG,CAAA,CAAE,EAAG,GAAA,GAAM,UAAU,IAAA;gBAC3C,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA,uBAAuB,OAAO,MAAA,KAAW,IAAI,MAAA;gBAC7C,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG;gBACpC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,WAAW,SAAS,QAAQ;oBAC9B,UAAU,GAAA,IAAO,OAAO,MAAM,GAAA;oBAC9B,UAAU,IAAA,IAAQ,OAAO,MAAM,IAAA;oBAC/B,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI;oBACrB,IAAA,CAAK,WAAA,CAAY,EAAA,CAAG,CAAA,CAAE,EAAG,GAAA,GAAM,UAAU,IAAA;gBAC3C,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA;YACF;YAEA,IAAI,KAAK;gBACP,MAAM,SAAS,4BAA4B,IAAI,UAAA,CAAW,CAAC;gBAC3D,IAAI,IAAA,CAAK,OAAA,CAAQ,MAAA,EAAQ;oBACvB,QAAQ,KAAA,CAAM,MAAM;oBACpB;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,MAAM;gBACxB;YACF;QACF;QAEA,IAAA,CAAK,KAAA,CAAM,GAAA,GAAM;QACjB,OAAO;IACT;IAEA,OAAO,GAAA,EAAa,SAAkB,CAAC,CAAA,EAAG;QACxC,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK;YAAE;YAAK;QAAO,CAAC;QACrC,OAAO;IACT;IAAA;;GAAA,GAKA,aAAa,GAAA,EAAa,SAAkB,CAAC,CAAA,EAAY;QAEvD,IAAI,YAAY;QAChB,IAAI,QAAgC;QAGpC,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACrB,MAAM,QAAQ,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,KAAK;YAC3C,IAAI,MAAM,MAAA,GAAS,GAAG;gBACpB,MAAA,CAAQ,QAAQ,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,aAAA,CAAc,IAAA,CAAK,SAAS,CAAA,KAAM,KAAM;oBAClF,IAAI,MAAM,QAAA,CAAS,KAAA,CAAM,CAAC,CAAA,CAAE,KAAA,CAAM,KAAA,CAAM,CAAC,CAAA,CAAE,WAAA,CAAY,GAAG,IAAI,GAAG,CAAA,CAAE,CAAC,GAAG;wBACrE,YAAY,UAAU,KAAA,CAAM,GAAG,MAAM,KAAK,IACtC,MAAM,IAAI,MAAA,CAAO,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,GAAS,CAAC,IAAI,MACxC,UAAU,KAAA,CAAM,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,aAAA,CAAc,SAAS;oBACzE;gBACF;YACF;QACF;QAGA,MAAA,CAAQ,QAAQ,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,cAAA,CAAe,IAAA,CAAK,SAAS,CAAA,KAAM,KAAM;YACnF,YAAY,UAAU,KAAA,CAAM,GAAG,MAAM,KAAK,IAAI,OAAO,UAAU,KAAA,CAAM,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,cAAA,CAAe,SAAS;QAC3H;QAGA,MAAA,CAAQ,QAAQ,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,SAAA,CAAU,IAAA,CAAK,SAAS,CAAA,KAAM,KAAM;YAC9E,YAAY,UAAU,KAAA,CAAM,GAAG,MAAM,KAAK,IAAI,MAAM,IAAI,MAAA,CAAO,KAAA,CAAM,CAAC,CAAA,CAAE,MAAA,GAAS,CAAC,IAAI,MAAM,UAAU,KAAA,CAAM,IAAA,CAAK,SAAA,CAAU,KAAA,CAAM,MAAA,CAAO,SAAA,CAAU,SAAS;QAC7J;QAEA,IAAI,eAAe;QACnB,IAAI,WAAW;QACf,MAAO,IAAK;YACV,IAAI,CAAC,cAAc;gBACjB,WAAW;YACb;YACA,eAAe;YAEf,IAAI;YAGJ,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,QAAQ,KAAK,CAAC,iBAAiB;gBAC1D,IAAI,QAAQ,aAAa,IAAA,CAAK;oBAAE,OAAO,IAAA;gBAAK,GAAG,KAAK,MAAM,GAAG;oBAC3D,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;oBACpC,OAAO,IAAA,CAAK,KAAK;oBACjB,OAAO;gBACT;gBACA,OAAO;YACT,CAAC,GAAG;gBACF;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,GAAG,GAAG;gBACtC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG;gBACnC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,GAAG,GAAG;gBACpC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,KAAK,IAAA,CAAK,MAAA,CAAO,KAAK,GAAG;gBAC1D,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,MAAM,IAAA,KAAS,UAAU,WAAW,SAAS,QAAQ;oBACvD,UAAU,GAAA,IAAO,MAAM,GAAA;oBACvB,UAAU,IAAA,IAAQ,MAAM,IAAA;gBAC1B,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,KAAK,WAAW,QAAQ,GAAG;gBAC7D,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,GAAG,GAAG;gBACxC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,EAAA,CAAG,GAAG,GAAG;gBAClC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG;gBACnC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,GAAG,GAAG;gBACxC,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAGA,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,MAAA,IAAA,CAAW,QAAQ,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,CAAA,GAAI;gBAC3D,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,OAAO,IAAA,CAAK,KAAK;gBACjB;YACF;YAIA,IAAI,SAAS;YACb,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,aAAa;gBACxC,IAAI,aAAa;gBACjB,MAAM,UAAU,IAAI,KAAA,CAAM,CAAC;gBAC3B,IAAI;gBACJ,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,WAAA,CAAY,OAAA,CAAQ,CAAC,kBAAkB;oBAC7D,YAAY,cAAc,IAAA,CAAK;wBAAE,OAAO,IAAA;oBAAK,GAAG,OAAO;oBACvD,IAAI,OAAO,cAAc,YAAY,aAAa,GAAG;wBACnD,aAAa,KAAK,GAAA,CAAI,YAAY,SAAS;oBAC7C;gBACF,CAAC;gBACD,IAAI,aAAa,YAAY,cAAc,GAAG;oBAC5C,SAAS,IAAI,SAAA,CAAU,GAAG,aAAa,CAAC;gBAC1C;YACF;YACA,IAAI,QAAQ,IAAA,CAAK,SAAA,CAAU,UAAA,CAAW,MAAM,GAAG;gBAC7C,MAAM,IAAI,SAAA,CAAU,MAAM,GAAA,CAAI,MAAM;gBACpC,IAAI,MAAM,GAAA,CAAI,KAAA,CAAM,CAAA,CAAE,MAAM,KAAK;oBAC/B,WAAW,MAAM,GAAA,CAAI,KAAA,CAAM,CAAA,CAAE;gBAC/B;gBACA,eAAe;gBACf,MAAM,YAAY,OAAO,EAAA,CAAG,CAAA,CAAE;gBAC9B,IAAI,WAAW,SAAS,QAAQ;oBAC9B,UAAU,GAAA,IAAO,MAAM,GAAA;oBACvB,UAAU,IAAA,IAAQ,MAAM,IAAA;gBAC1B,OAAO;oBACL,OAAO,IAAA,CAAK,KAAK;gBACnB;gBACA;YACF;YAEA,IAAI,KAAK;gBACP,MAAM,SAAS,4BAA4B,IAAI,UAAA,CAAW,CAAC;gBAC3D,IAAI,IAAA,CAAK,OAAA,CAAQ,MAAA,EAAQ;oBACvB,QAAQ,KAAA,CAAM,MAAM;oBACpB;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,MAAM;gBACxB;YACF;QACF;QAEA,OAAO;IACT;AACF;;ACxcO,IAAM,YAAN,MAAgB;IACrB,QAAA;IACA,OAAA;IAAA,oBAAA;IACA,YAAYC,QAAAA,CAAyB;QACnC,IAAA,CAAK,OAAA,GAAUA,YAAW;IAC5B;IAEA,MAAM,KAAA,EAA6B;QACjC,OAAO;IACT;IAEA,KAAK,EAAE,IAAA,EAAM,IAAA,EAAM,OAAA,CAAQ,CAAA,EAAwB;QACjD,MAAM,aAAA,CAAc,QAAQ,EAAA,EAAI,KAAA,CAAM,MAAM,aAAa,GAAA,CAAI,CAAC,CAAA;QAE9D,MAAM,OAAO,KAAK,OAAA,CAAQ,MAAM,aAAA,EAAe,EAAE,IAAI;QAErD,IAAI,CAAC,YAAY;YACf,OAAO,gBAAA,CACF,UAAU,OAAOC,QAAO,MAAM,IAAI,CAAA,IACnC;QACN;QAEA,OAAO,gCACHA,QAAO,UAAU,IACjB,OAAA,CACC,UAAU,OAAOA,QAAO,MAAM,IAAI,CAAA,IACnC;IACN;IAEA,WAAW,EAAE,MAAA,CAAO,CAAA,EAA8B;QAChD,MAAM,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,MAAM;QACrC,OAAO,CAAA;AAAA,EAAiB,IAAI,CAAA;AAAA,CAAA;IAC9B;IAEA,KAAK,EAAE,IAAA,CAAK,CAAA,EAAsC;QAChD,OAAO;IACT;IAEA,QAAQ,EAAE,MAAA,EAAQ,KAAA,CAAM,CAAA,EAA2B;QACjD,OAAO,CAAA,EAAA,EAAK,KAAK,CAAA,CAAA,EAAI,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,CAAC,CAAA,GAAA,EAAM,KAAK,CAAA;AAAA,CAAA;IACjE;IAEA,GAAG,KAAA,EAA0B;QAC3B,OAAO;IACT;IAEA,KAAK,KAAA,EAA4B;QAC/B,MAAM,UAAU,MAAM,OAAA;QACtB,MAAM,QAAQ,MAAM,KAAA;QAEpB,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,KAAA,CAAM,MAAA,EAAQ,IAAK;YAC3C,MAAM,OAAO,MAAM,KAAA,CAAM,CAAC,CAAA;YAC1B,QAAQ,IAAA,CAAK,QAAA,CAAS,IAAI;QAC5B;QAEA,MAAM,OAAO,UAAU,OAAO;QAC9B,MAAM,YAAa,WAAW,UAAU,IAAM,aAAa,QAAQ,MAAO;QAC1E,OAAO,MAAM,OAAO,YAAY,QAAQ,OAAO,OAAO,OAAO;IAC/D;IAEA,SAAS,IAAA,EAA+B;QACtC,IAAI,WAAW;QACf,IAAI,KAAK,IAAA,EAAM;YACb,MAAM,WAAW,IAAA,CAAK,QAAA,CAAS;gBAAE,SAAS,CAAC,CAAC,KAAK,OAAA;YAAQ,CAAC;YAC1D,IAAI,KAAK,KAAA,EAAO;gBACd,IAAI,KAAK,MAAA,CAAO,CAAC,CAAA,EAAG,SAAS,aAAa;oBACxC,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,GAAO,WAAW,MAAM,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA;oBACtD,IAAI,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,IAAU,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,MAAA,GAAS,KAAK,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,KAAS,QAAQ;wBACzG,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAA,GAAO,WAAW,MAAMA,QAAO,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,CAAA,CAAE,IAAI;wBACrF,KAAK,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,GAAU;oBACrC;gBACF,OAAO;oBACL,KAAK,MAAA,CAAO,OAAA,CAAQ;wBAClB,MAAM;wBACN,KAAK,WAAW;wBAChB,MAAM,WAAW;wBACjB,SAAS;oBACX,CAAC;gBACH;YACF,OAAO;gBACL,YAAY,WAAW;YACzB;QACF;QAEA,YAAY,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,KAAK,MAAA,EAAQ,CAAC,CAAC,KAAK,KAAK;QAEvD,OAAO,CAAA,IAAA,EAAO,QAAQ,CAAA;AAAA,CAAA;IACxB;IAEA,SAAS,EAAE,OAAA,CAAQ,CAAA,EAA4B;QAC7C,OAAO,YAAA,CACF,UAAU,gBAAgB,EAAA,IAC3B;IACN;IAEA,UAAU,EAAE,MAAA,CAAO,CAAA,EAA6B;QAC9C,OAAO,CAAA,GAAA,EAAM,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,CAAC,CAAA;AAAA,CAAA;IAC9C;IAEA,MAAM,KAAA,EAA6B;QACjC,IAAI,SAAS;QAGb,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,CAAO,MAAA,EAAQ,IAAK;YAC5C,QAAQ,IAAA,CAAK,SAAA,CAAU,MAAM,MAAA,CAAO,CAAC,CAAC;QACxC;QACA,UAAU,IAAA,CAAK,QAAA,CAAS;YAAE,MAAM;QAAK,CAAC;QAEtC,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,IAAA,CAAK,MAAA,EAAQ,IAAK;YAC1C,MAAM,MAAM,MAAM,IAAA,CAAK,CAAC,CAAA;YAExB,OAAO;YACP,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,MAAA,EAAQ,IAAK;gBACnC,QAAQ,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,CAAC,CAAC;YAC/B;YAEA,QAAQ,IAAA,CAAK,QAAA,CAAS;gBAAE,MAAM;YAAK,CAAC;QACtC;QACA,IAAI,KAAM,CAAA,OAAO,CAAA,OAAA,EAAU,IAAI,CAAA,QAAA,CAAA;QAE/B,OAAO,uBAEH,SACA,eACA,OACA;IACN;IAEA,SAAS,EAAE,IAAA,CAAK,CAAA,EAA4B;QAC1C,OAAO,CAAA;AAAA,EAAS,IAAI,CAAA;AAAA,CAAA;IACtB;IAEA,UAAU,KAAA,EAAiC;QACzC,MAAM,UAAU,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,MAAM;QACpD,MAAM,OAAO,MAAM,MAAA,GAAS,OAAO;QACnC,MAAMC,OAAM,MAAM,KAAA,GACd,CAAA,CAAA,EAAI,IAAI,CAAA,QAAA,EAAW,MAAM,KAAK,CAAA,EAAA,CAAA,GAC9B,CAAA,CAAA,EAAI,IAAI,CAAA,CAAA,CAAA;QACZ,OAAOA,OAAM,UAAU,CAAA,EAAA,EAAK,IAAI,CAAA;AAAA,CAAA;IAClC;IAAA;;GAAA,GAKA,OAAO,EAAE,MAAA,CAAO,CAAA,EAA0B;QACxC,OAAO,CAAA,QAAA,EAAW,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,CAAC,CAAA,SAAA,CAAA;IACnD;IAEA,GAAG,EAAE,MAAA,CAAO,CAAA,EAAsB;QAChC,OAAO,CAAA,IAAA,EAAO,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,CAAC,CAAA,KAAA,CAAA;IAC/C;IAEA,SAAS,EAAE,IAAA,CAAK,CAAA,EAA4B;QAC1C,OAAO,CAAA,MAAA,EAASD,QAAO,MAAM,IAAI,CAAC,CAAA,OAAA,CAAA;IACpC;IAEA,GAAG,KAAA,EAA0B;QAC3B,OAAO;IACT;IAEA,IAAI,EAAE,MAAA,CAAO,CAAA,EAAuB;QAClC,OAAO,CAAA,KAAA,EAAQ,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,CAAC,CAAA,MAAA,CAAA;IAChD;IAEA,KAAK,EAAE,IAAA,EAAM,KAAA,EAAO,MAAA,CAAO,CAAA,EAAwB;QACjD,MAAM,OAAO,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM;QAC3C,MAAM,YAAY,SAAS,IAAI;QAC/B,IAAI,cAAc,MAAM;YACtB,OAAO;QACT;QACA,OAAO;QACP,IAAI,MAAM,cAAc,OAAO;QAC/B,IAAI,OAAO;YACT,OAAO,aAAcA,QAAO,KAAK,IAAK;QACxC;QACA,OAAO,MAAM,OAAO;QACpB,OAAO;IACT;IAEA,MAAM,EAAE,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,MAAA,CAAO,CAAA,EAAyB;QACzD,IAAI,QAAQ;YACV,OAAO,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,QAAQ,IAAA,CAAK,MAAA,CAAO,YAAY;QACjE;QACA,MAAM,YAAY,SAAS,IAAI;QAC/B,IAAI,cAAc,MAAM;YACtB,OAAOA,QAAO,IAAI;QACpB;QACA,OAAO;QAEP,IAAI,MAAM,CAAA,UAAA,EAAa,IAAI,CAAA,OAAA,EAAU,IAAI,CAAA,CAAA,CAAA;QACzC,IAAI,OAAO;YACT,OAAO,CAAA,QAAA,EAAWA,QAAO,KAAK,CAAC,CAAA,CAAA,CAAA;QACjC;QACA,OAAO;QACP,OAAO;IACT;IAEA,KAAK,KAAA,EAA6C;QAChD,OAAO,YAAY,SAAS,MAAM,MAAA,GAC9B,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,MAAM,MAAM,IACnC,aAAa,SAAS,MAAM,OAAA,GAAU,MAAM,IAAA,GAAOA,QAAO,MAAM,IAAI;IAC3E;AACF;;ACpNO,IAAM,gBAAN,MAAoB;IAAA,oCAAA;IAEzB,OAAO,EAAE,IAAA,CAAK,CAAA,EAAkB;QAC9B,OAAO;IACT;IAEA,GAAG,EAAE,IAAA,CAAK,CAAA,EAAc;QACtB,OAAO;IACT;IAEA,SAAS,EAAE,IAAA,CAAK,CAAA,EAAoB;QAClC,OAAO;IACT;IAEA,IAAI,EAAE,IAAA,CAAK,CAAA,EAAe;QACxB,OAAO;IACT;IAEA,KAAK,EAAE,IAAA,CAAK,CAAA,EAA6B;QACvC,OAAO;IACT;IAEA,KAAK,EAAE,IAAA,CAAK,CAAA,EAA6C;QACvD,OAAO;IACT;IAEA,KAAK,EAAE,IAAA,CAAK,CAAA,EAAgB;QAC1B,OAAO,KAAK;IACd;IAEA,MAAM,EAAE,IAAA,CAAK,CAAA,EAAiB;QAC5B,OAAO,KAAK;IACd;IAEA,KAAK;QACH,OAAO;IACT;AACF;;AClCO,IAAM,UAAN,MAAM,SAAQ;IACnB,QAAA;IACA,SAAA;IACA,aAAA;IACA,YAAYE,QAAAA,CAAyB;QACnC,IAAA,CAAK,OAAA,GAAUA,YAAW;QAC1B,IAAA,CAAK,OAAA,CAAQ,QAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,QAAA,IAAY,IAAI,UAAU;QAC/D,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,QAAA;QAC7B,IAAA,CAAK,QAAA,CAAS,OAAA,GAAU,IAAA,CAAK,OAAA;QAC7B,IAAA,CAAK,QAAA,CAAS,MAAA,GAAS,IAAA;QACvB,IAAA,CAAK,YAAA,GAAe,IAAI,cAAc;IACxC;IAAA;;GAAA,GAKA,OAAO,MAAM,MAAA,EAAiBA,QAAAA,EAAyB;QACrD,MAAMC,UAAS,IAAI,SAAQD,QAAO;QAClC,OAAOC,QAAO,KAAA,CAAM,MAAM;IAC5B;IAAA;;GAAA,GAKA,OAAO,YAAY,MAAA,EAAiBD,QAAAA,EAAyB;QAC3D,MAAMC,UAAS,IAAI,SAAQD,QAAO;QAClC,OAAOC,QAAO,WAAA,CAAY,MAAM;IAClC;IAAA;;GAAA,GAKA,MAAM,MAAA,EAAiB,MAAM,IAAA,EAAc;QACzC,IAAI,MAAM;QAEV,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;YACtC,MAAM,WAAW,MAAA,CAAO,CAAC,CAAA;YAGzB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,WAAA,CAAY,SAAS,IAAI,CAAA,EAAG;gBACvD,MAAM,eAAe;gBACrB,MAAM,MAAM,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,SAAA,CAAU,aAAa,IAAI,CAAA,CAAE,IAAA,CAAK;oBAAE,QAAQ,IAAA;gBAAK,GAAG,YAAY;gBACpG,IAAI,QAAQ,SAAS,CAAC;oBAAC;oBAAS;oBAAM;oBAAW;oBAAQ;oBAAS;oBAAc;oBAAQ;oBAAQ;oBAAa,MAAM;iBAAA,CAAE,QAAA,CAAS,aAAa,IAAI,GAAG;oBAChJ,OAAO,OAAO;oBACd;gBACF;YACF;YAEA,MAAM,QAAQ;YAEd,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;oBAAS;wBACZ,OAAO,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,KAAK;wBAChC;oBACF;gBACA,KAAK;oBAAM;wBACT,OAAO,IAAA,CAAK,QAAA,CAAS,EAAA,CAAG,KAAK;wBAC7B;oBACF;gBACA,KAAK;oBAAW;wBACd,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAK;wBAClC;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,KAAK;wBAC/B;oBACF;gBACA,KAAK;oBAAS;wBACZ,OAAO,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,KAAK;wBAChC;oBACF;gBACA,KAAK;oBAAc;wBACjB,OAAO,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAK;wBACrC;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,KAAK;wBAC/B;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,KAAK;wBAC/B;oBACF;gBACA,KAAK;oBAAa;wBAChB,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAK;wBACpC;oBACF;gBACA,KAAK;oBAAQ;wBACX,IAAI,YAAY;wBAChB,IAAI,OAAO,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,SAAS;wBACvC,MAAO,IAAI,IAAI,OAAO,MAAA,IAAU,MAAA,CAAO,IAAI,CAAC,CAAA,CAAE,IAAA,KAAS,OAAQ;4BAC7D,YAAY,MAAA,CAAO,EAAE,CAAC,CAAA;4BACtB,QAAQ,OAAO,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,SAAS;wBAC7C;wBACA,IAAI,KAAK;4BACP,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU;gCAC7B,MAAM;gCACN,KAAK;gCACL,MAAM;gCACN,QAAQ;oCAAC;wCAAE,MAAM;wCAAQ,KAAK;wCAAM,MAAM;wCAAM,SAAS;oCAAK,CAAC;iCAAA;4BACjE,CAAC;wBACH,OAAO;4BACL,OAAO;wBACT;wBACA;oBACF;gBAEA;oBAAS;wBACP,MAAM,SAAS,iBAAiB,MAAM,IAAA,GAAO;wBAC7C,IAAI,IAAA,CAAK,OAAA,CAAQ,MAAA,EAAQ;4BACvB,QAAQ,KAAA,CAAM,MAAM;4BACpB,OAAO;wBACT,OAAO;4BACL,MAAM,IAAI,MAAM,MAAM;wBACxB;oBACF;YACF;QACF;QAEA,OAAO;IACT;IAAA;;GAAA,GAKA,YAAY,MAAA,EAAiB,WAAsC,IAAA,CAAK,QAAA,EAAkB;QACxF,IAAI,MAAM;QAEV,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;YACtC,MAAM,WAAW,MAAA,CAAO,CAAC,CAAA;YAGzB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,WAAA,CAAY,SAAS,IAAI,CAAA,EAAG;gBACvD,MAAM,MAAM,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,SAAA,CAAU,SAAS,IAAI,CAAA,CAAE,IAAA,CAAK;oBAAE,QAAQ,IAAA;gBAAK,GAAG,QAAQ;gBAC5F,IAAI,QAAQ,SAAS,CAAC;oBAAC;oBAAU;oBAAQ;oBAAQ;oBAAS;oBAAU;oBAAM;oBAAY;oBAAM;oBAAO,MAAM;iBAAA,CAAE,QAAA,CAAS,SAAS,IAAI,GAAG;oBAClI,OAAO,OAAO;oBACd;gBACF;YACF;YAEA,MAAM,QAAQ;YAEd,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;oBAAU;wBACb,OAAO,SAAS,IAAA,CAAK,KAAK;wBAC1B;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,SAAS,IAAA,CAAK,KAAK;wBAC1B;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,SAAS,IAAA,CAAK,KAAK;wBAC1B;oBACF;gBACA,KAAK;oBAAS;wBACZ,OAAO,SAAS,KAAA,CAAM,KAAK;wBAC3B;oBACF;gBACA,KAAK;oBAAU;wBACb,OAAO,SAAS,MAAA,CAAO,KAAK;wBAC5B;oBACF;gBACA,KAAK;oBAAM;wBACT,OAAO,SAAS,EAAA,CAAG,KAAK;wBACxB;oBACF;gBACA,KAAK;oBAAY;wBACf,OAAO,SAAS,QAAA,CAAS,KAAK;wBAC9B;oBACF;gBACA,KAAK;oBAAM;wBACT,OAAO,SAAS,EAAA,CAAG,KAAK;wBACxB;oBACF;gBACA,KAAK;oBAAO;wBACV,OAAO,SAAS,GAAA,CAAI,KAAK;wBACzB;oBACF;gBACA,KAAK;oBAAQ;wBACX,OAAO,SAAS,IAAA,CAAK,KAAK;wBAC1B;oBACF;gBACA;oBAAS;wBACP,MAAM,SAAS,iBAAiB,MAAM,IAAA,GAAO;wBAC7C,IAAI,IAAA,CAAK,OAAA,CAAQ,MAAA,EAAQ;4BACvB,QAAQ,KAAA,CAAM,MAAM;4BACpB,OAAO;wBACT,OAAO;4BACL,MAAM,IAAI,MAAM,MAAM;wBACxB;oBACF;YACF;QACF;QACA,OAAO;IACT;AACF;;ACvMO,IAAM,SAAN,MAAa;IAClB,QAAA;IACA,MAAA;IAEA,YAAYC,QAAAA,CAAyB;QACnC,IAAA,CAAK,OAAA,GAAUA,YAAW;IAC5B;IAEA,OAAO,mBAAmB,aAAA,GAAA,IAAI,IAAI;QAChC;QACA;QACA;KACD,EAAA;IAAA;;GAAA,GAKD,WAAW,QAAA,EAAkB;QAC3B,OAAO;IACT;IAAA;;GAAA,GAKA,YAAYC,KAAAA,EAAc;QACxB,OAAOA;IACT;IAAA;;GAAA,GAKA,iBAAiB,MAAA,EAA8B;QAC7C,OAAO;IACT;IAAA;;GAAA,GAKA,eAAe;QACb,OAAO,IAAA,CAAK,KAAA,GAAQ,OAAO,GAAA,GAAM,OAAO,SAAA;IAC1C;IAAA;;GAAA,GAKA,gBAAgB;QACd,OAAO,IAAA,CAAK,KAAA,GAAQ,QAAQ,KAAA,GAAQ,QAAQ,WAAA;IAC9C;AACF;;ACtCO,IAAM,SAAN,MAAa;IAClB,WAAW,aAAa,EAAA;IACxB,UAAU,IAAA,CAAK,UAAA,CAAA;IAEf,QAAQ,IAAA,CAAK,aAAA,CAAc,IAAI,EAAA;IAC/B,cAAc,IAAA,CAAK,aAAA,CAAc,KAAK,EAAA;IAEtC,SAAS,QAAA;IACT,WAAW,UAAA;IACX,eAAe,cAAA;IACf,QAAQ,OAAA;IACR,YAAY,WAAA;IACZ,QAAQ,OAAA;IAER,YAAA,GAAe,IAAA,CAAyB;QACtC,IAAA,CAAK,GAAA,CAAI,GAAG,IAAI;IAClB;IAAA;;GAAA,GAKA,WAAW,MAAA,EAA8B,QAAA,EAA2D;QAClG,IAAI,SAAyB,CAAC,CAAA;QAC9B,KAAA,MAAW,SAAS,OAAQ;YAC1B,SAAS,OAAO,MAAA,CAAO,SAAS,IAAA,CAAK,IAAA,EAAM,KAAK,CAAC;YACjD,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;oBAAS;wBACZ,MAAM,aAAa;wBACnB,KAAA,MAAW,QAAQ,WAAW,MAAA,CAAQ;4BACpC,SAAS,OAAO,MAAA,CAAO,IAAA,CAAK,UAAA,CAAW,KAAK,MAAA,EAAQ,QAAQ,CAAC;wBAC/D;wBACA,KAAA,MAAW,OAAO,WAAW,IAAA,CAAM;4BACjC,KAAA,MAAW,QAAQ,IAAK;gCACtB,SAAS,OAAO,MAAA,CAAO,IAAA,CAAK,UAAA,CAAW,KAAK,MAAA,EAAQ,QAAQ,CAAC;4BAC/D;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAQ;wBACX,MAAM,YAAY;wBAClB,SAAS,OAAO,MAAA,CAAO,IAAA,CAAK,UAAA,CAAW,UAAU,KAAA,EAAO,QAAQ,CAAC;wBACjE;oBACF;gBACA;oBAAS;wBACP,MAAM,eAAe;wBACrB,IAAI,IAAA,CAAK,QAAA,CAAS,UAAA,EAAY,aAAA,CAAc,aAAa,IAAI,CAAA,EAAG;4BAC9D,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,WAAA,CAAY,aAAa,IAAI,CAAA,CAAE,OAAA,CAAQ,CAAC,gBAAgB;gCAC/E,MAAMC,UAAS,YAAA,CAAa,WAAW,CAAA,CAAE,IAAA,CAAK,QAAQ;gCACtD,SAAS,OAAO,MAAA,CAAO,IAAA,CAAK,UAAA,CAAWA,SAAQ,QAAQ,CAAC;4BAC1D,CAAC;wBACH,OAAA,IAAW,aAAa,MAAA,EAAQ;4BAC9B,SAAS,OAAO,MAAA,CAAO,IAAA,CAAK,UAAA,CAAW,aAAa,MAAA,EAAQ,QAAQ,CAAC;wBACvE;oBACF;YACF;QACF;QACA,OAAO;IACT;IAEA,IAAA,GAAO,IAAA,EAAyB;QAC9B,MAAM,aAA0C,IAAA,CAAK,QAAA,CAAS,UAAA,IAAc;YAAE,WAAW,CAAC;YAAG,aAAa,CAAC;QAAE;QAE7G,KAAK,OAAA,CAAQ,CAAC,SAAS;YAErB,MAAM,OAAO;gBAAE,GAAG,IAAA;YAAK;YAGvB,KAAK,KAAA,GAAQ,IAAA,CAAK,QAAA,CAAS,KAAA,IAAS,KAAK,KAAA,IAAS;YAGlD,IAAI,KAAK,UAAA,EAAY;gBACnB,KAAK,UAAA,CAAW,OAAA,CAAQ,CAAC,QAAQ;oBAC/B,IAAI,CAAC,IAAI,IAAA,EAAM;wBACb,MAAM,IAAI,MAAM,yBAAyB;oBAC3C;oBACA,IAAI,cAAc,KAAK;wBACrB,MAAM,eAAe,WAAW,SAAA,CAAU,IAAI,IAAI,CAAA;wBAClD,IAAI,cAAc;4BAEhB,WAAW,SAAA,CAAU,IAAI,IAAI,CAAA,GAAI,SAAA,GAAYC,KAAAA,EAAM;gCACjD,IAAI,MAAM,IAAI,QAAA,CAAS,KAAA,CAAM,IAAA,EAAMA,KAAI;gCACvC,IAAI,QAAQ,OAAO;oCACjB,MAAM,aAAa,KAAA,CAAM,IAAA,EAAMA,KAAI;gCACrC;gCACA,OAAO;4BACT;wBACF,OAAO;4BACL,WAAW,SAAA,CAAU,IAAI,IAAI,CAAA,GAAI,IAAI,QAAA;wBACvC;oBACF;oBACA,IAAI,eAAe,KAAK;wBACtB,IAAI,CAAC,IAAI,KAAA,IAAU,IAAI,KAAA,KAAU,WAAW,IAAI,KAAA,KAAU,UAAW;4BACnE,MAAM,IAAI,MAAM,6CAA6C;wBAC/D;wBACA,MAAM,WAAW,UAAA,CAAW,IAAI,KAAK,CAAA;wBACrC,IAAI,UAAU;4BACZ,SAAS,OAAA,CAAQ,IAAI,SAAS;wBAChC,OAAO;4BACL,UAAA,CAAW,IAAI,KAAK,CAAA,GAAI;gCAAC,IAAI,SAAS;6BAAA;wBACxC;wBACA,IAAI,IAAI,KAAA,EAAO;4BACb,IAAI,IAAI,KAAA,KAAU,SAAS;gCACzB,IAAI,WAAW,UAAA,EAAY;oCACzB,WAAW,UAAA,CAAW,IAAA,CAAK,IAAI,KAAK;gCACtC,OAAO;oCACL,WAAW,UAAA,GAAa;wCAAC,IAAI,KAAK;qCAAA;gCACpC;4BACF,OAAA,IAAW,IAAI,KAAA,KAAU,UAAU;gCACjC,IAAI,WAAW,WAAA,EAAa;oCAC1B,WAAW,WAAA,CAAY,IAAA,CAAK,IAAI,KAAK;gCACvC,OAAO;oCACL,WAAW,WAAA,GAAc;wCAAC,IAAI,KAAK;qCAAA;gCACrC;4BACF;wBACF;oBACF;oBACA,IAAI,iBAAiB,OAAO,IAAI,WAAA,EAAa;wBAC3C,WAAW,WAAA,CAAY,IAAI,IAAI,CAAA,GAAI,IAAI,WAAA;oBACzC;gBACF,CAAC;gBACD,KAAK,UAAA,GAAa;YACpB;YAGA,IAAI,KAAK,QAAA,EAAU;gBACjB,MAAM,WAAW,IAAA,CAAK,QAAA,CAAS,QAAA,IAAY,IAAI,UAAU,IAAA,CAAK,QAAQ;gBACtE,IAAA,MAAW,QAAQ,KAAK,QAAA,CAAU;oBAChC,IAAI,CAAA,CAAE,QAAQ,QAAA,GAAW;wBACvB,MAAM,IAAI,MAAM,CAAA,UAAA,EAAa,IAAI,CAAA,gBAAA,CAAkB;oBACrD;oBACA,IAAI;wBAAC;wBAAW,QAAQ;qBAAA,CAAE,QAAA,CAAS,IAAI,GAAG;wBAExC;oBACF;oBACA,MAAM,eAAe;oBACrB,MAAM,eAAe,KAAK,QAAA,CAAS,YAAY,CAAA;oBAC/C,MAAM,eAAe,QAAA,CAAS,YAAY,CAAA;oBAE1C,QAAA,CAAS,YAAY,CAAA,GAAI,CAAA,GAAIA,UAAoB;wBAC/C,IAAI,MAAM,aAAa,KAAA,CAAM,UAAUA,KAAI;wBAC3C,IAAI,QAAQ,OAAO;4BACjB,MAAM,aAAa,KAAA,CAAM,UAAUA,KAAI;wBACzC;wBACA,OAAO,OAAO;oBAChB;gBACF;gBACA,KAAK,QAAA,GAAW;YAClB;YACA,IAAI,KAAK,SAAA,EAAW;gBAClB,MAAM,YAAY,IAAA,CAAK,QAAA,CAAS,SAAA,IAAa,IAAI,WAAW,IAAA,CAAK,QAAQ;gBACzE,IAAA,MAAW,QAAQ,KAAK,SAAA,CAAW;oBACjC,IAAI,CAAA,CAAE,QAAQ,SAAA,GAAY;wBACxB,MAAM,IAAI,MAAM,CAAA,WAAA,EAAc,IAAI,CAAA,gBAAA,CAAkB;oBACtD;oBACA,IAAI;wBAAC;wBAAW;wBAAS,OAAO;qBAAA,CAAE,QAAA,CAAS,IAAI,GAAG;wBAEhD;oBACF;oBACA,MAAM,gBAAgB;oBACtB,MAAM,gBAAgB,KAAK,SAAA,CAAU,aAAa,CAAA;oBAClD,MAAM,gBAAgB,SAAA,CAAU,aAAa,CAAA;oBAG7C,SAAA,CAAU,aAAa,CAAA,GAAI,CAAA,GAAIA,UAAoB;wBACjD,IAAI,MAAM,cAAc,KAAA,CAAM,WAAWA,KAAI;wBAC7C,IAAI,QAAQ,OAAO;4BACjB,MAAM,cAAc,KAAA,CAAM,WAAWA,KAAI;wBAC3C;wBACA,OAAO;oBACT;gBACF;gBACA,KAAK,SAAA,GAAY;YACnB;YAGA,IAAI,KAAK,KAAA,EAAO;gBACd,MAAM,QAAQ,IAAA,CAAK,QAAA,CAAS,KAAA,IAAS,IAAI,OAAO;gBAChD,IAAA,MAAW,QAAQ,KAAK,KAAA,CAAO;oBAC7B,IAAI,CAAA,CAAE,QAAQ,KAAA,GAAQ;wBACpB,MAAM,IAAI,MAAM,CAAA,MAAA,EAAS,IAAI,CAAA,gBAAA,CAAkB;oBACjD;oBACA,IAAI;wBAAC;wBAAW,OAAO;qBAAA,CAAE,QAAA,CAAS,IAAI,GAAG;wBAEvC;oBACF;oBACA,MAAM,YAAY;oBAClB,MAAM,YAAY,KAAK,KAAA,CAAM,SAAS,CAAA;oBACtC,MAAM,WAAW,KAAA,CAAM,SAAS,CAAA;oBAChC,IAAI,OAAO,gBAAA,CAAiB,GAAA,CAAI,IAAI,GAAG;wBAErC,KAAA,CAAM,SAAS,CAAA,GAAI,CAAC,QAAiB;4BACnC,IAAI,IAAA,CAAK,QAAA,CAAS,KAAA,EAAO;gCACvB,OAAO,QAAQ,OAAA,CAAQ,UAAU,IAAA,CAAK,OAAO,GAAG,CAAC,EAAE,IAAA,CAAK,CAAAC,SAAO;oCAC7D,OAAO,SAAS,IAAA,CAAK,OAAOA,IAAG;gCACjC,CAAC;4BACH;4BAEA,MAAM,MAAM,UAAU,IAAA,CAAK,OAAO,GAAG;4BACrC,OAAO,SAAS,IAAA,CAAK,OAAO,GAAG;wBACjC;oBACF,OAAO;wBAEL,KAAA,CAAM,SAAS,CAAA,GAAI,CAAA,GAAID,UAAoB;4BACzC,IAAI,MAAM,UAAU,KAAA,CAAM,OAAOA,KAAI;4BACrC,IAAI,QAAQ,OAAO;gCACjB,MAAM,SAAS,KAAA,CAAM,OAAOA,KAAI;4BAClC;4BACA,OAAO;wBACT;oBACF;gBACF;gBACA,KAAK,KAAA,GAAQ;YACf;YAGA,IAAI,KAAK,UAAA,EAAY;gBACnB,MAAME,cAAa,IAAA,CAAK,QAAA,CAAS,UAAA;gBACjC,MAAM,iBAAiB,KAAK,UAAA;gBAC5B,KAAK,UAAA,GAAa,SAAS,KAAA,EAAO;oBAChC,IAAI,SAAyB,CAAC,CAAA;oBAC9B,OAAO,IAAA,CAAK,eAAe,IAAA,CAAK,IAAA,EAAM,KAAK,CAAC;oBAC5C,IAAIA,aAAY;wBACd,SAAS,OAAO,MAAA,CAAOA,YAAW,IAAA,CAAK,IAAA,EAAM,KAAK,CAAC;oBACrD;oBACA,OAAO;gBACT;YACF;YAEA,IAAA,CAAK,QAAA,GAAW;gBAAE,GAAG,IAAA,CAAK,QAAA;gBAAU,GAAG,IAAA;YAAK;QAC9C,CAAC;QAED,OAAO,IAAA;IACT;IAEA,WAAW,GAAA,EAAoB;QAC7B,IAAA,CAAK,QAAA,GAAW;YAAE,GAAG,IAAA,CAAK,QAAA;YAAU,GAAG,GAAA;QAAI;QAC3C,OAAO,IAAA;IACT;IAEA,MAAM,GAAA,EAAaC,QAAAA,EAAyB;QAC1C,OAAO,OAAO,GAAA,CAAI,KAAKA,YAAW,IAAA,CAAK,QAAQ;IACjD;IAEA,OAAO,MAAA,EAAiBA,QAAAA,EAAyB;QAC/C,OAAO,QAAQ,KAAA,CAAM,QAAQA,YAAW,IAAA,CAAK,QAAQ;IACvD;IAEQ,cAAc,SAAA,EAAoB;QAQxC,MAAMC,SAAyB,CAAC,KAAaD,aAAwC;YACnF,MAAM,UAAU;gBAAE,GAAGA,QAAAA;YAAQ;YAC7B,MAAM,MAAM;gBAAE,GAAG,IAAA,CAAK,QAAA;gBAAU,GAAG,OAAA;YAAQ;YAE3C,MAAM,aAAa,IAAA,CAAK,OAAA,CAAQ,CAAC,CAAC,IAAI,MAAA,EAAQ,CAAC,CAAC,IAAI,KAAK;YAGzD,IAAI,IAAA,CAAK,QAAA,CAAS,KAAA,KAAU,QAAQ,QAAQ,KAAA,KAAU,OAAO;gBAC3D,OAAO,WAAW,IAAI,MAAM,oIAAoI,CAAC;YACnK;YAGA,IAAI,OAAO,QAAQ,eAAe,QAAQ,MAAM;gBAC9C,OAAO,WAAW,IAAI,MAAM,gDAAgD,CAAC;YAC/E;YACA,IAAI,OAAO,QAAQ,UAAU;gBAC3B,OAAO,WAAW,IAAI,MAAM,0CACxB,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,GAAG,IAAI,mBAAmB,CAAC;YAChE;YAEA,IAAI,IAAI,KAAA,EAAO;gBACb,IAAI,KAAA,CAAM,OAAA,GAAU;gBACpB,IAAI,KAAA,CAAM,KAAA,GAAQ;YACpB;YAEA,MAAME,SAAQ,IAAI,KAAA,GAAQ,IAAI,KAAA,CAAM,YAAA,CAAa,IAAK,YAAY,OAAO,GAAA,GAAM,OAAO,SAAA;YACtF,MAAMC,UAAS,IAAI,KAAA,GAAQ,IAAI,KAAA,CAAM,aAAA,CAAc,IAAK,YAAY,QAAQ,KAAA,GAAQ,QAAQ,WAAA;YAE5F,IAAI,IAAI,KAAA,EAAO;gBACb,OAAO,QAAQ,OAAA,CAAQ,IAAI,KAAA,GAAQ,IAAI,KAAA,CAAM,UAAA,CAAW,GAAG,IAAI,GAAG,EAC/D,IAAA,CAAK,CAAAC,OAAOF,OAAME,MAAK,GAAG,CAAC,EAC3B,IAAA,CAAK,CAAA,SAAU,IAAI,KAAA,GAAQ,IAAI,KAAA,CAAM,gBAAA,CAAiB,MAAM,IAAI,MAAM,EACtE,IAAA,CAAK,CAAA,SAAU,IAAI,UAAA,GAAa,QAAQ,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,QAAQ,IAAI,UAAU,CAAC,EAAE,IAAA,CAAK,IAAM,MAAM,IAAI,MAAM,EAChH,IAAA,CAAK,CAAA,SAAUD,QAAO,QAAQ,GAAG,CAAC,EAClC,IAAA,CAAK,CAAAE,QAAQ,IAAI,KAAA,GAAQ,IAAI,KAAA,CAAM,WAAA,CAAYA,KAAI,IAAIA,KAAI,EAC3D,KAAA,CAAM,UAAU;YACrB;YAEA,IAAI;gBACF,IAAI,IAAI,KAAA,EAAO;oBACb,MAAM,IAAI,KAAA,CAAM,UAAA,CAAW,GAAG;gBAChC;gBACA,IAAI,SAASH,OAAM,KAAK,GAAG;gBAC3B,IAAI,IAAI,KAAA,EAAO;oBACb,SAAS,IAAI,KAAA,CAAM,gBAAA,CAAiB,MAAM;gBAC5C;gBACA,IAAI,IAAI,UAAA,EAAY;oBAClB,IAAA,CAAK,UAAA,CAAW,QAAQ,IAAI,UAAU;gBACxC;gBACA,IAAIG,QAAOF,QAAO,QAAQ,GAAG;gBAC7B,IAAI,IAAI,KAAA,EAAO;oBACbE,QAAO,IAAI,KAAA,CAAM,WAAA,CAAYA,KAAI;gBACnC;gBACA,OAAOA;YACT,EAAA,OAAS,GAAG;gBACV,OAAO,WAAW,CAAU;YAC9B;QACF;QAEA,OAAOJ;IACT;IAEQ,QAAQ,MAAA,EAAiB,KAAA,EAAgB;QAC/C,OAAO,CAAC,MAAuC;YAC7C,EAAE,OAAA,IAAW;YAEb,IAAI,QAAQ;gBACV,MAAM,MAAM,mCACRK,QAAO,EAAE,OAAA,GAAU,IAAI,IAAI,IAC3B;gBACJ,IAAI,OAAO;oBACT,OAAO,QAAQ,OAAA,CAAQ,GAAG;gBAC5B;gBACA,OAAO;YACT;YAEA,IAAI,OAAO;gBACT,OAAO,QAAQ,MAAA,CAAO,CAAC;YACzB;YACA,MAAM;QACR;IACF;AACF;;ACjVA,IAAM,iBAAiB,IAAI,OAAO;AAqB3B,SAAS,OAAO,GAAA,EAAa,GAAA,EAAsD;IACxF,OAAO,eAAe,KAAA,CAAM,KAAK,GAAG;AACtC;AAOA,OAAO,OAAA,GACP,OAAO,UAAA,GAAa,SAASC,QAAAA,EAAwB;IACnD,eAAe,UAAA,CAAWA,QAAO;IACjC,OAAO,QAAA,GAAW,eAAe,QAAA;IACjC,eAAe,OAAO,QAAQ;IAC9B,OAAO;AACT;AAKA,OAAO,WAAA,GAAc;AAErB,OAAO,QAAA,GAAW;AAMlB,OAAO,GAAA,GAAM,SAAA,GAAY,IAAA,EAAyB;IAChD,eAAe,GAAA,CAAI,GAAG,IAAI;IAC1B,OAAO,QAAA,GAAW,eAAe,QAAA;IACjC,eAAe,OAAO,QAAQ;IAC9B,OAAO;AACT;AAMA,OAAO,UAAA,GAAa,SAAS,MAAA,EAA8B,QAAA,EAA2D;IACpH,OAAO,eAAe,UAAA,CAAW,QAAQ,QAAQ;AACnD;AASA,OAAO,WAAA,GAAc,eAAe,WAAA;AAKpC,OAAO,MAAA,GAAS;AAChB,OAAO,MAAA,GAAS,QAAQ,KAAA;AACxB,OAAO,QAAA,GAAW;AAClB,OAAO,YAAA,GAAe;AACtB,OAAO,KAAA,GAAQ;AACf,OAAO,KAAA,GAAQ,OAAO,GAAA;AACtB,OAAO,SAAA,GAAY;AACnB,OAAO,KAAA,GAAQ;AACf,OAAO,KAAA,GAAQ;AAER,IAAM,UAAU,OAAO,OAAA;AACvB,IAAM,aAAa,OAAO,UAAA;AAC1B,IAAM,MAAM,OAAO,GAAA;AACnB,IAAM,aAAa,OAAO,UAAA;AAC1B,IAAM,cAAc,OAAO,WAAA;AAC3B,IAAM,QAAQ;AACd,IAAM,SAAS,QAAQ,KAAA;AACvB,IAAM,QAAQ,OAAO,GAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "debugId": null}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "file": "purify.es.mjs", "sources": ["file:///D:/mdeditor/node_modules/dompurify/src/utils.ts", "file:///D:/mdeditor/node_modules/dompurify/src/tags.ts", "file:///D:/mdeditor/node_modules/dompurify/src/attrs.ts", "file:///D:/mdeditor/node_modules/dompurify/src/regexp.ts", "file:///D:/mdeditor/node_modules/dompurify/src/purify.ts"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;AAAA,MAAM,EACJA,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,wBAAAA,EACD,GAAGC,MAAM,CAAA;AAEV,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAAA,EAAQ,GAAGH,MAAM,CAAC,CAAA,gDAAA;AACtC,IAAI,EAAEI,KAAK,EAAEC,SAAAA,EAAW,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAA;AAEpE,IAAI,CAACL,MAAM,EAAE;IACXA,MAAM,GAAG,SAAAA,MAAUM,CAAAA,CAAC,EAAA;QAClB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,IAAI,EAAE;IACTA,IAAI,GAAG,SAAAA,IAAUK,CAAAA,CAAC,EAAA;QAChB,OAAOA,CAAC,CAAA;KACT,CAAA;AACH,CAAA;AAEA,IAAI,CAACH,KAAK,EAAE;IACVA,KAAK,GAAG,SAAAA,KAAUI,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAA;QACpC,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA,IAAI,CAACL,SAAS,EAAE;IACdA,SAAS,GAAG,SAAAA,SAAAA,CAAUM,IAAI,EAAED,IAAI,EAAA;QAC9B,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAA;KACzB,CAAA;AACH,CAAA;AAEA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;AAErD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,WAAW,CAAC,CAAA;AAC7D,MAAMC,QAAQ,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,GAAG,CAAC,CAAA;AAC7C,MAAMC,SAAS,GAAGR,OAAO,CAACC,KAAK,CAACC,SAAS,CAACO,IAAI,CAAC,CAAA;AAE/C,MAAMC,WAAW,GAAGV,OAAO,CAACC,KAAK,CAACC,SAAS,CAACS,MAAM,CAAC,CAAA;AAEnD,MAAMC,iBAAiB,GAAGZ,OAAO,CAACa,MAAM,CAACX,SAAS,CAACY,WAAW,CAAC,CAAA;AAC/D,MAAMC,cAAc,GAAGf,OAAO,CAACa,MAAM,CAACX,SAAS,CAACc,QAAQ,CAAC,CAAA;AACzD,MAAMC,WAAW,GAAGjB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACgB,KAAK,CAAC,CAAA;AACnD,MAAMC,aAAa,GAAGnB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACkB,OAAO,CAAC,CAAA;AACvD,MAAMC,aAAa,GAAGrB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACoB,OAAO,CAAC,CAAA;AACvD,MAAMC,UAAU,GAAGvB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACsB,IAAI,CAAC,CAAA;AAEjD,MAAMC,oBAAoB,GAAGzB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACwB,cAAc,CAAC,CAAA;AAErE,MAAMC,UAAU,GAAG3B,OAAO,CAAC4B,MAAM,CAAC1B,SAAS,CAAC2B,IAAI,CAAC,CAAA;AAEjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;AAE9C;;;;;CAKG,GACH,SAAShC,OAAOA,CACdiC,IAAyC,EAAA;IAEzC,OAAO,SAACC,OAAY,EAAuB;QACzC,IAAIA,OAAO,YAAYN,MAAM,EAAE;YAC7BM,OAAO,CAACC,SAAS,GAAG,CAAC,CAAA;QACvB,CAAA;QAAC,IAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHsBzC,IAAW,GAAA,IAAAI,KAAA,CAAAmC,IAAA,GAAAA,CAAAA,GAAAA,IAAA,GAAA,IAAA,IAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,CAAA;YAAX1C,IAAW,CAAA0C,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;QAAA,CAAA;QAKlC,OAAOhD,KAAK,CAAC0C,IAAI,EAAEC,OAAO,EAAErC,IAAI,CAAC,CAAA;KAClC,CAAA;AACH,CAAA;AAEA;;;;;CAKG,GACH,SAASkC,WAAWA,CAAIE,IAA2B,EAAA;IACjD,OAAO,YAAA;QAAA,IAAA,IAAAO,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIzC,IAAW,GAAAI,IAAAA,KAAA,CAAAuC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,CAAA;YAAX5C,IAAW,CAAA4C,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;QAAA,CAAA;QAAA,OAAQjD,SAAS,CAACyC,IAAI,EAAEpC,IAAI,CAAC,CAAA;IAAA,CAAA,CAAA;AACrD,CAAA;AAEA;;;;;;;CAOG,GACH,SAAS6C,QAAQA,CACfC,GAAwB,EACxBC,KAAqB,EACoD;IAAA,IAAzEC,oBAAAA,UAAAA,MAAAA,GAAAA,KAAAA,SAAAA,CAAAA,EAAAA,KAAAA,YAAAA,SAAAA,CAAAA,EAAAA,GAAwDjC,iBAAiB,CAAA;IAEzE,IAAI7B,cAAc,EAAE;QAClB,4DAAA;QACA,6DAAA;QACA,mEAAA;QACAA,cAAc,CAAC4D,GAAG,EAAE,IAAI,CAAC,CAAA;IAC3B,CAAA;IAEA,IAAIG,CAAC,GAAGF,KAAK,CAACN,MAAM,CAAA;IACpB,MAAOQ,CAAC,EAAE,CAAE;QACV,IAAIC,OAAO,GAAGH,KAAK,CAACE,CAAC,CAAC,CAAA;QACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAMC,SAAS,GAAGH,iBAAiB,CAACE,OAAO,CAAC,CAAA;YAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;gBACzB,yDAAA;gBACA,IAAI,CAAC/D,QAAQ,CAAC4D,KAAK,CAAC,EAAE;oBACnBA,KAAe,CAACE,CAAC,CAAC,GAAGE,SAAS,CAAA;gBACjC,CAAA;gBAEAD,OAAO,GAAGC,SAAS,CAAA;YACrB,CAAA;QACF,CAAA;QAEAL,GAAG,CAACI,OAAO,CAAC,GAAG,IAAI,CAAA;IACrB,CAAA;IAEA,OAAOJ,GAAG,CAAA;AACZ,CAAA;AAEA;;;;;CAKG,GACH,SAASM,UAAUA,CAAIL,KAAU,EAAA;IAC/B,IAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,KAAK,CAACN,MAAM,EAAEY,KAAK,EAAE,CAAE;QACjD,MAAMC,eAAe,GAAG1B,oBAAoB,CAACmB,KAAK,EAAEM,KAAK,CAAC,CAAA;QAE1D,IAAI,CAACC,eAAe,EAAE;YACpBP,KAAK,CAACM,KAAK,CAAC,GAAG,IAAI,CAAA;QACrB,CAAA;IACF,CAAA;IAEA,OAAON,KAAK,CAAA;AACd,CAAA;AAEA;;;;;CAKG,GACH,SAASQ,KAAKA,CAAgCC,MAAS,EAAA;IACrD,MAAMC,SAAS,GAAGhE,MAAM,CAAC,IAAI,CAAC,CAAA;IAE9B,KAAK,MAAM,CAACiE,QAAQ,EAAEC,KAAK,CAAC,IAAI1E,OAAO,CAACuE,MAAM,CAAC,CAAE;QAC/C,MAAMF,eAAe,GAAG1B,oBAAoB,CAAC4B,MAAM,EAAEE,QAAQ,CAAC,CAAA;QAE9D,IAAIJ,eAAe,EAAE;YACnB,IAAIlD,KAAK,CAACwD,OAAO,CAACD,KAAK,CAAC,EAAE;gBACxBF,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAA;YACzC,CAAC,MAAM,IACLA,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACE,WAAW,KAAKvE,MAAM,EAC5B;gBACAmE,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAA;YACpC,CAAC,MAAM;gBACLF,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAAA;YAC7B,CAAA;QACF,CAAA;IACF,CAAA;IAEA,OAAOF,SAAS,CAAA;AAClB,CAAA;AAEA;;;;;;CAMG,GACH,SAASK,YAAYA,CACnBN,MAAS,EACTO,IAAY,EAAA;IAEZ,MAAOP,MAAM,KAAK,IAAI,CAAE;QACtB,MAAMQ,IAAI,GAAG3E,wBAAwB,CAACmE,MAAM,EAAEO,IAAI,CAAC,CAAA;QAEnD,IAAIC,IAAI,EAAE;YACR,IAAIA,IAAI,CAACC,GAAG,EAAE;gBACZ,OAAO9D,OAAO,CAAC6D,IAAI,CAACC,GAAG,CAAC,CAAA;YAC1B,CAAA;YAEA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;gBACpC,OAAOxD,OAAO,CAAC6D,IAAI,CAACL,KAAK,CAAC,CAAA;YAC5B,CAAA;QACF,CAAA;QAEAH,MAAM,GAAGpE,cAAc,CAACoE,MAAM,CAAC,CAAA;IACjC,CAAA;IAEA,SAASU,aAAaA,GAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,OAAOA,aAAa,CAAA;AACtB;AC3MO,MAAMC,MAAI,GAAG5E,MAAM,CAAC;IACzB,GAAG;IACH,MAAM;IACN,SAAS;IACT,SAAS;IACT,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,GAAG;IACH,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,KAAK;IACL,SAAS;IACT,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,GAAG;IACH,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,KAAK;IACL,MAAM;IACN,SAAS;IACT,MAAM;IACN,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,IAAI;IACJ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,GAAG;IACH,SAAS;IACT,KAAK;IACL,UAAU;IACV,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,GAAG;IACH,MAAM;IACN,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,UAAU;IACV,UAAU;IACV,OAAO;IACP,IAAI;IACJ,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,KAAK;IACL,OAAO;IACP,KAAK;CACG,CAAC,CAAA;AAEJ,MAAM6E,KAAG,GAAG7E,MAAM,CAAC;IACxB,KAAK;IACL,GAAG;IACH,UAAU;IACV,aAAa;IACb,cAAc;IACd,cAAc;IACd,eAAe;IACf,kBAAkB;IAClB,QAAQ;IACR,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,MAAM;IACN,GAAG;IACH,OAAO;IACP,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,gBAAgB;IAChB,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;CACC,CAAC,CAAA;AAEJ,MAAM8E,UAAU,GAAG9E,MAAM,CAAC;IAC/B,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,aAAa;IACb,cAAc;IACd,UAAU;IACV,cAAc;IACd,oBAAoB;IACpB,aAAa;IACb,QAAQ;IACR,cAAc;CACN,CAAC,CAAA;AAEX,uDAAA;AACA,yDAAA;AACA,mDAAA;AACA,cAAA;AACO,MAAM+E,aAAa,GAAG/E,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,QAAQ;IACR,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,eAAe;IACf,OAAO;IACP,WAAW;IACX,MAAM;IACN,cAAc;IACd,WAAW;IACX,SAAS;IACT,eAAe;IACf,QAAQ;IACR,KAAK;IACL,YAAY;IACZ,SAAS;IACT,KAAK;CACG,CAAC,CAAA;AAEJ,MAAMgF,QAAM,GAAGhF,MAAM,CAAC;IAC3B,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,YAAY;IACZ,eAAe;IACf,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,aAAa;CACL,CAAC,CAAA;AAEX,yDAAA;AACA,0CAAA;AACO,MAAMiF,gBAAgB,GAAGjF,MAAM,CAAC;IACrC,SAAS;IACT,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,MAAM;CACE,CAAC,CAAA;AAEJ,MAAMkF,IAAI,GAAGlF,MAAM,CAAC;IAAC,OAAO;CAAU,CAAC;ACpRvC,MAAM4E,IAAI,GAAG5E,MAAM,CAAC;IACzB,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,gBAAgB;IAChB,cAAc;IACd,sBAAsB;IACtB,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,UAAU;IACV,cAAc;IACd,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,KAAK;IACL,UAAU;IACV,yBAAyB;IACzB,uBAAuB;IACvB,UAAU;IACV,WAAW;IACX,SAAS;IACT,cAAc;IACd,MAAM;IACN,KAAK;IACL,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,UAAU;IACV,IAAI;IACJ,WAAW;IACX,WAAW;IACX,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,KAAK;IACL,KAAK;IACL,WAAW;IACX,OAAO;IACP,QAAQ;IACR,KAAK;IACL,WAAW;IACX,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,aAAa;IACb,SAAS;IACT,eAAe;IACf,qBAAqB;IACrB,QAAQ;IACR,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,KAAK;IACL,UAAU;IACV,KAAK;IACL,UAAU;IACV,MAAM;IACN,MAAM;IACN,SAAS;IACT,YAAY;IACZ,OAAO;IACP,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;CACE,CAAC,CAAA;AAEJ,MAAM6E,GAAG,GAAG7E,MAAM,CAAC;IACxB,eAAe;IACf,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,WAAW;IACX,QAAQ;IACR,eAAe;IACf,eAAe;IACf,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,MAAM;IACN,eAAe;IACf,WAAW;IACX,WAAW;IACX,OAAO;IACP,qBAAqB;IACrB,6BAA6B;IAC7B,eAAe;IACf,iBAAiB;IACjB,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;IACT,KAAK;IACL,UAAU;IACV,WAAW;IACX,KAAK;IACL,UAAU;IACV,MAAM;IACN,cAAc;IACd,WAAW;IACX,QAAQ;IACR,aAAa;IACb,aAAa;IACb,eAAe;IACf,aAAa;IACb,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,cAAc;IACd,aAAa;IACb,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,YAAY;IACZ,UAAU;IACV,eAAe;IACf,mBAAmB;IACnB,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,iBAAiB;IACjB,IAAI;IACJ,KAAK;IACL,WAAW;IACX,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,WAAW;IACX,YAAY;IACZ,UAAU;IACV,MAAM;IACN,cAAc;IACd,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,OAAO;IACP,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,aAAa;IACb,aAAa;IACb,kBAAkB;IAClB,WAAW;IACX,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,SAAS;IACT,OAAO;IACP,QAAQ;IACR,aAAa;IACb,QAAQ;IACR,UAAU;IACV,aAAa;IACb,MAAM;IACN,YAAY;IACZ,qBAAqB;IACrB,kBAAkB;IAClB,cAAc;IACd,QAAQ;IACR,eAAe;IACf,qBAAqB;IACrB,gBAAgB;IAChB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,aAAa;IACb,WAAW;IACX,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,iBAAiB;IACjB,OAAO;IACP,kBAAkB;IAClB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,gBAAgB;IAChB,QAAQ;IACR,cAAc;IACd,OAAO;IACP,cAAc;IACd,gBAAgB;IAChB,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;IAChB,YAAY;IACZ,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,eAAe;IACf,eAAe;IACf,OAAO;IACP,cAAc;IACd,MAAM;IACN,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,YAAY;CACJ,CAAC,CAAA;AAEJ,MAAMgF,MAAM,GAAGhF,MAAM,CAAC;IAC3B,QAAQ;IACR,aAAa;IACb,OAAO;IACP,UAAU;IACV,OAAO;IACP,cAAc;IACd,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,KAAK;IACL,SAAS;IACT,cAAc;IACd,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,eAAe;IACf,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,WAAW;IACX,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,eAAe;IACf,UAAU;IACV,UAAU;IACV,MAAM;IACN,UAAU;IACV,UAAU;IACV,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,eAAe;IACf,sBAAsB;IACtB,WAAW;IACX,WAAW;IACX,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,gBAAgB;IAChB,WAAW;IACX,SAAS;IACT,OAAO;IACP,OAAO;CACR,CAAC,CAAA;AAEK,MAAMmF,GAAG,GAAGnF,MAAM,CAAC;IACxB,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,WAAW;IACX,aAAa;CACL,CAAC;AChXX,gDAAA;AACO,MAAMoF,aAAa,GAAGnF,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAA,+DAAA;AACxD,MAAMoF,QAAQ,GAAGpF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC9C,MAAMqF,WAAW,GAAGrF,IAAI,CAAC,eAAe,CAAC,CAAC,CAAA,2CAAA;AAC1C,MAAMsF,SAAS,GAAGtF,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAA,wCAAA;AACvD,MAAMuF,SAAS,GAAGvF,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA,wCAAA;AACzC,MAAMwF,cAAc,GAAGxF,IAAI,CAChC,kGAAkG,CAAA,wCAAA;;AAE7F,MAAMyF,iBAAiB,GAAGzF,IAAI,CAAC,uBAAuB,CAAC,CAAA;AACvD,MAAM0F,eAAe,GAAG1F,IAAI,CACjC,6DAA6D,CAAA,uCAAA;;AAExD,MAAM2F,YAAY,GAAG3F,IAAI,CAAC,SAAS,CAAC,CAAA;AACpC,MAAM4F,cAAc,GAAG5F,IAAI,CAAC,0BAA0B,CAAC;;;;;;;;;;;;;;AChB9D,4CAAA,GAkCA,iEAAA;AACA,MAAM6F,SAAS,GAAG;IAChBnC,OAAO,EAAE,CAAC;IACVoC,SAAS,EAAE,CAAC;IACZb,IAAI,EAAE,CAAC;IACPc,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAAE,aAAA;IACpBC,UAAU,EAAE,CAAC;IAAE,aAAA;IACfC,sBAAsB,EAAE,CAAC;IACzBC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,EAAE;IACpBC,QAAQ,EAAE,EAAE,CAAA,aAAA;CACb,CAAA;AAED,MAAMC,SAAS,GAAG,SAAZA,SAASA,GAAG;IAChB,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;AACtD,CAAC,CAAA;AAED;;;;;;;CAOG,GACH,MAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAC7BC,YAAsC,EACtCC,iBAAoC,EAAA;IAEpC,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAC/C;QACA,OAAO,IAAI,CAAA;IACb,CAAA;IAEA,sDAAA;IACA,8EAAA;IACA,gEAAA;IACA,IAAIC,MAAM,GAAG,IAAI,CAAA;IACjB,MAAMC,SAAS,GAAG,uBAAuB,CAAA;IACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;QAClED,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC,CAAA;IACpD,CAAA;IAEA,MAAMG,UAAU,GAAG,WAAW,GAAA,CAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;IAE7D,IAAI;QACF,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;YAC3CC,UAAUA,EAACxC,IAAI,EAAA;gBACb,OAAOA,IAAI,CAAA;aACZ;YACDyC,eAAeA,EAACC,SAAS,EAAA;gBACvB,OAAOA,SAAS,CAAA;YAClB,CAAA;QACD,CAAA,CAAC,CAAA;KACH,CAAC,OAAOC,CAAC,EAAE;QACV,mEAAA;QACA,yEAAA;QACA,sBAAA;QACAC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAGN,UAAU,GAAG,wBAAwB,CAC/D,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAA;AACF,CAAC,CAAA;AAED,MAAMO,eAAe,GAAG,SAAlBA,eAAeA,GAAG;IACtB,OAAO;QACLC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,sBAAsB,EAAE,EAAE;QAC1BC,wBAAwB,EAAE,EAAE;QAC5BC,sBAAsB,EAAE,EAAE;QAC1BC,uBAAuB,EAAE,EAAE;QAC3BC,qBAAqB,EAAE,EAAE;QACzBC,mBAAmB,EAAE,EAAE;QACvBC,sBAAsB,EAAE,EAAA;KACzB,CAAA;AACH,CAAC,CAAA;AAED,SAASC,eAAeA,GAAiC;IAAA,IAAhC1B,MAAqB,IAAAzD,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAoF,CAAAA,CAAAA,KAAAA,SAAA,GAAApF,SAAA,CAAAwD,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;IACvD,MAAM6B,SAAS,IAAeC,IAAgB,GAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;IAExED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;IAE3BH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;IAEtB,IACE,CAAChC,MAAM,KACP,CAACA,MAAM,EAACL,QAAQ,IAChBK,MAAM,EAACL,QAAQ,CAACsC,QAAQ,KAAK7C,SAAS,CAACO,QAAQ,IAC/C,CAACK,MAAM,EAACkC,OAAO,EACf;QACA,uDAAA;QACA,uCAAA;QACAN,SAAS,CAACO,WAAW,GAAG,KAAK,CAAA;QAE7B,OAAOP,SAAS,CAAA;IAClB,CAAA;IAEA,IAAI,EAAEjC,QAAAA,EAAU,GAAGK,MAAM,CAAA;IAEzB,MAAMoC,gBAAgB,GAAGzC,QAAQ,CAAA;IACjC,MAAM0C,aAAa,GACjBD,gBAAgB,CAACC,aAAkC,CAAA;IACrD,MAAM,EACJC,gBAAgB,EAChBC,mBAAmB,EACnBC,IAAI,EACJN,OAAO,EACPO,UAAU,EACVC,YAAY,GAAG1C,MAAM,EAAC0C,YAAY,IAAK1C,MAAc,EAAC2C,eAAe,EACrEC,eAAe,EACfC,SAAS,EACT3C,YAAAA,EACD,GAAGF,MAAM,CAAA;IAEV,MAAM8C,gBAAgB,GAAGZ,OAAO,CAAC9H,SAAS,CAAA;IAE1C,MAAM2I,SAAS,GAAGlF,YAAY,CAACiF,gBAAgB,EAAE,WAAW,CAAC,CAAA;IAC7D,MAAME,MAAM,GAAGnF,YAAY,CAACiF,gBAAgB,EAAE,QAAQ,CAAC,CAAA;IACvD,MAAMG,cAAc,GAAGpF,YAAY,CAACiF,gBAAgB,EAAE,aAAa,CAAC,CAAA;IACpE,MAAMI,aAAa,GAAGrF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAClE,MAAMK,aAAa,GAAGtF,YAAY,CAACiF,gBAAgB,EAAE,YAAY,CAAC,CAAA;IAElE,kEAAA;IACA,+DAAA;IACA,oFAAA;IACA,uEAAA;IACA,oEAAA;IACA,gBAAA;IACA,IAAI,OAAOP,mBAAmB,KAAK,UAAU,EAAE;QAC7C,MAAMa,QAAQ,GAAGzD,QAAQ,CAAC0D,aAAa,CAAC,UAAU,CAAC,CAAA;QACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;YACtD5D,QAAQ,GAAGyD,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;QAC3C,CAAA;IACF,CAAA;IAEA,IAAIC,kBAAkB,CAAA;IACtB,IAAIC,SAAS,GAAG,EAAE,CAAA;IAElB,MAAM,EACJC,cAAc,EACdC,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAAA,EACD,GAAGlE,QAAQ,CAAA;IACZ,MAAM,EAAEmE,UAAAA,EAAY,GAAG1B,gBAAgB,CAAA;IAEvC,IAAI2B,KAAK,GAAG/C,eAAe,EAAE,CAAA;IAE7B;;GAEG,GACHY,SAAS,CAACO,WAAW,GACnB,OAAOnJ,OAAO,KAAK,UAAU,IAC7B,OAAOmK,aAAa,KAAK,UAAU,IACnCO,cAAc,IACdA,cAAc,CAACM,kBAAkB,KAAKrC,SAAS,CAAA;IAEjD,MAAM,EACJjD,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTE,iBAAiB,EACjBC,eAAe,EACfE,cAAAA,EACD,GAAG8E,WAAW,CAAA;IAEf,IAAI,EAAElF,gBAAAA,gBAAAA,EAAgB,GAAGkF,WAAW,CAAA;IAEpC;;;GAGG,GAEH,yBAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAGvH,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAGwH,MAAS,EACZ;WAAGA,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,QAAW,EACd;WAAGA,IAAS;KACb,CAAC,CAAA;IAEF,2BAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IACvB,MAAMC,oBAAoB,GAAG1H,QAAQ,CAAC,CAAA,CAAE,EAAE,CACxC;WAAG2H,IAAU,EACb;WAAGA,GAAS,EACZ;WAAGA,MAAY,EACf;WAAGA,GAAS;KACb,CAAC,CAAA;IAEF;;;;;GAKG,GACH,IAAIC,uBAAuB,GAAGnL,MAAM,CAACE,IAAI,CACvCC,MAAM,CAAC,IAAI,EAAE;QACXiL,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDmH,kBAAkB,EAAE;YAClBH,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,IAAA;SACR;QACDoH,8BAA8B,EAAE;YAC9BJ,QAAQ,EAAE,IAAI;YACdC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE,IAAI;YAChBlH,KAAK,EAAE,KAAA;QACR,CAAA;IACF,CAAA,CAAC,CACH,CAAA;IAED,+DAAA,GACA,IAAIqH,WAAW,GAAG,IAAI,CAAA;IAEtB,qEAAA,GACA,IAAIC,WAAW,GAAG,IAAI,CAAA;IAEtB,sCAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,6CAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAE1B,wCAAA,GACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;IAEnC;uDACuD,GACvD,IAAIC,wBAAwB,GAAG,IAAI,CAAA;IAEnC;;GAEG,GACH,IAAIC,kBAAkB,GAAG,KAAK,CAAA;IAE9B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB,wDAAA,GACA,IAAIC,cAAc,GAAG,KAAK,CAAA;IAE1B,sEAAA,GACA,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;0EAC0E,GAC1E,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;;;GAGG,GACH,IAAIC,UAAU,GAAG,KAAK,CAAA;IAEtB;sEACsE,GACtE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;2CAC2C,GAC3C,IAAIC,mBAAmB,GAAG,KAAK,CAAA;IAE/B;;GAEG,GACH,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;;;;;;;;;;;;GAYG,GACH,IAAIC,oBAAoB,GAAG,KAAK,CAAA;IAChC,MAAMC,2BAA2B,GAAG,eAAe,CAAA;IAEnD,+CAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;IAEvB;wEACwE,GACxE,IAAIC,QAAQ,GAAG,KAAK,CAAA;IAEpB,qDAAA,GACA,IAAIC,YAAY,GAA8B,CAAA,CAAE,CAAA;IAEhD,uDAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;IAC1B,MAAMC,uBAAuB,GAAGxJ,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC3C,gBAAgB;QAChB,OAAO;QACP,UAAU;QACV,MAAM;QACN,eAAe;QACf,MAAM;QACN,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,QAAQ;QACR,OAAO;QACP,KAAK;QACL,UAAU;QACV,OAAO;QACP,OAAO;QACP,OAAO;QACP,KAAK;KACN,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIyJ,aAAa,GAAG,IAAI,CAAA;IACxB,MAAMC,qBAAqB,GAAG1J,QAAQ,CAAC,CAAA,CAAE,EAAE;QACzC,OAAO;QACP,OAAO;QACP,KAAK;QACL,QAAQ;QACR,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,iDAAA,GACA,IAAI2J,mBAAmB,GAAG,IAAI,CAAA;IAC9B,MAAMC,2BAA2B,GAAG5J,QAAQ,CAAC,CAAA,CAAE,EAAE;QAC/C,KAAK;QACL,OAAO;QACP,KAAK;QACL,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEF,MAAM6J,gBAAgB,GAAG,oCAAoC,CAAA;IAC7D,MAAMC,aAAa,GAAG,4BAA4B,CAAA;IAClD,MAAMC,cAAc,GAAG,8BAA8B,CAAA;IACrD,sBAAA,GACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;IAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;IAE1B,gCAAA,GACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;IAC7B,MAAMC,0BAA0B,GAAGnK,QAAQ,CACzC,CAAA,CAAE,EACF;QAAC6J,gBAAgB;QAAEC,aAAa;QAAEC,cAAc;KAAC,EACjD1L,cAAc,CACf,CAAA;IAED,IAAI+L,8BAA8B,GAAGpK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;KACR,CAAC,CAAA;IAEF,IAAIqK,uBAAuB,GAAGrK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAAC,gBAAgB;KAAC,CAAC,CAAA;IAE9D,oDAAA;IACA,gDAAA;IACA,kDAAA;IACA,kBAAA;IACA,MAAMsK,4BAA4B,GAAGtK,QAAQ,CAAC,CAAA,CAAE,EAAE;QAChD,OAAO;QACP,OAAO;QACP,MAAM;QACN,GAAG;QACH,QAAQ;KACT,CAAC,CAAA;IAEF,qCAAA,GACA,IAAIuK,iBAAiB,GAAkC,IAAI,CAAA;IAC3D,MAAMC,4BAA4B,GAAG;QAAC,uBAAuB;QAAE,WAAW;KAAC,CAAA;IAC3E,MAAMC,yBAAyB,GAAG,WAAW,CAAA;IAC7C,IAAItK,iBAAiB,GAA0C,IAAI,CAAA;IAEnE,+CAAA,GACA,IAAIuK,MAAM,GAAkB,IAAI,CAAA;IAEhC,kDAAA,GACA,kDAAA,GAEA,MAAMC,WAAW,GAAG5H,QAAQ,CAAC0D,aAAa,CAAC,MAAM,CAAC,CAAA;IAElD,MAAMmE,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,SAAkB,EAAA;QAElB,OAAOA,SAAS,YAAY3L,MAAM,IAAI2L,SAAS,YAAYC,QAAQ,CAAA;KACpE,CAAA;IAED;;;;GAIG,GACH,sCAAA;IACA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,GAA6B;QAAA,IAAhBC,GAAA,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAc,CAAA,CAAE,CAAA;QAC7C,IAAI+K,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;YAC5B,OAAA;QACF,CAAA;QAEA,8CAAA,GACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACnCA,GAAG,GAAG,CAAA,CAAE,CAAA;QACV,CAAA;QAEA,wDAAA,GACAA,GAAG,GAAGtK,KAAK,CAACsK,GAAG,CAAC,CAAA;QAEhBT,iBAAiB,GACf,mDAAA;QACAC,4BAA4B,CAAC5L,OAAO,CAACoM,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC9DE,yBAAyB,GACzBO,GAAG,CAACT,iBAAiB,CAAA;QAE3B,iGAAA;QACApK,iBAAiB,GACfoK,iBAAiB,KAAK,uBAAuB,GACzClM,cAAc,GACdH,iBAAiB,CAAA;QAEvB,gCAAA,GACAoJ,YAAY,GAAGvI,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC1D,YAAY,EAAEnH,iBAAiB,CAAC,GACjDoH,oBAAoB,CAAA;QACxBE,YAAY,GAAG1I,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACvD,YAAY,EAAEtH,iBAAiB,CAAC,GACjDuH,oBAAoB,CAAA;QACxBwC,kBAAkB,GAAGnL,oBAAoB,CAACiM,GAAG,EAAE,oBAAoB,CAAC,GAChEhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACd,kBAAkB,EAAE7L,cAAc,CAAC,GACpD8L,0BAA0B,CAAA;QAC9BR,mBAAmB,GAAG5K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAChEhL,QAAQ,CACNU,KAAK,CAACkJ,2BAA2B,CAAC,EAClCoB,GAAG,CAACC,iBAAiB,EACrB9K,iBAAiB,CAClB,GACDyJ,2BAA2B,CAAA;QAC/BH,aAAa,GAAG1K,oBAAoB,CAACiM,GAAG,EAAE,mBAAmB,CAAC,GAC1DhL,QAAQ,CACNU,KAAK,CAACgJ,qBAAqB,CAAC,EAC5BsB,GAAG,CAACE,iBAAiB,EACrB/K,iBAAiB,CAClB,GACDuJ,qBAAqB,CAAA;QACzBH,eAAe,GAAGxK,oBAAoB,CAACiM,GAAG,EAAE,iBAAiB,CAAC,GAC1DhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,GACpDqJ,uBAAuB,CAAA;QAC3BrB,WAAW,GAAGpJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC7C,WAAW,EAAEhI,iBAAiB,CAAC,GAChDO,KAAK,CAAC,CAAA,CAAE,CAAC,CAAA;QACb0H,WAAW,GAAGrJ,oBAAoB,CAACiM,GAAG,EAAE,aAAa,CAAC,GAClDhL,QAAQ,CAAC,CAAA,CAAE,EAAEgL,GAAG,CAAC5C,WAAW,EAAEjI,iBAAiB,CAAC,GAChDO,KAAK,CAAC,CAAA,CAAE,CAAC,CAAA;QACb4I,YAAY,GAAGvK,oBAAoB,CAACiM,GAAG,EAAE,cAAc,CAAC,GACpDA,GAAG,CAAC1B,YAAY,GAChB,KAAK,CAAA;QACTjB,eAAe,GAAG2C,GAAG,CAAC3C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,eAAe,GAAG0C,GAAG,CAAC1C,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;QAChDC,uBAAuB,GAAGyC,GAAG,CAACzC,uBAAuB,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC/DC,wBAAwB,GAAGwC,GAAG,CAACxC,wBAAwB,KAAK,KAAK,CAAC,CAAA,eAAA;QAClEC,kBAAkB,GAAGuC,GAAG,CAACvC,kBAAkB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrDC,YAAY,GAAGsC,GAAG,CAACtC,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,cAAc,GAAGqC,GAAG,CAACrC,cAAc,IAAI,KAAK,CAAC,CAAA,gBAAA;QAC7CG,UAAU,GAAGkC,GAAG,CAAClC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCC,mBAAmB,GAAGiC,GAAG,CAACjC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDC,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACvDH,UAAU,GAAGmC,GAAG,CAACnC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;QACrCI,YAAY,GAAG+B,GAAG,CAAC/B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,oBAAoB,GAAG8B,GAAG,CAAC9B,oBAAoB,IAAI,KAAK,CAAC,CAAA,gBAAA;QACzDE,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;QAC1CC,QAAQ,GAAG2B,GAAG,CAAC3B,QAAQ,IAAI,KAAK,CAAC,CAAA,gBAAA;QACjClH,gBAAc,GAAG6I,GAAG,CAACG,kBAAkB,IAAI9D,cAA0B,CAAA;QACrE2C,SAAS,GAAGgB,GAAG,CAAChB,SAAS,IAAID,cAAc,CAAA;QAC3CK,8BAA8B,GAC5BY,GAAG,CAACZ,8BAA8B,IAAIA,8BAA8B,CAAA;QACtEC,uBAAuB,GACrBW,GAAG,CAACX,uBAAuB,IAAIA,uBAAuB,CAAA;QAExDzC,uBAAuB,GAAGoD,GAAG,CAACpD,uBAAuB,IAAI,CAAA,CAAE,CAAA;QAC3D,IACEoD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;YACAD,uBAAuB,CAACC,YAAY,GAClCmD,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAA;QAC5C,CAAA;QAEA,IACEmD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;YACAL,uBAAuB,CAACK,kBAAkB,GACxC+C,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAA;QAClD,CAAA;QAEA,IACE+C,GAAG,CAACpD,uBAAuB,IAC3B,OAAOoD,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;YACAN,uBAAuB,CAACM,8BAA8B,GACpD8C,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,CAAA;QAC9D,CAAA;QAEA,IAAIO,kBAAkB,EAAE;YACtBH,eAAe,GAAG,KAAK,CAAA;QACzB,CAAA;QAEA,IAAIS,mBAAmB,EAAE;YACvBD,UAAU,GAAG,IAAI,CAAA;QACnB,CAAA;QAEA,sBAAA,GACA,IAAIQ,YAAY,EAAE;YAChBhC,YAAY,GAAGtH,QAAQ,CAAC,CAAA,CAAE,EAAEwH,IAAS,CAAC,CAAA;YACtCC,YAAY,GAAG,EAAE,CAAA;YACjB,IAAI6B,YAAY,CAAChI,IAAI,KAAK,IAAI,EAAE;gBAC9BtB,QAAQ,CAACsH,YAAY,EAAEE,MAAS,CAAC,CAAA;gBACjCxH,QAAQ,CAACyH,YAAY,EAAEE,IAAU,CAAC,CAAA;YACpC,CAAA;YAEA,IAAI2B,YAAY,CAAC/H,GAAG,KAAK,IAAI,EAAE;gBAC7BvB,QAAQ,CAACsH,YAAY,EAAEE,KAAQ,CAAC,CAAA;gBAChCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC9H,UAAU,KAAK,IAAI,EAAE;gBACpCxB,QAAQ,CAACsH,YAAY,EAAEE,UAAe,CAAC,CAAA;gBACvCxH,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACjC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;YAEA,IAAI2B,YAAY,CAAC5H,MAAM,KAAK,IAAI,EAAE;gBAChC1B,QAAQ,CAACsH,YAAY,EAAEE,QAAW,CAAC,CAAA;gBACnCxH,QAAQ,CAACyH,YAAY,EAAEE,MAAY,CAAC,CAAA;gBACpC3H,QAAQ,CAACyH,YAAY,EAAEE,GAAS,CAAC,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,kCAAA,GACA,IAAIqD,GAAG,CAACI,QAAQ,EAAE;YAChB,IAAI9D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG5G,KAAK,CAAC4G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAtH,QAAQ,CAACsH,YAAY,EAAE0D,GAAG,CAACI,QAAQ,EAAEjL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACK,QAAQ,EAAE;YAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;gBACzCD,YAAY,GAAG/G,KAAK,CAAC+G,YAAY,CAAC,CAAA;YACpC,CAAA;YAEAzH,QAAQ,CAACyH,YAAY,EAAEuD,GAAG,CAACK,QAAQ,EAAElL,iBAAiB,CAAC,CAAA;QACzD,CAAA;QAEA,IAAI6K,GAAG,CAACC,iBAAiB,EAAE;YACzBjL,QAAQ,CAAC2J,mBAAmB,EAAEqB,GAAG,CAACC,iBAAiB,EAAE9K,iBAAiB,CAAC,CAAA;QACzE,CAAA;QAEA,IAAI6K,GAAG,CAACzB,eAAe,EAAE;YACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;gBAC/CD,eAAe,GAAG7I,KAAK,CAAC6I,eAAe,CAAC,CAAA;YAC1C,CAAA;YAEAvJ,QAAQ,CAACuJ,eAAe,EAAEyB,GAAG,CAACzB,eAAe,EAAEpJ,iBAAiB,CAAC,CAAA;QACnE,CAAA;QAEA,iDAAA,GACA,IAAIiJ,YAAY,EAAE;YAChB9B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QAC9B,CAAA;QAEA,0EAAA,GACA,IAAIqB,cAAc,EAAE;YAClB3I,QAAQ,CAACsH,YAAY,EAAE;gBAAC,MAAM;gBAAE,MAAM;gBAAE,MAAM;aAAC,CAAC,CAAA;QAClD,CAAA;QAEA,0EAAA,GACA,IAAIA,YAAY,CAACgE,KAAK,EAAE;YACtBtL,QAAQ,CAACsH,YAAY,EAAE;gBAAC,OAAO;aAAC,CAAC,CAAA;YACjC,OAAOa,WAAW,CAACoD,KAAK,CAAA;QAC1B,CAAA;QAEA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;YAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAAC1H,UAAU,KAAK,UAAU,EAAE;gBAC7D,MAAM1E,eAAe,CACnB,6EAA6E,CAC9E,CAAA;YACH,CAAA;YAEA,IAAI,OAAO4L,GAAG,CAACQ,oBAAoB,CAACzH,eAAe,KAAK,UAAU,EAAE;gBAClE,MAAM3E,eAAe,CACnB,kFAAkF,CACnF,CAAA;YACH,CAAA;YAEA,0CAAA;YACAwH,kBAAkB,GAAGoE,GAAG,CAACQ,oBAAoB,CAAA;YAE7C,+CAAA;YACA3E,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;QAC/C,CAAC,MAAM;YACL,6EAAA;YACA,IAAI8C,kBAAkB,KAAK7B,SAAS,EAAE;gBACpC6B,kBAAkB,GAAGvD,yBAAyB,CAC5CC,YAAY,EACZmC,aAAa,CACd,CAAA;YACH,CAAA;YAEA,qEAAA;YACA,IAAImB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;gBAChEA,SAAS,GAAGD,kBAAkB,CAAC9C,UAAU,CAAC,EAAE,CAAC,CAAA;YAC/C,CAAA;QACF,CAAA;QAEA,iDAAA;QACA,uCAAA;QACA,IAAIpH,MAAM,EAAE;YACVA,MAAM,CAACsO,GAAG,CAAC,CAAA;QACb,CAAA;QAEAN,MAAM,GAAGM,GAAG,CAAA;KACb,CAAA;IAED;;gBAEgB,GAChB,MAAMS,YAAY,GAAGzL,QAAQ,CAAC,CAAA,CAAE,EAAE,CAChC;WAAGwH,KAAQ,EACX;WAAGA,UAAe,EAClB;WAAGA,aAAkB;KACtB,CAAC,CAAA;IACF,MAAMkE,eAAe,GAAG1L,QAAQ,CAAC,CAAA,CAAE,EAAE,CACnC;WAAGwH,QAAW,EACd;WAAGA,gBAAqB;KACzB,CAAC,CAAA;IAEF;;;;;GAKG,GACH,MAAMmE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAatL,OAAgB,EAAA;QACrD,IAAIuL,MAAM,GAAGrF,aAAa,CAAClG,OAAO,CAAC,CAAA;QAEnC,wDAAA;QACA,qDAAA;QACA,IAAI,CAACuL,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;YAC9BD,MAAM,GAAG;gBACPE,YAAY,EAAE9B,SAAS;gBACvB6B,OAAO,EAAE,UAAA;aACV,CAAA;QACH,CAAA;QAEA,MAAMA,OAAO,GAAG3N,iBAAiB,CAACmC,OAAO,CAACwL,OAAO,CAAC,CAAA;QAClD,MAAME,aAAa,GAAG7N,iBAAiB,CAAC0N,MAAM,CAACC,OAAO,CAAC,CAAA;QAEvD,IAAI,CAAC3B,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAA;QACd,CAAA;QAEA,IAAIzL,OAAO,CAACyL,YAAY,KAAKhC,aAAa,EAAE;YAC1C,oDAAA;YACA,sDAAA;YACA,uBAAA;YACA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,KAAK,CAAA;YAC1B,CAAA;YAEA,oDAAA;YACA,qDAAA;YACA,2BAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;gBAC5C,OACEgC,OAAO,KAAK,KAAK,IAAA,CAChBE,aAAa,KAAK,gBAAgB,IACjC3B,8BAA8B,CAAC2B,aAAa,CAAC,CAAC,CAAA;YAEpD,CAAA;YAEA,iDAAA;YACA,oDAAA;YACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QACvC,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAKjC,gBAAgB,EAAE;YAC7C,uDAAA;YACA,uDAAA;YACA,uBAAA;YACA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;gBAC1C,OAAO8B,OAAO,KAAK,MAAM,CAAA;YAC3B,CAAA;YAEA,mDAAA;YACA,qCAAA;YACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;gBACzC,OAAO+B,OAAO,KAAK,MAAM,IAAIxB,uBAAuB,CAAC0B,aAAa,CAAC,CAAA;YACrE,CAAA;YAEA,oDAAA;YACA,uDAAA;YACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;QAC1C,CAAA;QAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAK/B,cAAc,EAAE;YAC3C,iDAAA;YACA,mDAAA;YACA,wCAAA;YACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACO,uBAAuB,CAAC0B,aAAa,CAAC,EACvC;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACO,8BAA8B,CAAC2B,aAAa,CAAC,EAC9C;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,gDAAA;YACA,mDAAA;YACA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,IAAA,CACxBvB,4BAA4B,CAACuB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;QAErE,CAAA;QAEA,6DAAA;QACA,IACEtB,iBAAiB,KAAK,uBAAuB,IAC7CL,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EACxC;YACA,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,qDAAA;QACA,qDAAA;QACA,wDAAA;QACA,6BAAA;QACA,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;GAIG,GACH,MAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAU,EAAA;QACvCpO,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;YAAE/E,OAAO,EAAE6L,IAAAA;QAAM,CAAA,CAAC,CAAA;QAE/C,IAAI;YACF,0DAAA;YACA3F,aAAa,CAAC2F,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC,CAAA;SACtC,CAAC,OAAOjI,CAAC,EAAE;YACVmC,MAAM,CAAC8F,IAAI,CAAC,CAAA;QACd,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAY,EAAEhM,OAAgB,EAAA;QAC/D,IAAI;YACFvC,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAEpC,OAAO,CAACiM,gBAAgB,CAACD,IAAI,CAAC;gBACzCE,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;SACH,CAAC,OAAO4D,CAAC,EAAE;YACVnG,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;gBAC3B3C,SAAS,EAAE,IAAI;gBACf8J,IAAI,EAAElM,OAAAA;YACP,CAAA,CAAC,CAAA;QACJ,CAAA;QAEAA,OAAO,CAACmM,eAAe,CAACH,IAAI,CAAC,CAAA;QAE7B,2DAAA;QACA,IAAIA,IAAI,KAAK,IAAI,EAAE;YACjB,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;gBACrC,IAAI;oBACFkD,YAAY,CAAC5L,OAAO,CAAC,CAAA;gBACvB,CAAC,CAAC,OAAO4D,CAAC,EAAE,CAAA,CAAC;YACf,CAAC,MAAM;gBACL,IAAI;oBACF5D,OAAO,CAACoM,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC,CAAA;gBAChC,CAAC,CAAC,OAAOpI,CAAC,EAAE,CAAA,CAAC;YACf,CAAA;QACF,CAAA;KACD,CAAA;IAED;;;;;GAKG,GACH,MAAMyI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAa,EAAA;QAC3C,0BAAA,GACA,IAAIC,GAAG,GAAG,IAAI,CAAA;QACd,IAAIC,iBAAiB,GAAG,IAAI,CAAA;QAE5B,IAAIhE,UAAU,EAAE;YACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;QACrC,CAAC,MAAM;YACL,+EAAA,GACA,MAAMG,OAAO,GAAGvO,WAAW,CAACoO,KAAK,EAAE,aAAa,CAAC,CAAA;YACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;QAC3C,CAAA;QAEA,IACEvC,iBAAiB,KAAK,uBAAuB,IAC7CP,SAAS,KAAKD,cAAc,EAC5B;YACA,4GAAA;YACA4C,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;QACpB,CAAA;QAEA,MAAMI,YAAY,GAAGnG,kBAAkB,GACnCA,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;QACT;;;KAGG,GACH,IAAI3C,SAAS,KAAKD,cAAc,EAAE;YAChC,IAAI;gBACF6C,GAAG,GAAG,IAAI3G,SAAS,EAAE,CAAC+G,eAAe,CAACD,YAAY,EAAExC,iBAAiB,CAAC,CAAA;YACxE,CAAC,CAAC,OAAOtG,CAAC,EAAE,CAAA,CAAC;QACf,CAAA;QAEA,6DAAA,GACA,IAAI,CAAC2I,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;YAChCL,GAAG,GAAG9F,cAAc,CAACoG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAChE,IAAI;gBACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAC1CpD,SAAS,GACTkG,YAAY,CAAA;aACjB,CAAC,OAAO9I,CAAC,EAAE;YACV,8CAAA;YAAA,CAAA;QAEJ,CAAA;QAEA,MAAMmJ,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;QAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;YAC9BO,IAAI,CAACC,YAAY,CACftK,QAAQ,CAACuK,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAC3B,CAAA;QACH,CAAA;QAEA,2CAAA,GACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;YAChC,OAAO9C,oBAAoB,CAACuG,IAAI,CAC9BZ,GAAG,EACHjE,cAAc,GAAG,MAAM,GAAG,MAAM,CACjC,CAAC,CAAC,CAAC,CAAA;QACN,CAAA;QAEA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;KACnD,CAAA;IAED;;;;;GAKG,GACH,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAaxI,IAAU,EAAA;QAC9C,OAAO8B,kBAAkB,CAACyG,IAAI,CAC5BvI,IAAI,CAAC0B,aAAa,IAAI1B,IAAI,EAC1BA,IAAI,EACJ,sCAAA;QACAY,UAAU,CAAC6H,YAAY,GACrB7H,UAAU,CAAC8H,YAAY,GACvB9H,UAAU,CAAC+H,SAAS,GACpB/H,UAAU,CAACgI,2BAA2B,GACtChI,UAAU,CAACiI,kBAAkB,EAC/B,IAAI,CACL,CAAA;KACF,CAAA;IAED;;;;;GAKG,GACH,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAa1N,OAAgB,EAAA;QAC7C,OACEA,OAAO,YAAY2F,eAAe,IAAA,CACjC,OAAO3F,OAAO,CAAC2N,QAAQ,KAAK,QAAQ,IACnC,OAAO3N,OAAO,CAAC4N,WAAW,KAAK,QAAQ,IACvC,OAAO5N,OAAO,CAAC8L,WAAW,KAAK,UAAU,IACzC,CAAA,CAAE9L,OAAO,CAAC6N,UAAU,YAAYpI,YAAY,CAAC,IAC7C,OAAOzF,OAAO,CAACmM,eAAe,KAAK,UAAU,IAC7C,OAAOnM,OAAO,CAACoM,YAAY,KAAK,UAAU,IAC1C,OAAOpM,OAAO,CAACyL,YAAY,KAAK,QAAQ,IACxC,OAAOzL,OAAO,CAACgN,YAAY,KAAK,UAAU,IAC1C,OAAOhN,OAAO,CAAC8N,aAAa,KAAK,UAAU,CAAC,CAAA;KAEjD,CAAA;IAED;;;;;GAKG,GACH,MAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAatN,KAAc,EAAA;QACtC,OAAO,OAAO8E,IAAI,KAAK,UAAU,IAAI9E,KAAK,YAAY8E,IAAI,CAAA;KAC3D,CAAA;IAED,SAASyI,aAAaA,CAOpBlH,KAAU,EAAEmH,WAA6B,EAAEC,IAAsB,EAAA;QACjElR,YAAY,CAAC8J,KAAK,GAAGqH,IAAI,IAAI;YAC3BA,IAAI,CAAChB,IAAI,CAACxI,SAAS,EAAEsJ,WAAW,EAAEC,IAAI,EAAE7D,MAAM,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACJ,CAAA;IAEA;;;;;;;;GAQG,GACH,MAAM+D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAgB,EAAA;QAClD,IAAI5H,OAAO,GAAG,IAAI,CAAA;QAElB,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC1C,sBAAsB,EAAE6J,WAAW,EAAE,IAAI,CAAC,CAAA;QAE9D,gDAAA,GACA,IAAIP,YAAY,CAACO,WAAW,CAAC,EAAE;YAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,MAAMzC,OAAO,GAAG1L,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;QAEvD,6BAAA,GACAK,aAAa,CAAClH,KAAK,CAACvC,mBAAmB,EAAE0J,WAAW,EAAE;YACpDzC,OAAO;YACP6C,WAAW,EAAEpH,YAAAA;QACd,CAAA,CAAC,CAAA;QAEF,oDAAA,GACA,IACEoB,YAAY,IACZ4F,WAAW,CAACH,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACE,WAAW,CAACK,iBAAiB,CAAC,IACvC1P,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACnB,SAAS,CAAC,IAC7ClO,UAAU,CAAC,UAAU,EAAEqP,WAAW,CAACL,WAAW,CAAC,EAC/C;YACAhC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,oDAAA,GACA,IAAIA,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACK,sBAAsB,EAAE;YAC7DoJ,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IACE5F,YAAY,IACZ4F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACM,OAAO,IAC1C7D,UAAU,CAAC,SAAS,EAAEqP,WAAW,CAACC,IAAI,CAAC,EACvC;YACAtC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,mDAAA,GACA,IAAI,CAAChH,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;YAClD,+CAAA,GACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAI+C,qBAAqB,CAAC/C,OAAO,CAAC,EAAE;gBAC3D,IACEjE,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EACzD;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;gBAEA,IACEjE,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACxDlD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAC7C;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;YACF,CAAA;YAEA,+CAAA,GACA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;gBAC7C,MAAMgD,UAAU,GAAGtI,aAAa,CAAC+H,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU,CAAA;gBACvE,MAAMtB,UAAU,GAAGjH,aAAa,CAACgI,WAAW,CAAC,IAAIA,WAAW,CAACf,UAAU,CAAA;gBAEvE,IAAIA,UAAU,IAAIsB,UAAU,EAAE;oBAC5B,MAAMC,UAAU,GAAGvB,UAAU,CAAC3N,MAAM,CAAA;oBAEpC,IAAK,IAAImP,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,CAAE;wBACxC,MAAMC,UAAU,GAAG7I,SAAS,CAACoH,UAAU,CAACwB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;wBACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;wBACjEJ,UAAU,CAACxB,YAAY,CAAC2B,UAAU,EAAE3I,cAAc,CAACiI,WAAW,CAAC,CAAC,CAAA;oBAClE,CAAA;gBACF,CAAA;YACF,CAAA;YAEArC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,+CAAA,GACA,IAAIA,WAAW,YAAYhJ,OAAO,IAAI,CAACqG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;YACxErC,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,6DAAA,GACA,IACE,CAACzC,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxB5M,UAAU,CAAC,6BAA6B,EAAEqP,WAAW,CAACnB,SAAS,CAAC,EAChE;YACAlB,YAAY,CAACqC,WAAW,CAAC,CAAA;YACzB,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,gDAAA,GACA,IAAI7F,kBAAkB,IAAI6F,WAAW,CAACjJ,QAAQ,KAAK7C,SAAS,CAACZ,IAAI,EAAE;YACjE,kCAAA,GACA8E,OAAO,GAAG4H,WAAW,CAACL,WAAW,CAAA;YAEjC5Q,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5DxI,OAAO,GAAGjI,aAAa,CAACiI,OAAO,EAAEwI,IAAI,EAAE,GAAG,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;YAEF,IAAIZ,WAAW,CAACL,WAAW,KAAKvH,OAAO,EAAE;gBACvC5I,SAAS,CAACkH,SAAS,CAACI,OAAO,EAAE;oBAAE/E,OAAO,EAAEiO,WAAW,CAACnI,SAAS,EAAE;gBAAA,CAAE,CAAC,CAAA;gBAClEmI,WAAW,CAACL,WAAW,GAAGvH,OAAO,CAAA;YACnC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC7C,qBAAqB,EAAEgK,WAAW,EAAE,IAAI,CAAC,CAAA;QAE7D,OAAO,KAAK,CAAA;KACb,CAAA;IAED;;;;;;;GAOG,GACH,sCAAA;IACA,MAAMa,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,KAAa,EACbC,MAAc,EACdvO,KAAa,EAAA;QAEb,sCAAA,GACA,IACEmI,YAAY,IAAA,CACXoG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,IAAA,CACrCvO,KAAK,IAAIiC,QAAQ,IAAIjC,KAAK,IAAI6J,WAAW,CAAC,EAC3C;YACA,OAAO,KAAK,CAAA;QACd,CAAA;QAEA;;;gEAG8D,GAC9D,IACErC,eAAe,IACf,CAACF,WAAW,CAACiH,MAAM,CAAC,IACpBpQ,UAAU,CAACgD,SAAS,EAAEoN,MAAM,CAAC,EAC7B,CAED;aAAM,IAAIhH,eAAe,IAAIpJ,UAAU,CAACiD,SAAS,EAAEmN,MAAM,CAAC,EAAE,CAG5D;aAAM,IAAI,CAAC5H,YAAY,CAAC4H,MAAM,CAAC,IAAIjH,WAAW,CAACiH,MAAM,CAAC,EAAE;YACvD,IACE,kGAAA;YACA,qGAAA;YACA,sHAAA;YACCT,qBAAqB,CAACQ,KAAK,CAAC,IAAA,CACzBxH,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAEuH,KAAK,CAAC,IACtDxH,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAACuH,KAAK,CAAE,CAAC,IAAA,CAC/CxH,uBAAuB,CAACK,kBAAkB,YAAY/I,MAAM,IAC5DD,UAAU,CAAC2I,uBAAuB,CAACK,kBAAkB,EAAEoH,MAAM,CAAC,IAC7DzH,uBAAuB,CAACK,kBAAkB,YAAY6C,QAAQ,IAC7DlD,uBAAuB,CAACK,kBAAkB,CAACoH,MAAM,CAAE,CAAC,IAC1D,sEAAA;YACA,6FAAA;YACCA,MAAM,KAAK,IAAI,IACdzH,uBAAuB,CAACM,8BAA8B,IAAA,CACpDN,uBAAuB,CAACC,YAAY,YAAY3I,MAAM,IACtDD,UAAU,CAAC2I,uBAAuB,CAACC,YAAY,EAAE/G,KAAK,CAAC,IACtD8G,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAAC/G,KAAK,CAAE,CAAE,EACpD,CAGD;iBAAM;gBACL,OAAO,KAAK,CAAA;YACd,CAAA;QACA,6DAAA,GACF,CAAC,MAAM,IAAI6I,mBAAmB,CAAC0F,MAAM,CAAC,EAAE,CAIvC;aAAM,IACLpQ,UAAU,CAACkD,gBAAc,EAAE1D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID;aAAM,IACL,CAACgN,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBzQ,aAAa,CAACmC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnC2I,aAAa,CAAC2F,KAAK,CAAC,EACpB,CAKD;aAAM,IACL7G,uBAAuB,IACvB,CAACtJ,UAAU,CAACmD,iBAAiB,EAAE3D,aAAa,CAACqC,KAAK,EAAEuB,eAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD;aAAM,IAAIvB,KAAK,EAAE;YAChB,OAAO,KAAK,CAAA;QACd,CAAC,MAAM,CAEL;QAGF,OAAO,IAAI,CAAA;KACZ,CAAA;IAED;;;;;;;GAOG,GACH,MAAM8N,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAa/C,OAAe,EAAA;QACrD,OAAOA,OAAO,KAAK,gBAAgB,IAAItN,WAAW,CAACsN,OAAO,EAAEtJ,cAAc,CAAC,CAAA;KAC5E,CAAA;IAED;;;;;;;;;GASG,GACH,MAAM+M,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAahB,WAAoB,EAAA;QACxD,6BAAA,GACAD,aAAa,CAAClH,KAAK,CAAC3C,wBAAwB,EAAE8J,WAAW,EAAE,IAAI,CAAC,CAAA;QAEhE,MAAM,EAAEJ,UAAAA,EAAY,GAAGI,WAAW,CAAA;QAElC,iEAAA,GACA,IAAI,CAACJ,UAAU,IAAIH,YAAY,CAACO,WAAW,CAAC,EAAE;YAC5C,OAAA;QACF,CAAA;QAEA,MAAMiB,SAAS,GAAG;YAChBC,QAAQ,EAAE,EAAE;YACZC,SAAS,EAAE,EAAE;YACbC,QAAQ,EAAE,IAAI;YACdC,iBAAiB,EAAElI,YAAY;YAC/BmI,aAAa,EAAE7K,SAAAA;SAChB,CAAA;QACD,IAAI3E,CAAC,GAAG8N,UAAU,CAACtO,MAAM,CAAA;QAEzB,4DAAA,GACA,MAAOQ,CAAC,EAAE,CAAE;YACV,MAAMyP,IAAI,GAAG3B,UAAU,CAAC9N,CAAC,CAAC,CAAA;YAC1B,MAAM,EAAEiM,IAAI,EAAEP,YAAY,EAAEhL,KAAK,EAAE2O,SAAAA,EAAW,GAAGI,IAAI,CAAA;YACrD,MAAMR,MAAM,GAAGlP,iBAAiB,CAACkM,IAAI,CAAC,CAAA;YAEtC,MAAMyD,SAAS,GAAGL,SAAS,CAAA;YAC3B,IAAI3O,KAAK,GAAGuL,IAAI,KAAK,OAAO,GAAGyD,SAAS,GAAGjR,UAAU,CAACiR,SAAS,CAAC,CAAA;YAEhE,6BAAA,GACAP,SAAS,CAACC,QAAQ,GAAGH,MAAM,CAAA;YAC3BE,SAAS,CAACE,SAAS,GAAG3O,KAAK,CAAA;YAC3ByO,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;YACzBH,SAAS,CAACK,aAAa,GAAG7K,SAAS,CAAC,CAAA,2DAAA;YACpCsJ,aAAa,CAAClH,KAAK,CAACxC,qBAAqB,EAAE2J,WAAW,EAAEiB,SAAS,CAAC,CAAA;YAClEzO,KAAK,GAAGyO,SAAS,CAACE,SAAS,CAAA;YAE3B;;OAEG,GACH,IAAIvG,oBAAoB,IAAA,CAAKmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;gBAClE,uCAAA;gBACAjD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBAEnC,8EAAA;gBACAxN,KAAK,GAAGqI,2BAA2B,GAAGrI,KAAK,CAAA;YAC7C,CAAA;YAEA,gEAAA,GACA,IAAI4H,YAAY,IAAIzJ,UAAU,CAAC,+BAA+B,EAAE6B,KAAK,CAAC,EAAE;gBACtEsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,2CAAA,GACA,IAAIiB,SAAS,CAACK,aAAa,EAAE;gBAC3B,SAAA;YACF,CAAA;YAEA,2CAAA,GACA,IAAI,CAACL,SAAS,CAACG,QAAQ,EAAE;gBACvBtD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,8CAAA,GACA,IAAI,CAAC9F,wBAAwB,IAAIvJ,UAAU,CAAC,MAAM,EAAE6B,KAAK,CAAC,EAAE;gBAC1DsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,kDAAA,GACA,IAAI7F,kBAAkB,EAAE;gBACtBpL,YAAY,CAAC;oBAACyE,aAAa;oBAAEC,QAAQ;oBAAEC,WAAW;iBAAC,GAAGkN,IAAI,IAAI;oBAC5DpO,KAAK,GAAGrC,aAAa,CAACqC,KAAK,EAAEoO,IAAI,EAAE,GAAG,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;YACJ,CAAA;YAEA,wCAAA,GACA,MAAME,KAAK,GAAGjP,iBAAiB,CAACmO,WAAW,CAACN,QAAQ,CAAC,CAAA;YACrD,IAAI,CAACmB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,EAAE;gBAC5CsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACnC,SAAA;YACF,CAAA;YAEA,gDAAA,GACA,IACE1H,kBAAkB,IAClB,OAAOtD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACyM,gBAAgB,KAAK,UAAU,EACnD;gBACA,IAAIjE,YAAY,EAAE,CAEjB;qBAAM;oBACL,OAAQxI,YAAY,CAACyM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;wBAClD,KAAK,aAAa;4BAAE;gCAClBvO,KAAK,GAAG8F,kBAAkB,CAAC9C,UAAU,CAAChD,KAAK,CAAC,CAAA;gCAC5C,MAAA;4BACF,CAAA;wBAEA,KAAK,kBAAkB;4BAAE;gCACvBA,KAAK,GAAG8F,kBAAkB,CAAC7C,eAAe,CAACjD,KAAK,CAAC,CAAA;gCACjD,MAAA;4BACF,CAAA;oBAKF,CAAA;gBACF,CAAA;YACF,CAAA;YAEA,0DAAA,GACA,IAAIA,KAAK,KAAKgP,SAAS,EAAE;gBACvB,IAAI;oBACF,IAAIhE,YAAY,EAAE;wBAChBwC,WAAW,CAAC0B,cAAc,CAAClE,YAAY,EAAEO,IAAI,EAAEvL,KAAK,CAAC,CAAA;oBACvD,CAAC,MAAM;wBACL,mFAAA,GACAwN,WAAW,CAAC7B,YAAY,CAACJ,IAAI,EAAEvL,KAAK,CAAC,CAAA;oBACvC,CAAA;oBAEA,IAAIiN,YAAY,CAACO,WAAW,CAAC,EAAE;wBAC7BrC,YAAY,CAACqC,WAAW,CAAC,CAAA;oBAC3B,CAAC,MAAM;wBACL1Q,QAAQ,CAACoH,SAAS,CAACI,OAAO,CAAC,CAAA;oBAC7B,CAAA;iBACD,CAAC,OAAOnB,CAAC,EAAE;oBACVmI,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC,CAAA;gBACrC,CAAA;YACF,CAAA;QACF,CAAA;QAEA,6BAAA,GACAD,aAAa,CAAClH,KAAK,CAAC9C,uBAAuB,EAAEiK,WAAW,EAAE,IAAI,CAAC,CAAA;KAChE,CAAA;IAED;;;;GAIG,GACH,MAAM2B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAA0B,EAAA;QAC7D,IAAIC,UAAU,GAAG,IAAI,CAAA;QACrB,MAAMC,cAAc,GAAG3C,mBAAmB,CAACyC,QAAQ,CAAC,CAAA;QAEpD,6BAAA,GACA7B,aAAa,CAAClH,KAAK,CAACzC,uBAAuB,EAAEwL,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE5D,MAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,CAAG;YAC/C,6BAAA,GACAhC,aAAa,CAAClH,KAAK,CAACtC,sBAAsB,EAAEsL,UAAU,EAAE,IAAI,CAAC,CAAA;YAE7D,8BAAA,GACA1B,iBAAiB,CAAC0B,UAAU,CAAC,CAAA;YAE7B,yBAAA,GACAb,mBAAmB,CAACa,UAAU,CAAC,CAAA;YAE/B,4BAAA,GACA,IAAIA,UAAU,CAACzJ,OAAO,YAAYhB,gBAAgB,EAAE;gBAClDuK,kBAAkB,CAACE,UAAU,CAACzJ,OAAO,CAAC,CAAA;YACxC,CAAA;QACF,CAAA;QAEA,6BAAA,GACA2H,aAAa,CAAClH,KAAK,CAAC5C,sBAAsB,EAAE2L,QAAQ,EAAE,IAAI,CAAC,CAAA;KAC5D,CAAA;IAED,sCAAA;IACAlL,SAAS,CAACsL,QAAQ,GAAG,SAAU3D,KAAK,EAAU;QAAA,IAAR3B,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QAC5C,IAAIyN,IAAI,GAAG,IAAI,CAAA;QACf,IAAImD,YAAY,GAAG,IAAI,CAAA;QACvB,IAAIjC,WAAW,GAAG,IAAI,CAAA;QACtB,IAAIkC,UAAU,GAAG,IAAI,CAAA;QACrB;;+DAE6D,GAC7DvG,cAAc,GAAG,CAAC0C,KAAK,CAAA;QACvB,IAAI1C,cAAc,EAAE;YAClB0C,KAAK,GAAG,OAAO,CAAA;QACjB,CAAA;QAEA,yCAAA,GACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACyB,OAAO,CAACzB,KAAK,CAAC,EAAE;YAChD,IAAI,OAAOA,KAAK,CAACrO,QAAQ,KAAK,UAAU,EAAE;gBACxCqO,KAAK,GAAGA,KAAK,CAACrO,QAAQ,EAAE,CAAA;gBACxB,IAAI,OAAOqO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,MAAMvN,eAAe,CAAC,iCAAiC,CAAC,CAAA;gBAC1D,CAAA;YACF,CAAC,MAAM;gBACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;YACrD,CAAA;QACF,CAAA;QAEA,6CAAA,GACA,IAAI,CAAC4F,SAAS,CAACO,WAAW,EAAE;YAC1B,OAAOoH,KAAK,CAAA;QACd,CAAA;QAEA,sBAAA,GACA,IAAI,CAAC/D,UAAU,EAAE;YACfmC,YAAY,CAACC,GAAG,CAAC,CAAA;QACnB,CAAA;QAEA,6BAAA,GACAhG,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;QAEtB,kDAAA,GACA,IAAI,OAAOuH,KAAK,KAAK,QAAQ,EAAE;YAC7BtD,QAAQ,GAAG,KAAK,CAAA;QAClB,CAAA;QAEA,IAAIA,QAAQ,EAAE;YACZ,6DAAA,GACA,IAAKsD,KAAc,CAACqB,QAAQ,EAAE;gBAC5B,MAAMnC,OAAO,GAAG1L,iBAAiB,CAAEwM,KAAc,CAACqB,QAAQ,CAAC,CAAA;gBAC3D,IAAI,CAAC1G,YAAY,CAACuE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;oBAClD,MAAMzM,eAAe,CACnB,yDAAyD,CAC1D,CAAA;gBACH,CAAA;YACF,CAAA;QACF,CAAC,MAAM,IAAIuN,KAAK,YAAY/G,IAAI,EAAE;YAChC;+CAC2C,GAC3CwH,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;YAC/B6D,YAAY,GAAGnD,IAAI,CAACzG,aAAa,CAACO,UAAU,CAACyF,KAAK,EAAE,IAAI,CAAC,CAAA;YACzD,IACE4D,YAAY,CAAClL,QAAQ,KAAK7C,SAAS,CAACnC,OAAO,IAC3CkQ,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAChC;gBACA,qCAAA,GACAZ,IAAI,GAAGmD,YAAY,CAAA;YACrB,CAAC,MAAM,IAAIA,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;gBAC3CZ,IAAI,GAAGmD,YAAY,CAAA;YACrB,CAAC,MAAM;gBACL,0DAAA;gBACAnD,IAAI,CAACqD,WAAW,CAACF,YAAY,CAAC,CAAA;YAChC,CAAA;QACF,CAAC,MAAM;YACL,0CAAA,GACA,IACE,CAACzH,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc,IACf,mDAAA;YACAgE,KAAK,CAAC/N,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;gBACA,OAAOgI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAAC6I,KAAK,CAAC,GACpCA,KAAK,CAAA;YACX,CAAA;YAEA,sCAAA,GACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;YAE3B,0CAAA,GACA,IAAI,CAACS,IAAI,EAAE;gBACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGnC,SAAS,GAAG,EAAE,CAAA;YACjE,CAAA;QACF,CAAA;QAEA,yDAAA,GACA,IAAIuG,IAAI,IAAIvE,UAAU,EAAE;YACtBoD,YAAY,CAACmB,IAAI,CAACsD,UAAU,CAAC,CAAA;QAC/B,CAAA;QAEA,qBAAA,GACA,MAAMC,YAAY,GAAGlD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC,CAAA;QAEjE,iDAAA,GACA,MAAQkB,WAAW,GAAGqC,YAAY,CAACN,QAAQ,EAAE,CAAG;YAC9C,8BAAA,GACA5B,iBAAiB,CAACH,WAAW,CAAC,CAAA;YAE9B,yBAAA,GACAgB,mBAAmB,CAAChB,WAAW,CAAC,CAAA;YAEhC,oCAAA,GACA,IAAIA,WAAW,CAAC5H,OAAO,YAAYhB,gBAAgB,EAAE;gBACnDuK,kBAAkB,CAAC3B,WAAW,CAAC5H,OAAO,CAAC,CAAA;YACzC,CAAA;QACF,CAAA;QAEA,gDAAA,GACA,IAAI2C,QAAQ,EAAE;YACZ,OAAOsD,KAAK,CAAA;QACd,CAAA;QAEA,kCAAA,GACA,IAAI7D,UAAU,EAAE;YACd,IAAIC,mBAAmB,EAAE;gBACvByH,UAAU,GAAGxJ,sBAAsB,CAACwG,IAAI,CAACJ,IAAI,CAACzG,aAAa,CAAC,CAAA;gBAE5D,MAAOyG,IAAI,CAACsD,UAAU,CAAE;oBACtB,0DAAA;oBACAF,UAAU,CAACC,WAAW,CAACrD,IAAI,CAACsD,UAAU,CAAC,CAAA;gBACzC,CAAA;YACF,CAAC,MAAM;gBACLF,UAAU,GAAGpD,IAAI,CAAA;YACnB,CAAA;YAEA,IAAI3F,YAAY,CAACmJ,UAAU,IAAInJ,YAAY,CAACoJ,cAAc,EAAE;gBAC1D;;;;;;QAME,GACFL,UAAU,GAAGtJ,UAAU,CAACsG,IAAI,CAAChI,gBAAgB,EAAEgL,UAAU,EAAE,IAAI,CAAC,CAAA;YAClE,CAAA;YAEA,OAAOA,UAAU,CAAA;QACnB,CAAA;QAEA,IAAIM,cAAc,GAAGnI,cAAc,GAAGyE,IAAI,CAAC2D,SAAS,GAAG3D,IAAI,CAACD,SAAS,CAAA;QAErE,gCAAA,GACA,IACExE,cAAc,IACdrB,YAAY,CAAC,UAAU,CAAC,IACxB8F,IAAI,CAACzG,aAAa,IAClByG,IAAI,CAACzG,aAAa,CAACqK,OAAO,IAC1B5D,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,IAC/BpN,UAAU,CAACoI,YAAwB,EAAE+F,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,CAAC,EACrE;YACAyE,cAAc,GACZ,YAAY,GAAG1D,IAAI,CAACzG,aAAa,CAACqK,OAAO,CAAC3E,IAAI,GAAG,KAAK,GAAGyE,cAAc,CAAA;QAC3E,CAAA;QAEA,uCAAA,GACA,IAAIrI,kBAAkB,EAAE;YACtBpL,YAAY,CAAC;gBAACyE,aAAa;gBAAEC,QAAQ;gBAAEC,WAAW;aAAC,GAAGkN,IAAI,IAAI;gBAC5D4B,cAAc,GAAGrS,aAAa,CAACqS,cAAc,EAAE5B,IAAI,EAAE,GAAG,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;QACJ,CAAA;QAEA,OAAOtI,kBAAkB,IAAIoC,mBAAmB,GAC5CpC,kBAAkB,CAAC9C,UAAU,CAACgN,cAAc,CAAC,GAC7CA,cAAc,CAAA;KACnB,CAAA;IAED9L,SAAS,CAACiM,SAAS,GAAG,YAAkB;QAAA,IAARjG,GAAG,GAAArL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAoF,SAAA,GAAApF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;QACtCoL,YAAY,CAACC,GAAG,CAAC,CAAA;QACjBpC,UAAU,GAAG,IAAI,CAAA;KAClB,CAAA;IAED5D,SAAS,CAACkM,WAAW,GAAG,YAAA;QACtBxG,MAAM,GAAG,IAAI,CAAA;QACb9B,UAAU,GAAG,KAAK,CAAA;KACnB,CAAA;IAED5D,SAAS,CAACmM,gBAAgB,GAAG,SAAUC,GAAG,EAAEvB,IAAI,EAAE/O,KAAK,EAAA;QACrD,+CAAA,GACA,IAAI,CAAC4J,MAAM,EAAE;YACXK,YAAY,CAAC,CAAA,CAAE,CAAC,CAAA;QAClB,CAAA;QAEA,MAAMqE,KAAK,GAAGjP,iBAAiB,CAACiR,GAAG,CAAC,CAAA;QACpC,MAAM/B,MAAM,GAAGlP,iBAAiB,CAAC0P,IAAI,CAAC,CAAA;QACtC,OAAOV,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,CAAA;KAC/C,CAAA;IAEDkE,SAAS,CAACqM,OAAO,GAAG,SAAUC,UAAU,EAAEC,YAAY,EAAA;QACpD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;YACtC,OAAA;QACF,CAAA;QAEAzT,SAAS,CAACqJ,KAAK,CAACmK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;KAC3C,CAAA;IAEDvM,SAAS,CAACwM,UAAU,GAAG,SAAUF,UAAU,EAAEC,YAAY,EAAA;QACvD,IAAIA,YAAY,KAAKxM,SAAS,EAAE;YAC9B,MAAMvE,KAAK,GAAG9C,gBAAgB,CAACyJ,KAAK,CAACmK,UAAU,CAAC,EAAEC,YAAY,CAAC,CAAA;YAE/D,OAAO/Q,KAAK,KAAK,CAAC,CAAC,GACfuE,SAAS,GACT/G,WAAW,CAACmJ,KAAK,CAACmK,UAAU,CAAC,EAAE9Q,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjD,CAAA;QAEA,OAAO5C,QAAQ,CAACuJ,KAAK,CAACmK,UAAU,CAAC,CAAC,CAAA;KACnC,CAAA;IAEDtM,SAAS,CAACyM,WAAW,GAAG,SAAUH,UAAU,EAAA;QAC1CnK,KAAK,CAACmK,UAAU,CAAC,GAAG,EAAE,CAAA;KACvB,CAAA;IAEDtM,SAAS,CAAC0M,cAAc,GAAG,YAAA;QACzBvK,KAAK,GAAG/C,eAAe,EAAE,CAAA;KAC1B,CAAA;IAED,OAAOY,SAAS,CAAA;AAClB,CAAA;AAEA,IAAA,SAAeF,eAAe,EAAE", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}]}